* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8fafc;
    color: #1e293b;
    overflow-x: hidden;
    min-height: 100vh;
    width: 100%;
    max-width: 100vw;
}

.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

/* 电脑端顶部导航栏 */
.desktop-top-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    background: #2c3e50;
    color: white;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.desktop-nav-left {
    display: flex;
    align-items: center;
}

.desktop-nav-left .logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 18px;
    font-weight: 600;
}

.desktop-nav-left .logo i {
    font-size: 24px;
    color: #3498db;
}

.desktop-nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.desktop-nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.desktop-nav-item {
    position: relative;
}

.desktop-nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    color: #bdc3c7;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.desktop-nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.desktop-nav-item.active .desktop-nav-link {
    color: white;
    background: #3498db;
}

.desktop-nav-link i {
    font-size: 16px;
}

.desktop-nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.desktop-nav-right .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.desktop-nav-right .user-avatar {
    width: 32px;
    height: 32px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.desktop-nav-right .user-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.desktop-nav-right .user-name {
    font-size: 14px;
    font-weight: 500;
    color: white;
}

.desktop-nav-right .user-role {
    font-size: 12px;
    color: #bdc3c7;
}

.desktop-nav-right .logout-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.desktop-nav-right .logout-btn:hover {
    background: #c0392b;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    color: white;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.sidebar.active {
    transform: translateX(0);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo i {
    font-size: 24px;
    color: #60a5fa;
}

.logo-text {
    font-size: 18px;
    font-weight: 700;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.sidebar-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 4px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active .nav-link {
    background-color: rgba(96, 165, 250, 0.2);
    color: #60a5fa;
    border-left-color: #60a5fa;
}

.nav-link i {
    font-size: 16px;
    width: 20px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.logout-btn {
    width: 100%;
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    color: #f87171;
}

/* 主内容区域 */
.main-content {
    margin-left: 0;
    transition: margin-left 0.3s ease;
    flex: 1;
}

.main-content.sidebar-open {
    margin-left: 280px;
}

/* 响应式设计 */
/* 电脑端 (768px以上) */
@media (min-width: 769px) {
    .app-container {
        flex-direction: column;
    }

    /* 电脑端隐藏侧边栏 */
    .sidebar {
        display: none !important;
    }

    /* 电脑端显示顶部导航栏 */
    .desktop-top-nav {
        display: flex;
    }

    /* 电脑端隐藏汉堡菜单 */
    .hamburger-btn {
        display: none;
    }

    /* 电脑端主内容区域不需要左边距 */
    .main-content {
        margin-left: 0;
    }

    .main-content.sidebar-open {
        margin-left: 0;
    }

    /* 电脑端顶部导航栏调整 */
    .top-header {
        padding: 0 24px;
    }

    .page-title {
        margin: 0;
    }
}

/* 移动端 (768px以下) */
@media (max-width: 768px) {
    .app-container {
        flex-direction: row;
    }

    /* 移动端隐藏电脑端顶部导航栏 */
    .desktop-top-nav {
        display: none !important;
    }

    /* 移动端显示侧边栏 */
    .sidebar {
        display: flex;
    }

    /* 移动端显示汉堡菜单 */
    .hamburger-btn {
        display: block;
    }
}

/* 顶部导航栏 */
.top-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 0 24px;
    height: 48px; /* 从64px减少到48px */
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.hamburger-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.2s;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.hamburger-btn:hover {
    background-color: #f1f5f9;
}

.hamburger-btn span {
    width: 20px;
    height: 2px;
    background-color: #64748b;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.hamburger-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-btn.active span:nth-child(2) {
    opacity: 0;
}

.hamburger-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    color: #64748b;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #f1f5f9;
    color: #1e293b;
}

.badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: #ef4444;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
}

/* 页面内容 */
.page-content {
    padding: 12px 24px 24px 24px; /* 减少顶部padding从24px到12px */
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
}

.content-section {
    display: none;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.content-section.active {
    display: block;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 4px;
}

.stat-label {
    color: #64748b;
    font-size: 14px;
}

/* 鼓励话语区域 */
.motivation-section {
    margin-top: 20px;
}

.motivation-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 30px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
}

.motivation-card:hover {
    transform: translateY(-5px);
}

.motivation-content h3 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
}

.motivation-content p {
    margin: 0;
    font-size: 18px;
    line-height: 1.6;
    opacity: 0.9;
}

.motivation-icon {
    font-size: 48px;
    opacity: 0.8;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.card-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
}

.card-content {
    padding: 24px;
}

/* 产品列表 */
.product-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.product-item:last-child {
    border-bottom: none;
}

.product-info {
    flex: 1;
}

.product-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.product-stats {
    font-size: 12px;
    color: #64748b;
}

.product-revenue {
    font-weight: 600;
    color: #10b981;
}

/* 客户列表 */
.customer-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.customer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.customer-item:last-child {
    border-bottom: none;
}

.customer-info {
    flex: 1;
}

.customer-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
    font-size: 14px;
}

.customer-code {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 2px;
}

.customer-stats {
    font-size: 12px;
    color: #64748b;
}

.customer-revenue {
    font-weight: 600;
    color: #10b981;
}

/* 地区统计 */
.region-stats {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.region-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.region-item:last-child {
    border-bottom: none;
}

.region-info {
    flex: 1;
}

.region-name {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
}

.region-stats {
    font-size: 12px;
    color: #64748b;
}

.region-revenue {
    font-weight: 600;
    color: #10b981;
}

/* 月度趋势 */
.monthly-trend {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.trend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.trend-item:last-child {
    border-bottom: none;
}

.trend-info {
    flex: 1;
}

.trend-period {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 4px;
    font-size: 14px;
}

.trend-stats {
    font-size: 12px;
    color: #64748b;
}

.trend-revenue {
    font-weight: 600;
    color: #10b981;
}

/* 无数据状态 */
.no-data {
    text-align: center;
    color: #64748b;
    padding: 20px;
    font-style: italic;
}

/* 最近销售 */
.recent-sales {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.sale-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.sale-item:last-child {
    border-bottom: none;
}

.sale-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 600;
}

.sale-details {
    flex: 1;
}

.sale-product {
    font-weight: 500;
    color: #1e293b;
    font-size: 14px;
}

.sale-time {
    font-size: 12px;
    color: #64748b;
}

.sale-amount {
    font-weight: 600;
    color: #10b981;
    font-size: 14px;
}

/* 页面头部 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
}

.page-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
}

.page-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 10px 16px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
    background: #f1f5f9;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #e2e8f0;
    color: #475569;
    transform: translateY(-1px);
}

/* 筛选器样式 */
.filters-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.filter-group:last-child {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: end;
}

.filter-group:last-child .btn {
    padding: 8px 16px;
    font-size: 14px;
}

/* 多选筛选器样式 */
.multi-select {
    position: relative;
    width: 100%;
}

.multi-select-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: border-color 0.2s;
    min-height: 38px;
}

.multi-select-display:hover {
    border-color: #9ca3af;
}

.multi-select.active .multi-select-display {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.multi-select-display .placeholder {
    color: #9ca3af;
    font-size: 14px;
}

.multi-select-display .selected-items {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    flex: 1;
}

.selected-item {
    background: #e0e7ff;
    color: #3730a3;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.selected-item .remove {
    cursor: pointer;
    font-weight: bold;
}

.selected-item .remove:hover {
    color: #dc2626;
}

.multi-select-display i {
    color: #6b7280;
    transition: transform 0.2s;
}

.multi-select.active .multi-select-display i {
    transform: rotate(180deg);
}

.multi-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 320px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.multi-select.active .multi-select-dropdown {
    display: block;
}

/* 禁用状态的筛选器样式 */
.multi-select.disabled .multi-select-display {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

.multi-select.disabled .multi-select-display:hover {
    border-color: #e9ecef;
}

.multi-select.disabled .multi-select-display .placeholder {
    color: #6c757d !important;
    font-style: italic;
}

.multi-select.disabled .multi-select-display i {
    color: #6c757d !important;
}

.select-all-option {
    padding: 8px 12px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.select-all-option label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    margin: 0;
}

.options-list {
    max-height: 280px;
    overflow-y: auto;
}

.options-list label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin: 0;
    font-size: 14px;
}

.options-list label:hover {
    background: #f3f4f6;
}

.options-list input[type="checkbox"] {
    margin: 0;
}

/* 筛选器行布局调整 */
.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
    margin-bottom: 16px;
}

.filter-row:last-child {
    margin-bottom: 0;
}

/* 数据表格样式 */
.table-wrapper {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
}

.data-table th {
    background: #f8fafc;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
    white-space: nowrap;
}

.data-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    color: #1e293b;
}

.data-table tbody tr:hover {
    background: #f8fafc;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: white;
    border-top: 1px solid #e2e8f0;
}

.pagination-info {
    color: #64748b;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    gap: 8px;
}

.pagination-btn {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.pagination-btn.active {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn-small {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn-view {
    background: #dbeafe;
    color: #1d4ed8;
}

.action-btn-view:hover {
    background: #bfdbfe;
}

.action-btn-edit {
    background: #fef3c7;
    color: #d97706;
}

.action-btn-edit:hover {
    background: #fde68a;
}

/* 数据表格容器 */
.data-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #64748b;
    position: relative;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
    vertical-align: middle;
}

/* 错误状态 */
.error {
    text-align: center;
    padding: 40px;
    color: #ef4444;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    margin: 20px 0;
}

/* 无数据状态 */
.no-data {
    text-align: center;
    padding: 40px;
    color: #9ca3af;
    font-style: italic;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 遮罩层 */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 响应式设计 */
@media (min-width: 1024px) {
    .app-container {
        display: block; /* 改为block，不使用flex布局 */
    }

    .sidebar {
        display: none; /* 大屏幕时隐藏侧边栏 */
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    /* 大屏幕时减少页面内容的空白 */
    .page-content {
        padding: 0 12px 12px 12px; /* 减少所有方向的padding */
    }

    .hamburger-btn {
        display: none;
    }

    .overlay {
        display: none;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .page-content {
        padding: 16px;
    }

    .top-header {
        padding: 0 16px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .sidebar {
        width: 100%;
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
    }

    .main-content.sidebar-open {
        margin-left: 0 !important;
    }

    /* 强制移动端布局 */
    .app-container {
        display: block !important;
        width: 100% !important;
        max-width: 100vw !important;
        overflow-x: hidden !important;
    }

    /* 确保没有元素超出屏幕 */
    * {
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    /* 隐藏可能导致遮挡的元素 */
    .overlay {
        display: none !important;
    }

    /* 移动端筛选器优化 */
    .filters-container {
        padding: 15px;
        margin-bottom: 15px;
        margin-left: 0;
        margin-right: 0;
    }

    .filter-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .filter-group {
        width: 100%;
    }

    .filter-group:last-child {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .filter-group:last-child .btn {
        width: 100%;
        justify-content: center;
    }

    /* 移动端表格优化 */
    .table-wrapper {
        margin: 0 -15px;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .sales-table {
        font-size: 12px;
    }

    .sales-table th,
    .sales-table td {
        padding: 8px 6px;
        white-space: nowrap;
    }

    /* 移动端多选筛选器优化 */
    .multi-select {
        width: 100%;
    }

    .multi-select-display {
        min-height: 40px;
        padding: 8px 12px;
    }

    .multi-select-dropdown {
        max-height: 320px;
        left: 0;
        right: 0;
        width: auto;
    }

    /* 移动端分析表格优化 */
    .analysis-section {
        margin: 0 -15px;
        padding: 15px;
        border-radius: 0;
    }

    .analysis-section h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .stat-card {
        padding: 16px;
    }

    .stat-value {
        font-size: 20px;
    }

    .card-content {
        padding: 16px;
    }

    /* 超小屏幕筛选器优化 */
    .filters-container {
        padding: 12px;
        margin: 0 -12px 15px -12px;
    }

    .filter-group label {
        font-size: 13px;
    }

    .filter-group select,
    .filter-group input {
        padding: 10px 12px;
        font-size: 14px;
    }

    /* 超小屏幕表格优化 */
    .sales-table {
        font-size: 11px;
    }

    .sales-table th,
    .sales-table td {
        padding: 6px 4px;
    }

    /* 超小屏幕多选筛选器 */
    .multi-select-display {
        font-size: 13px;
        padding: 10px 12px;
    }

    .selected-item {
        font-size: 12px;
        padding: 2px 6px;
    }

    /* 超小屏幕分析区域 */
    .analysis-section {
        margin: 0 -12px;
        padding: 12px;
    }

    .analysis-section h3 {
        font-size: 16px;
        margin-bottom: 12px;
    }
}

/* 超小屏幕优化 (320px及以下) */
@media (max-width: 320px) {
    .page-content {
        padding: 12px;
    }

    .filters-container {
        padding: 10px;
        margin: 0 -10px 12px -10px;
    }

    .filter-group select,
    .filter-group input {
        padding: 8px 10px;
        font-size: 13px;
    }

    .btn {
        padding: 8px 12px;
        font-size: 13px;
    }

    .sales-table {
        font-size: 10px;
    }

    .sales-table th,
    .sales-table td {
        padding: 4px 2px;
    }

    .analysis-section {
        margin: 0 -10px;
        padding: 10px;
    }

    .analysis-section h3 {
        font-size: 14px;
        margin-bottom: 10px;
    }
}

/* 销售分析样式 */
.sales-analysis-container {
    padding: 20px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
}

.analysis-section {
    margin-bottom: 30px;
}

.analysis-section h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.analysis-table-wrapper {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
    max-width: 100%;
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
}

.analysis-table th,
.analysis-table td {
    padding: 12px 15px;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
}

.analysis-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.analysis-table tbody tr:hover {
    background: #f8f9fa;
}

/* 汇总行样式 */
.analysis-table tbody tr[style*="background-color: #f8f9fa"] {
    background-color: #f8f9fa !important;
    border-top: 2px solid #dee2e6 !important;
    font-weight: bold !important;
}

/* 折叠功能样式 */
.section-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 15px;
    gap: 12px;
    flex-wrap: wrap;
    min-height: 32px;
}

.section-header h3,
.section-header h4 {
    margin: 0;
    color: #1f2937;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
    word-break: break-word;
    line-height: 1.3;
}

.section-header h3 {
    font-size: 18px;
}

.section-header h4 {
    font-size: 16px;
}

/* 折叠表格标题内的按钮样式 */
.collapsible-header h3 {
    margin: 0;
    color: #1f2937;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
}

/* 层级分析表格样式 */
.analysis-dimension-selector {
    border: 1px solid #e5e7eb;
}

.analysis-dimension-selector select {
    min-width: 100px;
}

.analysis-dimension-selector select:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
}

/* 层级表格行样式 */
#dynamicAnalysisTable tbody tr {
    transition: all 0.2s ease;
    border-bottom: 1px solid #f1f5f9;
    min-height: 48px;
}

#dynamicAnalysisTable tbody tr:hover {
    background-color: #f0f9ff !important;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 可点击的层级行 */
#dynamicAnalysisTable tbody tr[onclick] {
    cursor: pointer;
}

#dynamicAnalysisTable tbody tr[onclick]:hover {
    background-color: #dbeafe !important;
    transform: translateX(4px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 第一层级样式 */
#dynamicAnalysisTable tbody tr[style*="border-left: 4px solid"] {
    font-size: 15px;
    min-height: 52px;
}

/* 第二层级样式 */
#dynamicAnalysisTable tbody tr[style*="border-left: 3px solid"] {
    font-size: 14px;
    min-height: 48px;
}

/* 第三层级样式 */
#dynamicAnalysisTable tbody tr[style*="border-left: 2px solid"] {
    font-size: 13px;
    min-height: 44px;
}

/* 表格单元格对齐 */
#dynamicAnalysisTable td {
    vertical-align: middle;
    line-height: 1.4;
}

/* 第一列特殊样式 */
#dynamicAnalysisTable td:first-child {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
}

/* 数字列右对齐 */
#dynamicAnalysisTable td:not(:first-child) {
    text-align: right;
    font-variant-numeric: tabular-nums;
}

/* 展开/收起图标容器 */
#dynamicAnalysisTable .hierarchy-icon-container {
    display: flex;
    align-items: center;
    gap: 8px;
    min-height: 24px;
}

/* 展开/收起图标样式 */
#dynamicAnalysisTable .hierarchy-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    color: #374151;
    font-size: 12px;
    font-weight: bold;
    font-family: monospace;
    line-height: 1;
    transition: all 0.2s ease;
    border-radius: 2px;
}

#dynamicAnalysisTable .hierarchy-icon:hover {
    color: #1e40af;
    background-color: #e0f2fe;
    transform: scale(1.1);
}

/* 层级文本样式 */
#dynamicAnalysisTable .hierarchy-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.toggle-btn {
    background: none;
    border: 1px solid #d1d5db;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9fafb;
    min-width: 60px;
    height: 32px;
    font-size: 12px;
    flex-shrink: 0;
}

.toggle-btn:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
    transform: translateY(-1px);
}

.toggle-btn:active {
    transform: translateY(0);
}

.toggle-text {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.toggle-btn.collapsed .toggle-text {
    color: #6b7280;
}

/* 响应式设计 - 确保标题在小屏幕上完全显示 */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .section-header h3,
    .section-header h4 {
        font-size: 16px;
        width: 100%;
        flex: none;
    }
    
    .toggle-btn {
        align-self: flex-end;
        min-width: 50px;
        height: 28px;
        padding: 4px 8px;
    }
    
    .toggle-text {
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .section-header h3,
    .section-header h4 {
        font-size: 14px;
    }
}

/* 图表数据标签控制按钮样式 */
.data-labels-toggle {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 10;
}

.data-labels-toggle button {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(2px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-labels-toggle button:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.data-labels-toggle .btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.data-labels-toggle .btn-secondary:hover {
    background: #5a6268;
    border-color: #545b62;
}

/* 确保图表容器有相对定位，但排除气泡图容器避免干扰双击事件 */
.chart-container:not(.bubble-chart-container),
.monthly-comparison-chart-container,
.chart-item:not(.bubble-chart-container) {
    position: relative;
}

/* 图表数据标签样式 */
.chart-data-label {
    font-size: 11px;
    font-weight: bold;
    color: #333;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 2px;
    padding: 1px 3px;
}

/* 图表标题和数据类型切换按钮 */
.chart-header-with-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.chart-header-with-toggle h3 {
    margin: 0;
    flex: 1;
    min-width: 200px;
}

.data-type-toggle {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-type-toggle .toggle-btn {
    padding: 6px 12px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s ease;
    min-width: 40px;
}

.data-type-toggle .toggle-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.data-type-toggle .toggle-btn.active {
    background: #007bff;
    color: white;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.data-type-toggle .toggle-btn:first-child {
    border-right: 1px solid #ddd;
}

.data-type-toggle .toggle-btn.active:first-child {
    border-right: 1px solid #0056b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chart-header-with-toggle {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .chart-header-with-toggle h3 {
        margin-bottom: 10px;
    }
    
    .data-type-toggle {
        align-self: flex-end;
    }
}

/* 用户通知动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.user-notification {
    transition: all 0.3s ease;
}

.user-notification:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}



.collapsible-content {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    opacity: 1;
    max-height: none;
    margin-bottom: 20px;
}

.collapsible-content.collapsed {
    max-height: 0 !important;
    opacity: 0;
    margin-bottom: 0 !important;
    margin-top: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

/* 标题文字过渡动画 */
.section-header h3,
.section-header h4 {
    transition: opacity 0.3s ease;
    opacity: 1 !important; /* 确保标题始终可见 */
}

/* 折叠状态下的标题样式 */
.toggle-btn.collapsed + h3,
.toggle-btn.collapsed + h4,
.section-header:has(.toggle-btn.collapsed) h3,
.section-header:has(.toggle-btn.collapsed) h4 {
    opacity: 1 !important;
    color: #6b7280; /* 折叠状态下稍微变浅，但仍然可见 */
}

.analysis-table tbody tr[style*="background-color: #f8f9fa"]:hover {
    background-color: #e9ecef !important;
}

/* 图表区域外层容器 */
.charts-section {
    margin-top: 20px;
}

/* 月份对比图表区域外层容器 */
.monthly-charts-section {
    margin-top: 20px;
}

/* 气泡图区域外层容器 */
.bubble-charts-section {
    margin-top: 20px;
}

/* 销售指标对比分析区域外层容器 */
.sales-target-comparison-section {
    margin-top: 20px;
}

/* 销售员占比分析图区域外层容器 */
.pie-chart-section {
    margin-top: 20px;
}

/* 图表容器样式 - 6列网格布局，第一行3个图表，第二行2个图表 */
.charts-container {
    display: grid !important;
    grid-template-columns: repeat(6, 1fr) !important;
    grid-template-rows: auto auto !important;
    gap: 20px;
    margin-top: 0;
    padding: 20px;
    width: 100%;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-sizing: border-box;
}

/* 图表项样式 */
.chart-item {
    background: white;
    border-radius: 6px;
    padding: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    min-height: 300px;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
}

.chart-item h3 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    text-align: center;
    width: 100%;
    line-height: 1.2;
}

/* 响应式图表容器布局 - 根据屏幕尺寸调整 */
.charts-container {
    display: grid !important;
    grid-template-rows: auto !important;
}

/* 电脑端默认6列网格布局 */
@media (min-width: 769px) {
    .charts-container {
        grid-template-columns: repeat(6, 1fr) !important;
    }

    #chartsContainer {
        grid-template-columns: repeat(6, 1fr) !important;
    }
}

/* 平板端2列布局 */
@media (max-width: 768px) and (min-width: 481px) {
    .charts-container {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    #chartsContainer {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* 手机端单列布局 */
@media (max-width: 480px) {
    .charts-container {
        grid-template-columns: 1fr !important;
    }

    #chartsContainer {
        grid-template-columns: 1fr !important;
    }
}

/* 全宽度柱状图样式 */
.chart-item-full {
    grid-column: 1 / -1;
    min-height: 480px;
    padding: 20px;
}

/* 半宽度柱状图样式 - 用于并排显示 */
.chart-item-half {
    min-height: 480px;
    padding: 20px;
}

/* 月份对比图表容器 - 2x2网格布局 */
.monthly-charts-container {
    grid-column: 1 / -1;
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    grid-template-rows: auto auto;
    gap: 20px;
    margin-top: 20px;
}

.monthly-comparison-chart-container {
    position: relative;
    height: 400px;
    margin-top: 16px;
}

.monthly-comparison-chart-container canvas {
    width: 100% !important;
    height: 100% !important;
}

/* 医院销量占比图表样式 */
.hospital-sales-chart-section {
    grid-column: 1 / -1;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hospital-sales-chart-container {
    grid-column: 1 / -1;
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    grid-template-rows: auto !important;
    gap: 20px;
    margin-top: 20px;
}

.hospital-sales-chart-wrapper {
    position: relative;
    height: 500px;
    margin-top: 16px;
}

.hospital-sales-chart-wrapper canvas {
    width: 100% !important;
    height: 100% !important;
}

/* 强制医院图表并排显示 - 仅适用于中大屏幕 */
@media (min-width: 481px) {
    .hospital-sales-chart-container {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        grid-template-rows: auto !important;
    }
}

/* 大屏幕优化 - 增加间距 */
@media (min-width: 1400px) {
    .hospital-sales-chart-container {
        gap: 30px !important;
        padding: 0 20px;
    }

    .monthly-comparison-chart-container {
        height: 500px;
    }
}

/* 超大屏幕优化 - 全屏显示 */
@media (min-width: 1920px) {
    .hospital-sales-chart-container {
        gap: 40px !important;
        padding: 0 40px;
    }

    .monthly-comparison-chart-container {
        height: 550px;
    }
}

.chart-info {
    margin-top: 8px;
}

.info-text {
    font-size: 14px;
    color: #666;
    font-style: italic;
}

/* 销售指标对比图表样式 */
.sales-target-comparison-container {
    grid-column: 1 / -1;
    margin-top: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sales-target-comparison-container .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.sales-target-comparison-container .chart-header .section-header {
    margin-bottom: 0;
    flex: 1;
    min-width: 300px;
}

.sales-target-comparison-container .chart-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
}

.sales-target-comparison-container .chart-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-shrink: 0;
}

.sales-target-comparison-container .form-select {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #374151;
}

.sales-target-comparison-container .chart-container {
    margin-bottom: 30px;
    height: 400px;
}

.sales-target-comparison-container canvas {
    width: 100% !important;
    height: 100% !important;
}

/* 销售指标表格样式 */
.sales-target-table-container {
    margin-top: 20px;
}

.sales-target-table-container h4 {
    margin-bottom: 15px;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.table-responsive {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.sales-target-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;
}

.sales-target-table th {
    background: #f9fafb;
    color: #374151;
    font-weight: 600;
    padding: 12px 16px;
    text-align: center;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
}

.sales-target-table td {
    padding: 12px 16px;
    text-align: center;
    border-bottom: 1px solid #f3f4f6;
    white-space: nowrap;
}

.sales-target-table tbody tr:hover {
    background: #f9fafb;
}

.sales-target-table tbody tr:last-child td {
    border-bottom: none;
}

/* 数值样式 */
.sales-target-table .amount {
    font-weight: 500;
    color: #1f2937;
}

.sales-target-table .amount.primary {
    font-weight: 600;
    color: #1f2937;
    background-color: #f0f9ff;
}

.sales-target-table .amount.secondary {
    font-weight: 400;
    color: #6b7280;
}

.sales-target-table .completion-rate {
    font-weight: 500;
}

.sales-target-table .completion-high {
    color: #059669;
}

.sales-target-table .completion-medium {
    color: #d97706;
}

.sales-target-table .completion-low {
    color: #dc2626;
}

.sales-target-table .completion-none {
    color: #9ca3af;
}

.sales-target-table .qp-value {
    font-weight: 500;
    color: #7c3aed;
}

/* 汇总行样式 */
.sales-target-table .summary-row {
    background-color: #f8f9fa !important;
    border-top: 2px solid #dee2e6 !important;
    font-weight: bold !important;
}

.sales-target-table .summary-row:hover {
    background-color: #f8f9fa !important;
}

.sales-target-table .summary-row td {
    font-weight: bold !important;
    color: #495057 !important;
    border-bottom: none !important;
    padding: 14px 16px !important;
}

.sales-target-table .summary-row .amount {
    font-weight: bold !important;
}

.sales-target-table .summary-row .completion-rate {
    font-weight: bold !important;
}

.sales-target-table .summary-row .qp-value {
    font-weight: bold !important;
}

/* 图表网格布局 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    min-height: 360px;
    transition: all 0.3s ease;
}

/* 图表标题字体大小调整 */
.chart-item h3 {
    font-size: 12px;
    margin-bottom: 6px;
    text-align: center;
    color: #374151;
    font-weight: 600;
}

    /* 平板和中等屏幕尺寸优化 */
    @media (min-width: 768px) and (max-width: 1024px) {
        .charts-container {
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            padding: 15px;
        }

        .chart-item {
            min-height: 240px;
            padding: 12px;
        }

        .chart-item h3 {
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
        }

        .achievement-chart-container,
        .yoy-comparison-chart-container,
        .mom-comparison-chart-container {
            width: 140px;
            height: 140px;
        }

        .achievement-rate,
        .comparison-ratio {
            font-size: 16px;
        }

        .achievement-label,
        .comparison-label {
            font-size: 9px;
        }

        .legend-item {
            font-size: 11px;
            padding: 2px 4px;
        }

        .legend-color {
            width: 9px;
            height: 9px;
        }

        .chart-data-info {
            font-size: 10px;
        }

        .data-item {
            padding: 2px 6px;
        }

        .monthly-charts-container {
            grid-template-columns: 1fr 1fr !important; /* 平板端保持并排 */
            gap: 15px;
        }

        .hospital-sales-chart-container {
            grid-template-columns: 1fr 1fr !important; /* 平板端医院图表保持并排 */
            gap: 20px !important;
        }

        .chart-item-half {
            min-height: 420px;
            padding: 15px;
        }
    }

/* 响应式设计 - 保持6列布局，中等屏幕改为4列 */
@media (max-width: 1200px) and (min-width: 901px) {
    .charts-container {
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
        padding: 15px;
    }
}

@media (max-width: 900px) {
    .charts-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 15px;
    }

    .chart-item {
        min-height: 240px;
        padding: 10px;
    }

    .chart-item h3 {
        font-size: 13px;
        margin-bottom: 10px;
    }

    /* 保持1x4布局时的图表尺寸 */
    .achievement-chart-container,
    .yoy-comparison-chart-container,
    .mom-comparison-chart-container {
        width: 130px;
        height: 130px;
    }

    .achievement-rate,
    .comparison-ratio {
        font-size: 14px;
    }

    .achievement-label,
    .comparison-label {
        font-size: 8px;
    }

    .legend-item {
        font-size: 10px;
        padding: 2px 3px;
    }

    .legend-color {
        width: 8px;
        height: 8px;
    }

    .chart-data-info {
        font-size: 10px;
    }

    .data-item {
        padding: 2px 6px;
    }
}

@media (max-width: 768px) and (min-width: 481px) {
    .charts-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        padding: 12px;
    }

    .chart-item {
        min-height: 240px;
        padding: 12px;
    }

    .chart-item h3 {
        font-size: 13px;
        margin-bottom: 10px;
    }

    /* 平板端图表尺寸 */
    .achievement-chart-container,
    .yoy-comparison-chart-container,
    .mom-comparison-chart-container {
        width: 140px;
        height: 140px;
    }

    .achievement-rate,
    .comparison-ratio {
        font-size: 12px;
    }

    .achievement-label,
    .comparison-label {
        font-size: 7px;
    }

    .legend-item {
        font-size: 9px;
        padding: 2px 3px;
    }

    .legend-color {
        width: 7px;
        height: 7px;
    }

    .chart-data-info {
        font-size: 9px;
    }

    .data-item {
        padding: 2px 5px;
    }

    .chart-item-full {
        min-height: 400px;
    }

    .chart-item-half {
        min-height: 400px;
    }

    .monthly-charts-container {
        grid-template-columns: 1fr 1fr !important; /* 保持并排显示 */
        gap: 15px;
    }

    .hospital-sales-chart-container {
        grid-template-columns: 1fr 1fr !important; /* 医院图表保持并排显示 */
        gap: 20px !important;
    }

    .monthly-comparison-chart-container {
        height: 320px;
    }
}

@media (max-width: 480px) {
    /* 手机端数据分析图表 - 每行一个图表 */
    .charts-container {
        display: grid !important;
        grid-template-columns: 1fr !important;
        grid-template-rows: repeat(5, auto) !important;
        gap: 20px !important;
        padding: 20px 15px !important;
    }

    .chart-item {
        width: 100% !important;
        max-width: 320px !important;
        margin: 0 auto !important;
        padding: 20px 15px !important;
        min-height: 300px !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        background: #ffffff !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
    }

    .chart-item h3 {
        font-size: 18px !important;
        margin-bottom: 15px !important;
        text-align: center !important;
        color: #1f2937 !important;
        font-weight: 600 !important;
    }

    /* 手机端竖直显示，图表尺寸保持一致 */
    .achievement-chart-container,
    .yoy-comparison-chart-container,
    .mom-comparison-chart-container {
        width: 180px;
        height: 180px;
    }

    .achievement-rate,
    .comparison-ratio {
        font-size: 18px;
    }

    .achievement-label,
    .comparison-label {
        font-size: 10px;
    }

    .legend-item {
        font-size: 10px;
        padding: 3px 5px;
    }

    .legend-color {
        width: 8px;
        height: 8px;
    }

    .chart-data-info {
        font-size: 12px;
        margin-top: 10px;
    }

    .data-item {
        padding: 4px 10px;
    }

    .chart-item-full {
        min-height: 350px;
        padding: 16px;
    }

    .chart-item-half {
        min-height: 350px;
        padding: 16px;
    }

    .monthly-charts-container {
        grid-template-columns: 1fr !important; /* 小屏幕垂直排列 */
        gap: 12px;
    }

    .hospital-sales-chart-container {
        grid-template-columns: 1fr !important; /* 小屏幕医院图表垂直排列 */
        gap: 20px !important;
    }
    
    /* 手机端医院图表容器高度优化 */
    .hospital-sales-chart-wrapper {
        height: 400px !important; /* 增加高度避免压扁 */
    }

    /* 手机端产品销售占比图样式 */
    .chart-item-pie-large {
        grid-column: span 1 !important;
        width: 100% !important;
        max-width: 320px !important;
        margin: 0 auto !important;
        min-height: 380px !important;
        padding: 20px 15px !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .chart-item-pie-large canvas {
        height: 280px !important; /* 与销售员图表保持一致 */
        max-width: 280px !important; /* 与销售员图表保持一致 */
        width: 100% !important;
    }

    .monthly-comparison-chart-container {
        height: 380px !important; /* 增加高度避免压扁 */
    }
    
    /* 手机端医院图表项样式优化 */
    .hospital-sales-chart-container .chart-item-half {
        min-height: 420px !important;
        padding: 20px 15px !important;
    }
}

/* 手机端专用 - 强制医院图表垂直排列 */
@media (max-width: 480px) {
    /* 强制覆盖所有医院图表容器样式 */
    .hospital-sales-chart-container,
    #hospitalSalesChartContainer {
        display: grid !important;
        grid-template-columns: 1fr !important;
        grid-template-rows: repeat(2, auto) !important;
        gap: 20px !important;
        width: 100% !important;
    }
    
    /* 医院图表项在手机端占满宽度 */
    .hospital-sales-chart-container .chart-item,
    .hospital-sales-chart-container .chart-item-half {
        grid-column: 1 / -1 !important;
        width: 100% !important;
        max-width: 100% !important;
        margin: 0 !important;
    }
    
    /* 医院图表canvas容器在手机端优化 */
    .hospital-sales-chart-container .monthly-comparison-chart-container {
        height: 380px !important;
        width: 100% !important;
        position: relative !important;
    }
    
    .hospital-sales-chart-container .monthly-comparison-chart-container canvas {
        width: 100% !important;
        height: 100% !important;
        max-height: 380px !important;
    }
}

/* 总达成率环形图样式 */
.achievement-chart-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 180px;
    height: 180px;
    margin: 0 auto;
}

.chart-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
}

.achievement-rate {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    line-height: 1;
}

.achievement-label {
    font-size: 10px;
    color: #666;
    margin-top: 2px;
}

/* 产品占比饼图样式 */
.product-share-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    width: 100%;
    min-height: 280px;
}

.product-share-chart-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 280px;
    height: 280px;
    flex-shrink: 0;
    position: relative;
}

/* 紧凑图例样式 - 用于产品销售占比等图表 */
.chart-legend-compact {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
    margin-top: 8px;
    font-size: 9px;
    line-height: 1.2;
}

.chart-legend-compact .legend-item {
    display: flex;
    align-items: center;
    gap: 3px;
    padding: 2px 4px;
    background: #f8f9fa;
    border-radius: 3px;
    font-size: 8px;
    min-height: 18px;
    white-space: nowrap;
    flex: 0 0 auto;
}

.chart-legend-compact .legend-color {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    flex-shrink: 0;
}

.chart-legend-compact .legend-label {
    font-size: 8px;
    font-weight: 500;
    max-width: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chart-legend-compact .legend-percentage {
    font-size: 7px;
    color: #666;
    font-weight: 600;
}

/* 同比和环比图表容器样式 */
.yoy-comparison-chart-container,
.mom-comparison-chart-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 180px;
    height: 180px;
    margin: 0 auto;
}

.chart-legend {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
    max-height: 80px;
    overflow-y: auto;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 12px;
    color: #333;
    background: #f8f9fa;
    padding: 3px 6px;
    border-radius: 3px;
    white-space: nowrap;
}

.legend-color {
    width: 10px;
    height: 10px;
    border-radius: 2px;
    flex-shrink: 0;
}

/* 图表数据信息样式 */
.chart-data-info {
    margin-top: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 11px;
    text-align: center;
}

/* 水平排列的数据信息 */
.chart-data-info.horizontal-data {
    flex-direction: row;
    justify-content: center;
    gap: 15px;
}

/* 总达成率的金额信息 */
.chart-data-info.achievement-amounts {
    flex-direction: row;
    justify-content: center;
    gap: 15px;
}

/* 饼图包装器 - 使用原生响应式，不使用CSS缩放 */
.pie-chart-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
    justify-content: center;
    min-height: 300px;
    padding: 10px;
    flex-wrap: nowrap;
    box-sizing: border-box;
    overflow: visible;
    width: 100%;
    max-width: 100vw;
    /* 移除transform相关属性，避免鼠标事件位置错误 */
}



/* 卡片式图例容器 */
.chart-legend-cards {
    display: flex !important;
    flex-direction: column !important;
    gap: 4px !important;
    width: 350px !important;
    max-width: 350px !important;
    max-height: 450px;
    overflow-y: auto;
    flex-shrink: 1;
    align-self: center;
    margin: 0;
    box-sizing: border-box;
    visibility: visible !important;
    opacity: 1 !important;
    background: transparent !important;
}

/* 强制显示图例文字 - 确保文字可见 */
.chart-legend-cards * {
    visibility: visible !important;
    opacity: 1 !important;
}

.chart-legend-cards .legend-label,
.chart-legend-cards .legend-value,
.chart-legend-cards .legend-percentage {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
}

/* 图例卡片项 */
.chart-legend-cards .legend-item {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 10px !important;
    background: #ffffff !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
    min-height: 32px !important;
    margin-bottom: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    visibility: visible !important;
    opacity: 1 !important;
}

.chart-legend-cards .legend-item:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 图例颜色指示器 */
.chart-legend-cards .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 图例标签 */
.chart-legend-cards .legend-label {
    font-size: 12px !important;
    font-weight: 600 !important;
    color: #374151 !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90px;
    flex-shrink: 1;
    min-width: 50px;
    line-height: 1.4;
}

/* 图例数值 */
.chart-legend-cards .legend-value {
    font-size: 10px !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
    white-space: nowrap;
    flex: 1;
    text-align: left;
    margin-left: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    line-height: 1.4;
}

/* 图例百分比 */
.chart-legend-cards .legend-percentage {
    font-size: 10px !important;
    color: #059669 !important;
    font-weight: 600 !important;
    background: #ecfdf5 !important;
    padding: 2px 6px !important;
    border-radius: 3px;
    white-space: nowrap;
    flex-shrink: 0;
    line-height: 1.4;
}

/* 大屏幕响应式 - 调整间距和图例尺寸，不使用transform */
@media (max-width: 1400px) and (min-width: 1001px) {
    .pie-chart-wrapper {
        gap: 15px;
        padding: 8px;
    }

    .chart-legend-cards {
        width: 320px;
        max-width: 320px;
        max-height: 400px;
    }
}

/* 中等屏幕响应式设计 - 调整布局，不使用transform */
@media (max-width: 1000px) and (min-width: 801px) {
    .pie-chart-wrapper {
        gap: 12px;
        padding: 6px;
    }

    .chart-legend-cards {
        width: 300px;
        max-width: 300px;
        max-height: 380px;
    }

    .chart-legend-cards .legend-label {
        max-width: 50px;
        font-size: 10px;
    }
}

/* 小屏幕响应式设计 - 调整布局，不使用transform */
@media (max-width: 800px) and (min-width: 701px) {
    .pie-chart-wrapper {
        gap: 10px;
        padding: 5px;
    }

    .chart-legend-cards {
        width: 280px;
        max-width: 280px;
        max-height: 380px;
    }

    .chart-legend-cards .legend-label {
        max-width: 45px;
        font-size: 10px;
    }

    .chart-legend-cards .legend-value,
    .chart-legend-cards .legend-percentage {
        font-size: 9px;
    }
}

/* 小屏幕改为垂直布局 */
@media (max-width: 700px) {
    .pie-chart-wrapper {
        flex-direction: column;
        align-items: center;
        gap: 10px;
        padding: 5px;
        min-height: 250px;
    }

    .chart-legend-cards {
        width: 100%;
        max-width: 350px;
        gap: 2px;
        max-height: 300px;
    }
}

    .chart-legend-cards .legend-item {
        padding: 5px 8px;
        min-height: 26px;
        margin-bottom: 2px;
        gap: 6px;
    }

    .chart-legend-cards .legend-label {
        font-size: 10px;
        max-width: 100px;
    }

    .chart-legend-cards .legend-value,
    .chart-legend-cards .legend-percentage {
        font-size: 9px;
    }
}

/* 超小屏幕响应式 */
@media (max-width: 500px) {
    .pie-chart-wrapper {
        gap: 8px;
        padding: 3px;
        min-height: 200px;
    }

    .chart-legend-cards {
        max-width: 320px;
    }

    .chart-legend-cards .legend-label {
        font-size: 9px;
        max-width: 80px;
    }

    .chart-legend-cards .legend-value,
    .chart-legend-cards .legend-percentage {
        font-size: 8px;
    }
}



@media (max-width: 480px) {
    .chart-legend-cards {
        max-height: 200px;
        max-width: 300px;
    }

    .chart-legend-cards .legend-item {
        padding: 4px 6px;
        min-height: 24px;
        margin-bottom: 1px;
        gap: 5px;
    }

    .chart-legend-cards .legend-color {
        width: 10px;
        height: 10px;
    }

    .chart-legend-cards .legend-label {
        font-size: 9px;
        max-width: 80px;
    }

    .chart-legend-cards .legend-value,
    .chart-legend-cards .legend-percentage {
        font-size: 8px;
    }
}

/* 图例滚动条样式 */
.chart-legend-cards::-webkit-scrollbar {
    width: 4px;
}

.chart-legend-cards::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.chart-legend-cards::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.chart-legend-cards::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 产品销售占比图表样式 - 与其他环形图表完全一致 */
.product-share-chart-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 280px;
    height: 280px;
    margin: 0 auto;
}

/* 隐藏产品销售占比图表中间的文本 */
.product-share-chart-container .chart-center-text {
    display: none !important;
}

/* 产品图例卡片样式 */
.product-legend-cards {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    max-height: 280px;
    overflow-y: auto;
    padding: 10px 0;
    justify-content: flex-start;
}

.product-legend-card {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid;
    font-size: 12px;
    transition: all 0.2s ease;
    width: 100%;
    min-height: 50px;
}

.product-legend-card:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.product-legend-card .product-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.product-legend-card .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.product-legend-card .product-name {
    font-weight: 600;
    color: #374151;
    font-size: 11px;
    line-height: 1.2;
}

.product-legend-card .product-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

.product-legend-card .product-amount {
    color: #059669;
    font-weight: 600;
    font-size: 10px;
}

.product-legend-card .product-percentage {
    background: #dcfce7;
    color: #059669;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 10px;
    min-width: 40px;
    text-align: center;
}

.product-share-chart-container canvas {
    display: block;
    margin: 0 auto;
    width: 280px !important;
    height: 280px !important;
}

/* 产品销售占比图例样式 */
.product-share-legend {
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 100%;
    max-width: 350px;
    max-height: 300px;
    overflow-y: auto;
    padding: 0;
    margin: 8px 0 0 0;
}

.product-share-legend .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    transition: all 0.2s ease;
    min-height: 28px;
}

.product-share-legend .legend-item:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.product-share-legend .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.product-share-legend .legend-label {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    min-width: 60px;
    flex-shrink: 0;
}

.product-share-legend .legend-value {
    font-size: 10px;
    color: #6b7280;
    font-weight: 500;
    flex: 1;
}

.product-share-legend .legend-percentage {
    font-size: 10px;
    font-weight: 600;
    color: #059669;
    background: #ecfdf5;
    padding: 2px 6px;
    border-radius: 3px;
    min-width: 35px;
    text-align: center;
    flex-shrink: 0;
}

/* 销售员业绩占比图表样式 - 与其他环形图表完全一致 */
.sales-rep-chart-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 280px;
    height: 280px;
    margin: 0 auto;
}

.sales-rep-chart-container canvas {
    display: block;
    margin: 0 auto;
    width: 280px !important;
    height: 280px !important;
}

/* 销售员业绩占比图例样式 */
.sales-rep-legend {
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 100%;
    max-width: 400px;
    max-height: 300px;
    overflow-y: auto;
    padding: 0;
    margin: 8px 0 0 0;
}

.sales-rep-legend .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    transition: all 0.2s ease;
    min-height: 28px;
}

.sales-rep-legend .legend-item:hover {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.sales-rep-legend .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.sales-rep-legend .legend-label {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    min-width: 60px;
    flex-shrink: 0;
}

.sales-rep-legend .legend-value {
    font-size: 10px;
    color: #6b7280;
    font-weight: 500;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sales-rep-legend .legend-percentage {
    font-size: 10px;
    font-weight: 600;
    color: #059669;
    background: #ecfdf5;
    padding: 2px 6px;
    border-radius: 3px;
    min-width: 35px;
    text-align: center;
    flex-shrink: 0;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 8px;
    background: #f8f9fa;
}

/* 水平排列时的数据项样式 */
.horizontal-data .data-item,
.achievement-amounts .data-item {
    flex-direction: column;
    justify-content: center;
    text-align: center;
    min-width: 80px;
    gap: 2px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.horizontal-data .data-item .data-label,
.achievement-amounts .data-item .data-label {
    font-size: 10px;
    color: #666;
    font-weight: normal;
}

.horizontal-data .data-item .data-value,
.achievement-amounts .data-item .data-value {
    font-size: 12px;
    font-weight: bold;
    color: #333;
}

/* 移动端响应式调整 */
@media (max-width: 768px) {
    .chart-data-info.horizontal-data,
    .chart-data-info.achievement-amounts {
        gap: 10px;
    }

    .horizontal-data .data-item,
    .achievement-amounts .data-item {
        min-width: 70px;
        padding: 4px 6px;
    }

    .horizontal-data .data-item .data-label,
    .achievement-amounts .data-item .data-label {
        font-size: 9px;
    }

    .horizontal-data .data-item .data-value,
    .achievement-amounts .data-item .data-value {
        font-size: 11px;
    }
}
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

.data-label {
    color: #666;
    font-weight: 500;
}

.data-value {
    color: #333;
    font-weight: 600;
}

/* 同期对比环形图样式 - 尺寸已在上面统一定义 */

.comparison-ratio {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    line-height: 1;
}

.comparison-label {
    font-size: 10px;
    color: #666;
    margin-top: 2px;
}

/* 环比增长率环形图样式 - 尺寸已在上面统一定义 */

/* 图表样式已移除 */

/* 总计汇总样式 */
.summary-section {
    margin-bottom: 30px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.summary-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-align: center;
}

.summary-card .card-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.summary-card .card-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

/* 图表样式已移除 */

/* 鼓励话语区域样式 */
.encouragement-section {
    background: white;
    padding: 40px 20px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    text-align: center;
    margin: 20px 0;
}

.encouragement-card {
    max-width: 600px;
    margin: 0 auto;
}

.encouragement-icon {
    font-size: 48px;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

.encouragement-text {
    font-size: 24px;
    font-weight: 600;
    color: #2563eb;
    line-height: 1.4;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.encouragement-author {
    font-size: 16px;
    color: #6b7280;
    font-style: italic;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* 图表样式已移除 */

/* 图表样式已移除 */

/* 销售分析响应式设计 */

@media (max-width: 768px) {
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .summary-card {
        padding: 15px;
    }

    .summary-card .card-value {
        font-size: 18px;
    }

    .analysis-table-wrapper {
        font-size: 12px;
    }

    .analysis-table th,
    .analysis-table td {
        padding: 8px 6px;
        white-space: nowrap;
    }

    /* 移动端图表样式 - 这个规则被更具体的媒体查询覆盖 */
    /* 注释掉以避免与更具体的媒体查询冲突 */
    /*
    .charts-container,
    .charts-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        grid-template-rows: auto !important;
        gap: 15px !important;
        padding: 15px !important;
    }

    .chart-item,
    .chart-container {
        width: 100% !important;
        max-width: 300px !important;
        margin: 0 auto !important;
        padding: 15px !important;
        min-height: 280px !important;
    }
    */

    .achievement-chart-container,
    .product-share-chart-container,
    .yoy-comparison-chart-container,
    .mom-comparison-chart-container {
        width: 180px !important;
        height: 180px !important;
        margin: 0 auto !important;
    }

    .achievement-rate,
    .comparison-ratio {
        font-size: 24px !important;
    }

    .chart-title {
        font-size: 14px !important;
        margin-bottom: 10px !important;
    }

    .chart-legend {
        max-height: 80px !important;
        font-size: 10px !important;
        margin-top: 10px !important;
    }

    .legend-item {
        font-size: 11px !important;
        padding: 3px 5px !important;
    }

    /* 移动端柱状图样式 */
    .yoy-bar-chart-container {
        height: 250px !important;
        min-height: 250px !important;
    }

    /* 图表样式已移除 */
}

@media (max-width: 480px) {
    .summary-cards {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .summary-card {
        padding: 12px;
    }

    .summary-card .card-value {
        font-size: 16px;
    }

    .analysis-table-wrapper {
        font-size: 11px;
    }

    .analysis-table th,
    .analysis-table td {
        padding: 6px 4px;
    }

    /* 小屏幕图表样式 - 注释掉以避免冲突 */
    /*
    .charts-container,
    .charts-grid {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 12px !important;
        padding: 12px !important;
    }

    .chart-item,
    .chart-container {
        max-width: 280px !important;
        margin: 0 auto !important;
        padding: 12px !important;
        min-height: 260px !important;
    }
    */

    .achievement-chart-container,
    .yoy-comparison-chart-container,
    .mom-comparison-chart-container {
        width: 180px !important;
        height: 180px !important;
    }

    .achievement-rate,
    .comparison-ratio {
        font-size: 18px !important;
    }

    .chart-title {
        font-size: 8px !important;
        margin-bottom: 4px !important;
    }

    .chart-item h3 {
        font-size: 16px !important;
        margin-bottom: 10px !important;
    }

    .chart-data-info {
        font-size: 12px !important;
        margin-top: 10px !important;
    }

    .data-item {
        padding: 4px 10px !important;
    }

    .chart-legend {
        max-height: 60px !important;
        gap: 3px !important;
        font-size: 9px !important;
    }

    .legend-item {
        font-size: 10px !important;
        padding: 2px 4px !important;
    }

    /* 小屏幕柱状图样式 */
    .yoy-bar-chart-container {
        height: 200px !important;
        min-height: 200px !important;
    }

    /* 图表样式已移除 */
}

/* 用户管理样式 */
.users-management-container {
    padding: 10px 0; /* 添加适当的上下padding */
}

.users-header {
    display: flex;
    justify-content: flex-end; /* 改为右对齐，因为只有按钮了 */
    align-items: center;
    margin-bottom: 8px; /* 进一步减少底部间距 */
    padding: 8px 12px 8px 12px; /* 与表格边距保持一致 */
    border-bottom: 1px solid #e2e8f0;
}

.users-header h2 {
    color: #1e293b;
    font-size: 24px;
    font-weight: 600;
}

.users-table-wrapper {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 0 12px 12px 12px; /* 减少边距 */
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th {
    background-color: #f8fafc;
    color: #374151;
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
}

.users-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
    color: #374151;
    font-size: 14px;
}

.users-table tbody tr:hover {
    background-color: #f9fafb;
}

.role-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.role-badge.admin {
    background-color: #fef3c7;
    color: #92400e;
}

.role-badge.user {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 50px;
}

.status-badge.active {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.inactive {
    background-color: #fee2e2;
    color: #dc2626;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    gap: 6px;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}

.btn-danger {
    border-color: #dc2626;
    color: #dc2626;
}

.btn-danger:hover {
    background-color: #fef2f2;
    border-color: #b91c1c;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px 24px;
    border-top: 1px solid #e5e7eb;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #374151;
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

.form-text {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #6b7280;
}

/* 按钮样式增强 */
.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .users-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .users-table-wrapper {
        overflow-x: auto;
    }

    .users-table {
        min-width: 800px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .modal {
        padding: 10px;
    }

    .modal-content {
        max-height: 95vh;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
}

/* 气泡图样式 */
.bubble-charts-container {
    grid-column: 1 / -1; /* 占据整个网格宽度，和销售指标对比表格一样 */
    margin-top: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.bubble-charts-header {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
}

.chart-mode-toggle {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    gap: 5px;
}

.mode-btn {
    padding: 8px 16px;
    border: 2px solid #3b82f6;
    background: white;
    color: #3b82f6;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mode-btn:hover {
    background: #eff6ff;
    transform: translateY(-1px);
}

.mode-btn.active {
    background: #3b82f6;
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.bubble-charts-header h3 {
    color: #1f2937;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
}

.bubble-charts-description {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
}

/* 双气泡图并排布局 */
.dual-bubble-layout {
    display: flex;
    gap: 16px;
    margin-top: 20px;
    width: 100%;
}

/* 单个气泡图容器 */
.bubble-chart-container {
    flex: 1;
    min-width: 0; /* 允许flex项目收缩 */
    background: #f8fafc;
    border-radius: 8px;
    padding: 20px; /* 增加内边距为气泡留出空间 */
    border: 1px solid #e2e8f0;
    overflow: hidden; /* 确保气泡不会超出容器边界 */
    position: relative; /* 为绝对定位的子元素提供参考 */
}

.chart-header {
    margin-bottom: 12px;
}

.chart-header h4 {
    color: #1f2937;
    font-size: 15px;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-align: center;
}

.bubble-legend {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-label {
    font-size: 11px;
    color: #6b7280;
    background: #ffffff;
    padding: 3px 6px;
    border-radius: 3px;
    border: 1px solid #e5e7eb;
    white-space: nowrap;
}

.chart-canvas-container {
    position: relative;
    height: 580px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    overflow: visible; /* 允许气泡完全显示 */
}

.chart-canvas-container canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
    /* 防止气泡图元素超出canvas边界 */
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 3D气泡图容器样式 */
.threejs-container {
    width: 100%;
    height: 600px;
    border-radius: 8px;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.threejs-container canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
}

.bubble-chart-container {
    width: 100%;
}

.bubble-chart-container .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 16px;
}

.bubble-chart-container .chart-header h4 {
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.bubble-legend {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.bubble-legend .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6b7280;
    background: #f9fafb;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.bubble-legend .legend-label {
    font-weight: 500;
}

.chart-canvas-container {
    position: relative;
    height: 700px;
    margin-top: 16px;
    overflow: visible; /* 允许气泡完全显示 */
}

.chart-canvas-container canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
    /* 确保canvas内容不超出边界 */
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* 通用Canvas元素边界限制 */
canvas {
    /* 确保所有canvas元素都不会超出其容器 */
    max-width: 100% !important;
    max-height: 100% !important;
    box-sizing: border-box;
}

/* 气泡图专用Canvas设置 */
#personnelBubbleChart,
#productBubbleChart,
#modalBubbleChart {
    /* 允许气泡完全显示 */
    overflow: visible !important;
    /* 移除裁剪确保气泡不被截断 */
}

/* 气泡图容器的严格边界控制 */
.bubble-chart-container,
.chart-canvas-container {
    /* 确保容器内容被严格限制 */
    contain: layout style paint;
    will-change: auto;
}

/* 气泡图响应式设计 */
@media (max-width: 768px) {
    .bubble-charts-container {
        padding: 15px;
        margin-top: 20px;
    }

    .bubble-charts-header h3 {
        font-size: 18px;
    }

    .dual-bubble-layout {
        flex-direction: column;
        gap: 16px;
    }

    .bubble-chart-container {
        margin-bottom: 0;
    }

    .chart-canvas-container {
        height: 550px;
    }

    .bubble-legend {
        gap: 8px;
    }

    .legend-label {
        font-size: 11px;
        padding: 3px 6px;
    }

    .bubble-legend {
        justify-content: center;
        gap: 8px;
    }

    .bubble-legend .legend-item {
        font-size: 11px;
        padding: 4px 8px;
    }

    .chart-canvas-container {
        height: 600px;
    }
}

@media (max-width: 480px) {
    .bubble-charts-container {
        padding: 12px;
        margin: 15px -12px 0 -12px;
    }

    .bubble-legend {
        flex-direction: column;
        align-items: center;
        gap: 6px;
    }

    .chart-canvas-container {
        height: 550px;
    }
}

/* ==================== 气泡图弹窗样式 ==================== */

.bubble-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(2px);
}

.bubble-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.bubble-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.bubble-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.bubble-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
}

.bubble-modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.bubble-modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

/* 面包屑导航样式 */
.breadcrumb-info {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 10px 14px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #495057;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb-label {
    font-weight: 600;
    color: #6c757d;
    margin-right: 8px;
    white-space: nowrap;
}

.breadcrumb-path {
    font-family: 'Courier New', monospace;
    color: #007bff;
    font-weight: 500;
    word-break: break-all;
    flex: 1;
}

.bubble-modal-description {
    margin-bottom: 20px;
}

.bubble-modal-description p {
    margin: 0 0 12px 0;
    color: #6b7280;
    font-size: 14px;
}

.bubble-legend {
    background: #f8fafc;
    padding: 12px 16px;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

.legend-item {
    font-size: 13px;
    color: #374151;
    font-weight: 500;
}

.bubble-chart-container {
    margin: 20px 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    position: relative;
    height: 720px;
    overflow: visible; /* 允许气泡完全显示 */
}

.bubble-chart-container canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
    /* 确保canvas不会超出父容器 */
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.bubble-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
    margin-top: 20px;
}

.modal-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #6b7280;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-message {
    font-size: 16px;
    margin-bottom: 20px;
    color: #374151;
}

/* 医院列表弹窗样式 */
.hospital-list-modal {
    max-width: 1200px;
}

.hospital-list-summary {
    background: #f0f9ff;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #0ea5e9;
}

.hospital-list-summary p {
    margin: 0 0 8px 0;
    color: #0c4a6e;
    font-size: 14px;
}

.hospital-list-summary p:last-child {
    margin-bottom: 0;
}

.hospital-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
}

.hospital-list-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.hospital-list-table thead {
    background: #f9fafb;
    position: sticky;
    top: 0;
    z-index: 1;
}

.hospital-list-table th,
.hospital-list-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.hospital-list-table th {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.hospital-list-table tbody tr:hover {
    background-color: #f9fafb;
}

.hospital-name {
    font-weight: 500;
    color: #1f2937;
}

.sales-amount {
    font-weight: 600;
    color: #065f46 !important; /* 深绿灰色 - 销售额 */
}

.sales-quantity {
    font-weight: 500;
    color: #1e40af !important; /* 深蓝灰色 - 销量 */
}

.grv-value {
    font-weight: 600;
    color: #6b21a8 !important; /* 深紫灰色 - GRV差额 */
}

.gr-value {
    font-weight: 600;
    color: #c2410c !important; /* 深橙灰色 - GR百分比 */
}

.percentage,
.cumulative-percentage {
    font-weight: 500;
    color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .bubble-modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .bubble-modal-header {
        padding: 16px 20px;
    }

    .bubble-modal-body {
        padding: 20px;
    }

    .bubble-chart-container {
        height: 600px;
        padding: 12px;
    }

    .hospital-list-table {
        font-size: 12px;
    }

    .hospital-list-table th,
    .hospital-list-table td {
        padding: 8px 12px;
    }

    .bubble-modal-actions {
        flex-direction: column;
    }

    .bubble-modal-actions .btn {
        width: 100%;
    }
}

/* 医院销售对比表格控制按钮样式 */
.hospital-comparison-controls {
    margin: 20px 0;
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.hospital-comparison-controls .btn {
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hospital-comparison-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.hospital-comparison-controls .btn i {
    margin-right: 8px;
}

/* 医院销售对比表格样式 */
.hospital-comparison-table-section {
    grid-column: 1 / -1;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hospital-comparison-table-container {
    margin-top: 20px;
}

.table-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-group label {
    font-weight: 600;
    color: #374151;
    white-space: nowrap;
}

.control-group .form-select {
    min-width: 120px;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #374151;
}

.control-group .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-group .btn-primary {
    background: #3b82f6;
    color: white;
}

.control-group .btn-primary:hover {
    background: #2563eb;
}

/* 医院分组样式 */
.hospital-group {
    margin-bottom: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.group-header:hover {
    background: #f3f4f6;
}

.group-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #374151;
    font-size: 16px;
}

.expand-icon {
    font-size: 14px;
    color: #6b7280;
    transition: transform 0.2s ease;
}

.expand-icon.expanded {
    transform: rotate(90deg);
}

.group-sales {
    font-weight: 600;
    color: #059669;
    font-size: 14px;
}

.group-content {
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.group-content.collapsed {
    max-height: 0;
}

.group-content:not(.collapsed) {
    max-height: none;
    padding: 20px;
}

/* 医院对比表格样式 */
.hospital-comparison-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;
}

.hospital-comparison-table th,
.hospital-comparison-table td {
    padding: 12px 15px;
    text-align: center;
    border: 1px solid #e5e7eb;
    vertical-align: middle;
}

.hospital-comparison-table th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.hospital-comparison-table tbody tr:hover {
    background-color: #f9fafb;
}

.hospital-comparison-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.hospital-comparison-table tbody tr:nth-child(even):hover {
    background-color: #f3f4f6;
}

/* 医院名称列样式 */
.hospital-comparison-table td:first-child {
    text-align: left;
    font-weight: 500;
    color: #1f2937;
    max-width: 200px;
    word-wrap: break-word;
}

/* 产品列样式 */
.hospital-comparison-table td:nth-child(2) {
    text-align: left;
    font-weight: 500;
    color: #374151;
    max-width: 150px;
    word-wrap: break-word;
}

/* 数值列样式 */
.hospital-comparison-table .sales-amount {
    font-weight: 600;
    color: #065f46 !important; /* 深绿灰色 - 销售额 */
}

.hospital-comparison-table .sales-quantity {
    font-weight: 500;
    color: #1e40af !important; /* 深蓝灰色 - 销量 */
}

.hospital-comparison-table .growth-positive {
    color: #059669;
    font-weight: 600;
}

.hospital-comparison-table .growth-negative {
    color: #dc2626;
    font-weight: 600;
}

.hospital-comparison-table .growth-neutral {
    color: #6b7280;
    font-weight: 500;
}

/* 汇总信息样式 */
.summary-info {
    display: flex;
    gap: 30px;
    padding: 15px 20px;
    background: #f0f9ff;
    border-radius: 8px;
    margin-top: 20px;
    border-left: 4px solid #0ea5e9;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-label {
    font-weight: 500;
    color: #0c4a6e;
    font-size: 14px;
}

.summary-value {
    font-weight: 700;
    color: #0c4a6e;
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .table-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .control-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .control-group .form-select {
        min-width: auto;
    }

    .group-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .summary-info {
        flex-direction: column;
        gap: 15px;
    }

    .hospital-comparison-table {
        font-size: 12px;
    }

    .hospital-comparison-table th,
    .hospital-comparison-table td {
        padding: 8px 10px;
    }
}

/* 双层饼图样式 */
.pie-chart-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: visible;
    transition: all 0.3s ease;
}

/* 销售员占比图表样式 - 优化圆形效果 */
.chart-item-pie-large {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.chart-item-pie-large canvas {
    display: block;
    margin: 0 auto;
    /* 确保canvas保持正方形，获得完美圆形 */
    aspect-ratio: 1 / 1;
    max-width: 280px !important; /* 增大饼图尺寸，充分利用空间 */
    max-height: 280px !important; /* 增大饼图尺寸，充分利用空间 */
    width: auto !important;
    height: auto !important;
    z-index: 1;
}

.chart-legend-small {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
    max-height: 100px;
    overflow-y: auto;
    font-size: 10px;
}

/* 第一行图表样式 - 3个图表每个占用2列 */
.chart-item:nth-child(1),
.chart-item:nth-child(2),
.chart-item:nth-child(3) {
    grid-column: span 2 !important; /* 每个占用2列，总共6列，3个图表平均分布 */
    min-height: 280px !important;
    padding: 15px !important;
    overflow: visible !important;
    position: relative;
    box-sizing: border-box;
}

/* 第二行图表样式 - 2个图表每个占用3列，铺满整行 */
.chart-item-pie-large,
.chart-item:nth-child(4),
.chart-item:nth-child(5) {
    grid-column: span 3 !important; /* 每个占用3列，总共6列，2个图表平均分布 */
    min-height: 400px !important; /* 增加高度，给图表更多空间 */
    padding: 20px !important; /* 增加内边距 */
    overflow: visible !important;
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .charts-container {
        grid-template-columns: repeat(4, 1fr) !important;
    }

    .chart-item:nth-child(1),
    .chart-item:nth-child(2),
    .chart-item:nth-child(3) {
        grid-column: span 1 !important; /* 小屏幕时每个占用1列 */
    }

    .chart-item-pie-large,
    .chart-item:nth-child(4),
    .chart-item:nth-child(5) {
        grid-column: span 2 !important; /* 小屏幕时占用两列 */
        min-height: 280px !important;
    }

    .chart-item-pie-large canvas,
    .product-share-chart-container canvas,
    .sales-rep-chart-container canvas {
        max-width: 200px !important; /* 中等屏幕时适中尺寸 */
        max-height: 200px !important;
        width: 200px !important;
        height: 200px !important;
    }
}

@media (max-width: 768px) {
    .charts-container {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .chart-item:nth-child(1),
    .chart-item:nth-child(2),
    .chart-item:nth-child(3) {
        grid-column: span 1 !important;
    }

    .chart-item-pie-large,
    .chart-item:nth-child(4),
    .chart-item:nth-child(5) {
        grid-column: span 2 !important; /* 移动端占满整行 */
        min-height: 280px !important;
        padding: 15px !important;
    }

    .chart-item-pie-large canvas,
    .product-share-chart-container canvas,
    .sales-rep-chart-container canvas {
        max-width: 180px !important; /* 移动端适中尺寸 */
        max-height: 180px !important;
        width: 180px !important;
        height: 180px !important;
    }
}

.chart-legend-small .legend-item {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    background: #f8f9fa;
    border-radius: 3px;
    font-size: 10px;
    min-height: 24px;
    line-height: 1.2;
}

.chart-legend-small .legend-color {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.chart-legend-small .legend-label {
    font-size: 9px;
    font-weight: 500;
}

.chart-legend-small .legend-value,
.chart-legend-small .legend-percentage {
    font-size: 8px;
}

.pie-chart-section .section-header {
    padding: 20px 20px 0 20px;
    margin-bottom: 0;
}

.pie-chart-section .section-header h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.pie-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 30px;
    padding: 0 20px 20px 20px;
    min-height: 360px;
    overflow: visible;
}

.chart-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-legend {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 15px;
    width: 100%;
    max-width: 1000px;
    justify-content: center;
    align-items: flex-start;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    min-height: 44px;
    flex: 1;
    min-width: 180px;
    white-space: nowrap;
}

.legend-item:hover {
    background: #e9ecef;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.legend-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.legend-label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.legend-value {
    color: #6c757d;
    font-size: 12px;
}

.legend-percentage {
    color: #495057;
    font-size: 13px;
    font-weight: 500;
}

/* 中等屏幕响应式设计 */
@media (max-width: 1024px) and (min-width: 769px) {
    .chart-legend {
        max-width: 700px;
    }

    .legend-item {
        min-width: 150px;
        flex: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pie-chart-section .section-header {
        padding: 15px 15px 0 15px;
    }

    .pie-chart-section .section-header h3 {
        font-size: 16px;
        margin-bottom: 12px;
    }

    .pie-chart-container {
        gap: 20px;
        padding: 0 15px 15px 15px;
        min-height: auto;
    }

    .chart-wrapper canvas {
        max-width: 300px;
        max-height: 300px;
    }

    .chart-legend {
        flex-direction: column;
        max-width: 100%;
        max-height: 250px;
        gap: 12px;
    }

    .legend-item {
        min-height: 40px;
        padding: 5px 8px;
        gap: 8px;
        min-width: auto;
        flex: 1;
    }

    .legend-label {
        font-size: 13px;
    }

    .legend-value {
        font-size: 11px;
    }

    .legend-percentage {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .pie-chart-section .section-header {
        padding: 12px 12px 0 12px;
    }

    .pie-chart-section .section-header h3 {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .pie-chart-container {
        padding: 0 12px 12px 12px;
        gap: 15px;
    }

    .chart-wrapper canvas {
        max-width: 250px;
        max-height: 250px;
    }

    .chart-legend {
        max-height: 200px;
        gap: 10px;
    }

    .legend-item {
        min-width: auto;
        padding: 4px 6px;
    }
}

/* 自定义滚动条样式 */
.chart-legend::-webkit-scrollbar {
    width: 6px;
}

.chart-legend::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chart-legend::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chart-legend::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 医院分组样式 */
.hospital-group-section {
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hospital-group-header {
    background: #e2e8f0;
    color: #374151;
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.hospital-group-header:hover {
    background: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(203, 213, 225, 0.3);
}

.toggle-icon {
    width: 20px;
    height: 20px;
    background: rgba(55, 65, 81, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.3s ease;
}

.group-title {
    font-size: 16px;
    font-weight: 700;
}

.group-stats {
    font-size: 14px;
    opacity: 0.9;
    margin-left: auto;
}

.hospital-group-content {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

/* 新的统一医院销售对比表格样式 */
.hospital-comparison-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
    margin: 0;
    min-width: 1200px; /* 确保表格有足够宽度 */
}

/* 表格容器支持横向滚动 */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 三层表头样式 */
.hospital-comparison-table th {
    background: #e2e8f0;
    padding: 8px 12px;
    text-align: center;
    font-weight: 600;
    color: #374151;
    border: 1px solid #cbd5e1;
    white-space: nowrap;
    vertical-align: middle;
    position: relative;
}

/* 第一层表头 - 产品名称 */
.hospital-comparison-table .hospital-name-header {
    background: #e2e8f0;
    color: #374151;
    font-weight: 700;
    font-size: 15px;
    position: sticky;
    left: 0;
    z-index: 10;
    min-width: 120px;
}

.hospital-comparison-table .product-header {
    background: #e2e8f0;
    color: #374151;
    font-weight: 600;
    font-size: 14px;
    border-bottom: 2px solid #cbd5e1;
}

/* 第二层表头 - 时期分类 */
.hospital-comparison-table .period-header {
    background: #e2e8f0;
    color: #374151;
    font-weight: 500;
    font-size: 13px;
}

/* 第三层表头 - 具体指标 */
.hospital-comparison-table .amount-header,
.hospital-comparison-table .quantity-header,
.hospital-comparison-table .metric-header {
    background: #e2e8f0;
    color: #374151;
    font-weight: 500;
    font-size: 12px;
    padding: 6px 8px;
}

/* 数据单元格样式 */
.hospital-comparison-table td {
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    color: #1e293b;
    text-align: center;
    vertical-align: middle;
    font-size: 13px;
}

/* 医院名称列（固定列）*/
.hospital-comparison-table .hospital-name-cell {
    text-align: left;
    font-weight: 600;
    background: #f9fafb;
    border-right: 2px solid #d1d5db;
    position: sticky;
    left: 0;
    z-index: 5;
    min-width: 120px;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 销售额列样式 - 柔和绿色系 */
.hospital-comparison-table .sales-amount {
    font-weight: 600;
    color: #065f46 !important; /* 深绿灰色 */
    background-color: #f0fdf4;
}

/* 销量列样式 - 柔和蓝色系 */
.hospital-comparison-table .sales-quantity {
    font-weight: 500;
    color: #1e40af !important; /* 深蓝灰色 */
    background-color: #eff6ff;
}

/* GRV差额列样式 - 柔和紫色系 */
.hospital-comparison-table .grv-value {
    font-weight: 600;
    color: #6b21a8 !important; /* 深紫灰色 */
    background-color: #faf5ff;
}

/* GR百分比列样式 - 柔和橙色系 */
.hospital-comparison-table .gr-value {
    font-weight: 600;
    color: #c2410c !important; /* 深橙灰色 */
    background-color: #fff7ed;
}

/* 无数据样式 */
.hospital-comparison-table .no-data {
    color: #9ca3af;
    font-style: italic;
    background-color: #f9fafb;
}

/* 增长率颜色样式 */
.hospital-comparison-table .growth-positive {
    color: #059669;
    background-color: #ecfdf5;
    font-weight: 600;
}

.hospital-comparison-table .growth-negative {
    color: #dc2626;
    background-color: #fef2f2;
    font-weight: 600;
}

.hospital-comparison-table .growth-neutral {
    color: #6b7280;
    background-color: #f9fafb;
    font-weight: 500;
}

/* 表格行悬停效果 */
.hospital-comparison-table tbody tr:hover {
    background: #f0f9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hospital-comparison-table tbody tr:hover .hospital-name-cell {
    background: #e0f2fe;
}

/* 表格响应式设计 */
@media (max-width: 1024px) {
    .hospital-comparison-table {
        font-size: 12px;
        min-width: 1000px;
    }

    .hospital-comparison-table th,
    .hospital-comparison-table td {
        padding: 6px 8px;
    }

    .hospital-comparison-table .hospital-name-cell {
        min-width: 100px;
        max-width: 100px;
    }
}

@media (max-width: 768px) {
    .hospital-comparison-table {
        font-size: 11px;
        min-width: 900px;
    }

    .hospital-comparison-table th,
    .hospital-comparison-table td {
        padding: 4px 6px;
    }

    .hospital-comparison-table .hospital-name-cell {
        min-width: 80px;
        max-width: 80px;
        font-size: 10px;
    }

    .hospital-comparison-table .product-header {
        font-size: 12px;
    }

    .hospital-comparison-table .period-header {
        font-size: 11px;
    }

    .hospital-comparison-table .amount-header,
    .hospital-comparison-table .quantity-header,
    .hospital-comparison-table .metric-header {
        font-size: 10px;
        padding: 4px 6px;
    }
}

@media (max-width: 480px) {
    .hospital-comparison-table {
        font-size: 10px;
        min-width: 800px;
    }

    .hospital-comparison-table th,
    .hospital-comparison-table td {
        padding: 3px 4px;
    }

    .hospital-comparison-table .hospital-name-cell {
        min-width: 70px;
        max-width: 70px;
        font-size: 9px;
    }
}

/* 产品销售占比图 - 响应式设计 */
@media (max-width: 768px) {
    .product-share-wrapper {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .product-share-chart-container {
        width: 240px;
        height: 240px;
    }

    .product-legend-cards {
        max-height: 200px;
        width: 100%;
        max-width: 400px;
    }

    .product-legend-card {
        min-height: 45px;
        padding: 8px 10px;
        font-size: 11px;
    }
}
