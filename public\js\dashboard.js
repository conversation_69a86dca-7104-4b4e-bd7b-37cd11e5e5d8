// 初始化监控组件
const dataValidator = new DataValidator({ 
    debugMode: true, 
    strictMode: false 
});

const debugMonitor = new DebugMonitor({ 
    enabled: true, 
    logLevel: 'debug',
    keyProducts: ['美思信', '思考林', '申捷', '申维', '雷兰']
});

const performanceMonitor = new PerformanceMonitor({ 
    enabled: true,
    apiResponseThreshold: 5000,
    dataProcessingThreshold: 2000,
    renderingThreshold: 1000
});

const dataCorrector = new DataCorrector({
    debugMode: true,
    autoCorrect: true
});

document.addEventListener('DOMContentLoaded', function() {
    // DOM 元素
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const hamburgerBtn = document.getElementById('hamburgerBtn');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const overlay = document.getElementById('overlay');
    const logoutBtn = document.getElementById('logoutBtn');
    const desktopLogoutBtn = document.getElementById('desktopLogoutBtn');
    const navLinks = document.querySelectorAll('.nav-link, .desktop-nav-link');
    const contentSections = document.querySelectorAll('.content-section');
    const pageTitle = document.getElementById('pageTitle');

    // 用户信息元素
    const userName = document.getElementById('userName');
    const userRole = document.getElementById('userRole');
    const desktopUserName = document.getElementById('desktopUserName');
    const desktopUserRole = document.getElementById('desktopUserRole');

    // 统计数据元素
    const totalSales = document.getElementById('totalSales');
    const totalOrders = document.getElementById('totalOrders');
    const avgOrderValue = document.getElementById('avgOrderValue');
    const topProducts = document.getElementById('topProducts');
    const topCustomers = document.getElementById('topCustomers');
    const regionStats = document.getElementById('regionStats');
    const monthlyTrend = document.getElementById('monthlyTrend');

    // 检查登录状态
    checkAuth();

    // 初始化应用
    init();

    function init() {
        // 设置事件监听器
        setupEventListeners();

        // 加载用户信息
        loadUserInfo();

        // 初始化导航状态
        initNavigationState();

        // 首页默认加载用户年度数据汇总
        loadDashboardData();

        // 初始加载销售指标对比图表
        setTimeout(() => {
            console.log('开始初始加载销售指标对比图表');
            loadSalesTargetComparisonChart({});

            // 强制显示容器进行测试
            setTimeout(() => {
                const container = document.getElementById('salesTargetComparisonContainer');
                if (container) {
                    console.log('强制显示销售指标对比容器');
                    container.style.display = 'block';
                }
            }, 2000);
        }, 1000);

        // 初始化医院销售对比表格
        setTimeout(() => {
            console.log('初始化医院销售对比表格');
            if (typeof initHospitalComparisonTable === 'function') {
                initHospitalComparisonTable();
            }
        }, 1500);

        // 检查屏幕尺寸
        checkScreenSize();

        // 确保图表布局正确
        setTimeout(() => {
            if (typeof forceChartsLayout === 'function') {
                forceChartsLayout();
            }
        }, 500);

        // 监听窗口大小变化，重新应用布局
        window.addEventListener('resize', () => {
            setTimeout(() => {
                if (typeof forceChartsLayout === 'function') {
                    forceChartsLayout();
                }
            }, 100);
        });
    }

    function initNavigationState() {
        // 确保侧边栏和顶部导航栏的默认激活状态同步
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const isAdmin = userInfo.role === 'admin';
        const defaultPage = 'sales'; // 所有用户默认显示销售数据页面

        console.log('初始化导航状态，用户角色:', isAdmin ? '管理员' : '普通用户', '默认页面:', defaultPage);

        // 移除所有激活状态
        document.querySelectorAll('.nav-item.active, .desktop-nav-item.active').forEach(item => {
            item.classList.remove('active');
        });

        // 激活默认页面的导航项
        const sidebarNavItem = document.querySelector(`.nav-link[data-page="${defaultPage}"]`);
        const desktopNavItem = document.querySelector(`.desktop-nav-link[data-page="${defaultPage}"]`);

        if (sidebarNavItem) {
            sidebarNavItem.parentElement.classList.add('active');
        }
        if (desktopNavItem) {
            desktopNavItem.parentElement.classList.add('active');
        }

        // 显示默认页面内容
        showPage(defaultPage);
        updatePageTitle(defaultPage);
    }

    function setupEventListeners() {
        // 汉堡菜单按钮
        hamburgerBtn.addEventListener('click', toggleSidebar);

        // 侧边栏关闭按钮
        sidebarToggle.addEventListener('click', closeSidebar);

        // 遮罩层点击
        overlay.addEventListener('click', closeSidebar);

        // 退出登录
        logoutBtn.addEventListener('click', logout);
        if (desktopLogoutBtn) {
            desktopLogoutBtn.addEventListener('click', logout);
        }

        // 导航链接
        navLinks.forEach(link => {
            link.addEventListener('click', handleNavigation);
        });

        // 窗口大小变化
        window.addEventListener('resize', checkScreenSize);

        // ESC键关闭侧边栏
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeSidebar();
                closeUserModal();
                closePasswordModal();
            }
        });

        // 设置用户管理功能
        setupUserManagement();

        // 清空冗余数据按钮
        const cleanRedundantDataBtn = document.getElementById('cleanRedundantDataDashboard');
        if (cleanRedundantDataBtn) {
            cleanRedundantDataBtn.addEventListener('click', cleanRedundantData);
        }
    }

    function checkAuth() {
        const token = localStorage.getItem('authToken');
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        // 验证token
        fetch('/api/user', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Token无效');
            }
            return response.json();
        })
        .catch(error => {
            console.error('认证失败:', error);
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            window.location.href = '/login.html';
        });
    }

    function loadUserInfo() {
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        if (userInfo.username) {
            // 更新侧边栏用户信息
            if (userName) userName.textContent = userInfo.username;
            if (userRole) userRole.textContent = userInfo.role === 'admin' ? '管理员' : '用户';

            // 更新顶部导航栏用户信息
            if (desktopUserName) desktopUserName.textContent = userInfo.username;
            if (desktopUserRole) desktopUserRole.textContent = userInfo.role === 'admin' ? '管理员' : '用户';

            // 根据用户角色显示/隐藏管理员专用菜单
            const adminOnlyItems = document.querySelectorAll('.admin-only');
            console.log('=== 用户角色检查 ===');
            console.log('用户信息:', userInfo);
            console.log('用户角色:', userInfo.role);
            console.log('找到的admin-only元素数量:', adminOnlyItems.length);
            
            if (userInfo.role === 'admin') {
                adminOnlyItems.forEach((item, index) => {
                    item.style.display = 'block';
                    console.log(`显示admin-only元素 ${index + 1}:`, item);
                });
                console.log('管理员用户，显示所有管理员专用功能');
            } else {
                adminOnlyItems.forEach((item, index) => {
                    item.style.display = 'none';
                    console.log(`隐藏admin-only元素 ${index + 1}:`, item);
                });
                console.log('普通用户，隐藏管理员专用功能，但保持所有分析功能可用，销售员筛选器默认选中自己');
            }
        }
    }

    function showInitialDashboardState() {
        // 清空统计卡片
        if (totalSales) totalSales.textContent = '¥0';
        if (totalOrders) totalOrders.textContent = '0';
        if (avgOrderValue) avgOrderValue.textContent = '¥0';

        // 隐藏所有图表容器，等待用户选择筛选条件
        hideMonthlyComparisonChart();
        hideMonthlyMomChart();
        hideSalesTargetComparisonChart();
        console.log('初始状态：已隐藏所有图表，等待用户选择筛选条件');
    }

    // 图表功能已移除

    async function loadDashboardData() {
        // 仪表盘数据加载已禁用，图表功能已移除
        console.log('仪表盘数据加载已禁用');
        return;
    }

    function updateDashboardStats(data) {
        // 格式化数字
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            }).format(amount);
        };

        const formatNumber = (num) => {
            return new Intl.NumberFormat('zh-CN').format(num);
        };

        // 更新统计数据 - 适配新的数据结构
        if (totalSales) totalSales.textContent = formatCurrency(data.totalSales || 0);
        if (totalOrders) totalOrders.textContent = formatNumber(data.totalRecords || 0);
        if (avgOrderValue) {
            const avgValue = data.totalRecords > 0 ? (data.totalSales / data.totalRecords) : 0;
            avgOrderValue.textContent = formatCurrency(avgValue);
        }

        // 更新总销量（如果有对应的元素）
        const totalQuantityElement = document.getElementById('totalQuantity');
        if (totalQuantityElement) {
            totalQuantityElement.textContent = formatNumber(data.totalQuantity || 0) + ' 盒';
        }
    }

    function updateTopProducts(products) {
        // 检查元素是否存在，如果不存在则直接返回
        if (!topProducts) {
            console.log('topProducts元素不存在，跳过更新');
            return;
        }

        if (!products || products.length === 0) {
            topProducts.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }

        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            }).format(amount);
        };

        const formatNumber = (num) => {
            return new Intl.NumberFormat('zh-CN').format(num);
        };

        const html = products.map(product => `
            <div class="product-item">
                <div class="product-info">
                    <div class="product-name">${product.产品}</div>
                    <div class="product-stats">销量: ${formatNumber(product.total_quantity)} 盒 | 订单: ${product.order_count}</div>
                </div>
                <div class="product-revenue">${formatCurrency(product.total_revenue)}</div>
            </div>
        `).join('');

        topProducts.innerHTML = html;
    }

    function updateTopCustomers(customers) {
        if (!customers || customers.length === 0) {
            topCustomers.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }

        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            }).format(amount);
        };

        const html = customers.map(customer => `
            <div class="customer-item">
                <div class="customer-info">
                    <div class="customer-name">${customer.医院}</div>
                    <div class="customer-code">编码: ${customer.最终客户编码}</div>
                    <div class="customer-stats">订单: ${customer.order_count}</div>
                </div>
                <div class="customer-revenue">${formatCurrency(customer.total_revenue)}</div>
            </div>
        `).join('');

        topCustomers.innerHTML = html;
    }

    function updateRegionStats(regions) {
        if (!regions || regions.length === 0) {
            regionStats.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }

        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            }).format(amount);
        };

        const formatNumber = (num) => {
            return new Intl.NumberFormat('zh-CN').format(num);
        };

        const html = regions.map(region => `
            <div class="region-item">
                <div class="region-info">
                    <div class="region-name">${region.城市}</div>
                    <div class="region-stats">订单: ${region.order_count} | 销量: ${formatNumber(region.total_quantity)} 盒</div>
                </div>
                <div class="region-revenue">${formatCurrency(region.total_sales)}</div>
            </div>
        `).join('');

        regionStats.innerHTML = html;
    }

    function updateMonthlyTrend(trends) {
        if (!trends || trends.length === 0) {
            monthlyTrend.innerHTML = '<div class="no-data">暂无数据</div>';
            return;
        }

        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            }).format(amount);
        };

        const formatNumber = (num) => {
            return new Intl.NumberFormat('zh-CN').format(num);
        };

        const html = trends.map(trend => `
            <div class="trend-item">
                <div class="trend-info">
                    <div class="trend-period">${trend.年份}年${String(trend.月份).slice(-2)}月</div>
                    <div class="trend-stats">销量: ${formatNumber(trend.monthly_quantity)} 盒</div>
                </div>
                <div class="trend-revenue">${formatCurrency(trend.monthly_sales)}</div>
            </div>
        `).join('');

        monthlyTrend.innerHTML = html;
    }

    function toggleSidebar() {
        const isActive = sidebar.classList.contains('active');

        if (isActive) {
            closeSidebar();
        } else {
            openSidebar();
        }
    }

    function openSidebar() {
        sidebar.classList.add('active');
        overlay.classList.add('active');
        hamburgerBtn.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    function closeSidebar() {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
        hamburgerBtn.classList.remove('active');
        document.body.style.overflow = '';
    }

    function handleNavigation(e) {
        e.preventDefault();

        const link = e.currentTarget;
        const page = link.getAttribute('data-page');

        // 更新导航状态 - 同时处理侧边栏和顶部导航栏
        // 移除所有激活状态
        document.querySelectorAll('.nav-item.active, .desktop-nav-item.active').forEach(item => {
            item.classList.remove('active');
        });

        // 添加激活状态到当前项
        link.parentElement.classList.add('active');

        // 如果点击的是侧边栏链接，同步激活顶部导航栏对应项
        if (link.classList.contains('nav-link')) {
            const desktopNavItem = document.querySelector(`.desktop-nav-link[data-page="${page}"]`);
            if (desktopNavItem) {
                desktopNavItem.parentElement.classList.add('active');
            }
        }

        // 如果点击的是顶部导航栏链接，同步激活侧边栏对应项
        if (link.classList.contains('desktop-nav-link')) {
            const sidebarNavItem = document.querySelector(`.nav-link[data-page="${page}"]`);
            if (sidebarNavItem) {
                sidebarNavItem.parentElement.classList.add('active');
            }
        }

        // 更新页面内容
        showPage(page);

        // 更新页面标题
        updatePageTitle(page);

        // 在移动端关闭侧边栏
        if (window.innerWidth < 1024) {
            closeSidebar();
        }
    }

    function showPage(page) {
        // 隐藏所有内容区域
        contentSections.forEach(section => {
            section.classList.remove('active');
        });

        // 显示目标页面
        const targetSection = document.getElementById(`${page}-content`);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // 根据页面加载相应数据
        switch(page) {
            case 'sales':
                loadSalesData();
                break;
            case 'users':
                loadUsersData();
                break;
            case 'import':
                initImportPage();
                break;
        }
    }

    function updatePageTitle(page) {
        const titles = {
            'dashboard': '仪表盘',
            'sales': '销售数据',
            'import': '数据导入',
            'users': '用户管理',
            'settings': '设置'
        };

        pageTitle.textContent = titles[page] || '仪表盘';
    }

    function loadSalesData() {
        console.log('🚀 === 页面初始化：开始加载筛选选项 ===');
        
        // 加载筛选选项
        loadFilterOptions();
        


        // 不自动加载销售数据表格，等待用户筛选
        clearSalesTable();

        // 隐藏所有图表容器，等待用户选择筛选条件
        hideMonthlyComparisonChart();
        hideMonthlyMomChart();
        hideSalesTargetComparisonChart();

        // 设置筛选器事件
        setupSalesFilters();

        // 初始化多选筛选器
        initMultiSelectFilters();

        // 初始化销售员筛选框状态（针对普通用户）
        initRepFilterForNormalUser();

        // 初始化联动查询逻辑
        initLinkedFilters();

        console.log('销售数据页面加载完成，联动查询已初始化');
    }
    
    // 初始化销售员筛选框状态（针对普通用户）
    function initRepFilterForNormalUser() {
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const isAdmin = userInfo.role === 'admin';
        
        if (!isAdmin) {
            console.log('普通用户登录，销售员筛选器保持完整功能，默认选中自己');
            // 普通用户也可以使用完整的筛选功能，只是默认选中自己
            // 不再禁用任何UI元素
        }
    }

    function clearSalesTable() {
        const salesTableBody = document.getElementById('salesTableBody');
        if (salesTableBody) {
            salesTableBody.innerHTML = '<tr><td colspan="7" class="no-data">请选择筛选条件后点击搜索查看数据</td></tr>';
        }

        // 清空分页
        const pagination = document.getElementById('pagination');
        if (pagination) {
            pagination.innerHTML = '';
        }
    }

    async function loadFilterOptions() {
        const token = localStorage.getItem('authToken');
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

        try {
            console.log('🔄 === 开始加载筛选选项 ===');
            console.log('👤 当前用户:', userInfo.username, '角色:', userInfo.role);
            console.log('🔑 Token 存在:', !!token);
            
            const response = await fetch('/api/sales/filters', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            console.log('📡 API 响应状态:', response.status, response.statusText);

            if (!response.ok) {
                console.error('❌ 筛选选项请求失败:', response.status, response.statusText);
                throw new Error('获取筛选选项失败');
            }

            const data = await response.json();
            console.log('📦 收到的完整响应数据:', data);
            
            if (data.success) {
                console.log('✅ API 调用成功，开始填充筛选选项');
                console.log('📊 数据摘要:');
                if (data.data) {
                    Object.keys(data.data).forEach(key => {
                        const count = data.data[key] ? data.data[key].length : 0;
                        console.log(`  - ${key}: ${count} 条记录`);
                        if (key === 'months' && count === 0) {
                            console.warn('⚠️ 月份数据为空！');
                        }
                    });
                }
                
                populateFilterOptions(data.data);
                
                // 检查月份选项是否成功加载
                setTimeout(() => {
                    const monthOptions = document.getElementById('monthOptions');
                    const monthCount = monthOptions ? monthOptions.querySelectorAll('label').length : 0;
                    
                    if (monthCount === 0) {
                        console.warn('❌ 月份选项加载失败，启用备用方案');
                        loadDefaultMonthOptions();
                    } else {
                        console.log('✅ 月份选项加载成功，共', monthCount, '个选项');
                    }
                }, 500);
                
            } else {
                console.error('❌ API 返回 success: false');
                loadDefaultMonthOptions();
            }
        } catch (error) {
            console.error('💥 加载筛选选项失败:', error);
            console.error('错误详情:', error.stack);
            
            // 如果API失败，尝试加载基本的月份选项
            console.log('🔄 API失败，尝试加载默认月份选项');
            loadDefaultMonthOptions();
        }
    }

    // 加载默认月份选项（当API失败时使用）
    function loadDefaultMonthOptions() {
        console.log('📅 === 加载默认月份选项 ===');
        
        // 生成最近12个月的选项
        const currentDate = new Date();
        const defaultMonths = [];
        
        for (let i = 11; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const monthValue = `${year}${month}`;
            
            defaultMonths.push({
                月份: monthValue
            });
        }
        
        console.log('🗓️ 生成的默认月份选项:', defaultMonths);
        
        // 填充到页面
        populateMonthOptions(defaultMonths);
        
        // 显示用户提示
        showMonthLoadingNotification('使用默认月份选项，如需完整数据请刷新页面');
    }
    
    // 显示月份加载通知
    function showMonthLoadingNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 120px;
            right: 20px;
            background: #f39c12;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // 防抖函数
    let updateRelatedFiltersTimeout;
    let isUpdatingRelatedFilters = false; // 防止无限循环

    function debounceUpdateRelatedFilters() {
        if (isUpdatingRelatedFilters) {
            console.log('⏸️ 正在更新关联筛选器，跳过此次触发');
            return;
        }

        console.log('🚀 === 触发联动查询（防抖） ===');
        const currentFilters = getSelectedFilters();
        console.log('🔍 当前筛选条件快照:', JSON.stringify(currentFilters));

        clearTimeout(updateRelatedFiltersTimeout);
        updateRelatedFiltersTimeout = setTimeout(() => {
            console.log('⏰ 防抖延迟结束，开始执行联动查询...');
            updateRelatedFilters();
        }, 300); // 300ms 防抖
    }

    // 更新关联筛选器选项
    async function updateRelatedFilters() {
        if (isUpdatingRelatedFilters) {
            console.log('已在更新关联筛选器，跳过');
            return;
        }

        isUpdatingRelatedFilters = true;
        const token = localStorage.getItem('authToken');

        try {
            // 获取当前选择的筛选条件
            const currentFilters = getSelectedFilters();
            console.log('=== 联动查询更新筛选器 ===');
            console.log('当前筛选条件:', currentFilters);

            // 检查月份是否已选择（月份必选）
            if (!currentFilters.months || currentFilters.months.length === 0) {
                console.log('月份未选择，禁用其他筛选器');
                disableNonMonthFilters();
                return;
            }

            // 月份已选择，启用其他筛选器
            enableNonMonthFilters();

            console.log('发送联动查询请求，基于已选择条件:', JSON.stringify(currentFilters));
            const response = await fetch('/api/sales/filters/related', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(currentFilters)
            });

            if (!response.ok) {
                console.error('联动查询请求失败:', response.status, response.statusText);
                const errorText = await response.text();
                console.error('错误详情:', errorText);
                return;
            }

            const data = await response.json();
            console.log('联动查询响应:', data);

            if (data.success) {
                console.log('开始联动更新筛选选项...');
                // 只更新未选择的筛选器选项，保持已选择的不变
                updateUnselectedFilterOptions(data.data, currentFilters);
                console.log('联动查询完成，筛选选项已更新');
            }
        } catch (error) {
            console.error('联动查询失败:', error);
        } finally {
            isUpdatingRelatedFilters = false;
        }
    }

    // 只更新未选择的筛选器选项（联动查询核心逻辑）
    function updateUnselectedFilterOptions(filters, currentFilters) {
        console.log('=== 联动更新未选择的筛选器选项 ===');
        console.log('当前筛选条件:', currentFilters);
        console.log('服务器返回的筛选器数据:', filters);

        // 检查哪些筛选器已经有选择
        const hasMonthSelection = currentFilters.months && currentFilters.months.length > 0;
        const hasRepSelection = currentFilters.reps && currentFilters.reps.length > 0;
        const hasRegionProductLineSelection = currentFilters.regionProductLines && currentFilters.regionProductLines.length > 0;
        const hasProductSelection = currentFilters.products && currentFilters.products.length > 0;
        const hasTerminalSelection = currentFilters.terminals && currentFilters.terminals.length > 0;
        const hasCitySelection = currentFilters.cities && currentFilters.cities.length > 0;
        const hasManagerSelection = currentFilters.managers && currentFilters.managers.length > 0;

        console.log('筛选器选择状态:', {
            month: hasMonthSelection ? '已选择' : '未选择',
            rep: hasRepSelection ? '已选择' : '未选择',
            regionProductLine: hasRegionProductLineSelection ? '已选择' : '未选择',
            product: hasProductSelection ? '已选择' : '未选择',
            terminal: hasTerminalSelection ? '已选择' : '未选择',
            city: hasCitySelection ? '已选择' : '未选择',
            manager: hasManagerSelection ? '已选择' : '未选择'
        });

        // 联动更新：只更新未选择的筛选器
        if (!hasRepSelection && filters.reps) {
            console.log('🔄 联动更新销售员选项，数量:', filters.reps.length);
            populateRepOptions(filters.reps);
        } else if (hasRepSelection) {
            console.log('⏭️ 销售员已选择，跳过更新');
        }

        if (!hasRegionProductLineSelection && filters.regionProductLines) {
            console.log('🔄 联动更新地区产品线选项，数量:', filters.regionProductLines.length);
            populateRegionProductLineOptions(filters.regionProductLines);
        } else if (hasRegionProductLineSelection) {
            console.log('⏭️ 地区产品线已选择，跳过更新');
        }

        if (!hasProductSelection && filters.products) {
            console.log('🔄 联动更新产品选项，数量:', filters.products.length);
            populateProductOptions(filters.products);
        } else if (hasProductSelection) {
            console.log('⏭️ 产品已选择，跳过更新');
        }

        if (!hasTerminalSelection && filters.terminals) {
            console.log('🔄 联动更新医院选项，数量:', filters.terminals.length);
            populateTerminalOptions(filters.terminals);
        } else if (hasTerminalSelection) {
            console.log('⏭️ 医院已选择，跳过更新');
        }

        if (!hasCitySelection && filters.cities) {
            console.log('🔄 联动更新城市选项，数量:', filters.cities.length);
            populateCityOptions(filters.cities);
        } else if (hasCitySelection) {
            console.log('⏭️ 城市已选择，跳过更新');
        }

        if (!hasManagerSelection && filters.managers) {
            console.log('🔄 联动更新经理选项，数量:', filters.managers.length);
            populateManagerOptions(filters.managers);
        } else if (hasManagerSelection) {
            console.log('⏭️ 经理已选择，跳过更新');
        }

        // 注意：不需要重新初始化所有事件，因为每个populate函数都会重新绑定自己的事件
        console.log('✅ 联动查询完成，所有未选择的筛选器已更新');
    }

    // 保存当前选择状态
    function saveCurrentSelections() {
        const selections = {};
        const multiSelects = ['product', 'month', 'quarter', 'terminal', 'rep', 'regionProductLine', 'city', 'manager'];

        multiSelects.forEach(type => {
            selections[type] = getMultiSelectValues(type);
        });

        return selections;
    }

    // 恢复选择状态
    function restoreSelections(selections) {
        const multiSelects = ['product', 'month', 'quarter', 'terminal', 'rep', 'regionProductLine'];

        multiSelects.forEach(type => {
            if (selections[type] && selections[type].length > 0) {
                const dropdown = document.getElementById(`${type}Dropdown`);
                if (!dropdown) return;

                const checkboxes = dropdown.querySelectorAll('.options-list input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    if (selections[type].includes(checkbox.value)) {
                        checkbox.checked = true;
                    }
                });

                updateMultiSelectDisplay(type);
                updateSelectAllState(type);
            }
        });
    }

    function populateFilterOptions(filters) {
        console.log('🎯 === 填充筛选器选项开始 ===');
        console.log('接收到的筛选器数据:', filters);
        
        // 获取当前用户信息
        const currentUser = JSON.parse(localStorage.getItem('userInfo') || '{}');
        console.log('👤 当前用户:', currentUser.username, '角色:', currentUser.role);
        
        // 填充多选产品选项
        if (filters.products) {
            console.log('📦 产品选项数量:', filters.products.length);
            populateProductOptions(filters.products);
        } else {
            console.warn('⚠️ 未收到产品选项数据');
        }

        // 填充单选产品选项（如果存在）
        const productFilter = document.getElementById('productFilter');
        if (productFilter && filters.products) {
            productFilter.innerHTML = '<option value="">全部产品</option>';
            filters.products.forEach(item => {
                const option = document.createElement('option');
                option.value = item.产品;
                option.textContent = item.产品;
                productFilter.appendChild(option);
            });
        }

        // 填充月份选项 - 添加详细调试
        console.log('📅 === 月份选项填充检查 ===');
        console.log('filters.months 存在吗?', !!filters.months);
        console.log('filters.months 类型:', typeof filters.months);
        console.log('filters.months 内容:', filters.months);
        
        if (filters.months) {
            console.log('✅ 开始填充月份选项，数量:', filters.months.length);
            populateMonthOptions(filters.months);
        } else {
            console.error('❌ 月份选项数据为空或未定义！');
            console.log('完整的filters对象键:', Object.keys(filters));
        }

        // 填充季度选项 - 已移除季度筛选器，注释掉相关代码
        // console.log('Filters received:', filters);
        // if (filters.quarters) {
        //     console.log('Loading quarter options:', filters.quarters);
        //     populateQuarterOptions(filters.quarters);
        // } else {
        //     console.log('No quarters data in filters');
        // }

        // 填充销售员选项（根据用户角色）
        console.log('=== 销售员选项填充检查 ===');
        console.log('filters.reps 存在吗?', !!filters.reps);
        console.log('filters.reps 内容:', filters.reps);
        
        // 获取当前用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        console.log('当前用户信息:', userInfo);
        console.log('用户角色:', userInfo.role);
        
        if (filters.reps) {
            console.log('正在填充销售员选项，数量:', filters.reps.length);
            console.log('销售员列表:', filters.reps.map(r => r.新REP));
            populateRepOptions(filters.reps);
            
            if (userInfo.role === 'admin') {
                console.log('管理员用户：显示所有销售员选项');
            } else {
                console.log('普通用户：只显示匹配的销售员选项');
                console.log('用户匹配字段 (full_name):', userInfo.full_name);
            }
        } else {
            console.log('没有销售员数据，可能的原因：');
            console.log('1. 用户权限不足');
            console.log('2. 数据库中没有匹配的销售员记录');
            console.log('3. API查询出错');
        }

        // 填充地区产品线选项
        if (filters.regionProductLines) {
            console.log('正在填充地区产品线选项:', filters.regionProductLines);
            populateRegionProductLineOptions(filters.regionProductLines);
        } else {
            console.log('没有地区产品线数据，等待后端添加该字段');
        }

        // 填充城市选项
        if (filters.cities) {
            console.log('正在填充城市选项:', filters.cities);
            populateCityOptions(filters.cities);
        } else {
            console.log('没有城市数据');
        }

        // 填充经理选项
        if (filters.managers) {
            console.log('正在填充经理选项:', filters.managers);
            populateManagerOptions(filters.managers);
        } else {
            console.log('没有经理数据');
        }

        // 重新初始化多选筛选器事件（确保新加载的选项有正确的事件绑定）
        setTimeout(() => {
            initMultiSelectEvents();

            // 确保在选项填充后，非月份筛选器仍然被禁用
            console.log('选项填充完成，重新应用禁用逻辑...');
            const monthValues = getMultiSelectValues('month');
            if (!monthValues || monthValues.length === 0) {
                console.log('月份未选择，确保其他筛选器被禁用');
                disableNonMonthFilters();
            }
        }, 100);
    }

    function populateMonthOptions(months) {
        console.log('📅 开始填充月份选项，数量:', months?.length || 0);
        
        const monthOptions = document.getElementById('monthOptions');
        if (!monthOptions) {
            console.error('❌ 找不到 monthOptions 元素！');
            return;
        }

        monthOptions.innerHTML = '';
        
        if (!Array.isArray(months) || months.length === 0) {
            console.warn('⚠️ 月份数据为空或不是数组');
            return;
        }
        months.forEach((item, index) => {
            const label = document.createElement('label');
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = item.月份;

            // 格式化月份显示
            let displayText = item.月份;
            if (String(item.月份).length === 6) {
                // 如果是6位数格式 (如202401)，格式化为 "2024年01月"
                const year = String(item.月份).substring(0, 4);
                const month = String(item.月份).substring(4, 6);
                displayText = `${year}年${month}月`;
            }

            const span = document.createElement('span');
            span.textContent = ' ' + displayText;

            label.appendChild(checkbox);
            label.appendChild(span);
            monthOptions.appendChild(label);
        });

        // 重新绑定事件
        const checkboxes = monthOptions.querySelectorAll('input[type="checkbox"]');
        console.log(`🔗 为 ${checkboxes.length} 个月份复选框绑定事件`);
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                console.log('🔄 月份选择发生变化，触发联动查询');
                updateMultiSelectDisplay('month');
                updateSelectAllState('month');

                // 月份选择变化时的特殊处理
                handleMonthSelectionChange();

                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });

        console.log('✅ 月份选项填充完成，共', months.length, '个选项，事件已绑定');
        
        // 验证DOM元素是否正确创建
        const finalOptions = monthOptions.querySelectorAll('label');
        console.log('🔍 最终创建的月份选项数量:', finalOptions.length);
    }

    // 处理月份选择变化
    function handleMonthSelectionChange() {
        const selectedMonths = getMultiSelectValues('month');
        const monthDisplay = document.getElementById('monthDisplay');

        if (selectedMonths.length > 0) {
            console.log('月份已选择:', selectedMonths);

            // 恢复月份显示样式
            if (monthDisplay) {
                const placeholder = monthDisplay.querySelector('.placeholder');
                if (placeholder) {
                    placeholder.style.color = '';
                    placeholder.style.fontWeight = '';
                }
            }
        } else {
            console.log('月份未选择');

            // 恢复月份必选提示
            if (monthDisplay) {
                const placeholder = monthDisplay.querySelector('.placeholder');
                if (placeholder) {
                    placeholder.textContent = '请先选择月份（必选）';
                    placeholder.style.color = '#e74c3c';
                    placeholder.style.fontWeight = 'bold';
                }
            }
        }
    }

    function populateQuarterOptions(quarters) {
        console.log('populateQuarterOptions called with:', quarters);
        const quarterOptions = document.getElementById('quarterOptions');
        if (!quarterOptions) {
            console.error('quarterOptions element not found');
            return;
        }

        quarterOptions.innerHTML = '';
        quarters.forEach(item => {
            console.log('Adding quarter option:', item);
            const label = document.createElement('label');
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = item.季度;

            const span = document.createElement('span');
            span.textContent = ' ' + item.季度;

            label.appendChild(checkbox);
            label.appendChild(span);
            quarterOptions.appendChild(label);
        });

        // 重新绑定事件
        quarterOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                updateMultiSelectDisplay('quarter');
                updateSelectAllState('quarter');
                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });
    }

    function populateProductOptions(products) {
        const productOptions = document.getElementById('productOptions');
        if (!productOptions) return;

        productOptions.innerHTML = '';

        products.forEach(item => {
            const label = document.createElement('label');
            // 使用产品名称作为值和显示文本（因为后端按产品名称查询）
            const productName = item.产品;
            label.innerHTML = `
                <input type="checkbox" value="${productName}">
                <span>${productName}</span>
            `;
            productOptions.appendChild(label);
        });

        // 重新绑定事件
        productOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                console.log('🔄 产品选择发生变化，触发联动查询');
                updateMultiSelectDisplay('product');
                updateSelectAllState('product');
                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });

        console.log('✅ 产品选项填充完成，共', products.length, '个选项，事件已绑定');
    }

    function populateRepOptions(reps) {
        const repOptions = document.getElementById('repOptions');
        console.log('查找repOptions元素:', repOptions);
        if (!repOptions) {
            console.error('repOptions元素未找到');
            return;
        }

        repOptions.innerHTML = '';
        console.log('正在处理销售员数据:', reps);

        // 获取当前用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const isAdmin = userInfo.role === 'admin';
        const currentUserRepCode = userInfo.full_name || userInfo.username;

        reps.forEach(item => {
            const label = document.createElement('label');
            const repName = item.新REP;
            console.log('添加销售员:', repName);
            
            // 对于普通用户，默认选中自己，但允许修改
            const isCurrentUserRep = !isAdmin && repName === currentUserRepCode;
            const isChecked = isCurrentUserRep ? 'checked' : '';
            
            label.innerHTML = `
                <input type="checkbox" value="${repName}" ${isChecked}>
                <span>${repName}</span>
            `;
            repOptions.appendChild(label);
        });

        // 重新绑定事件
        repOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                console.log('🔄 销售员选择发生变化，触发联动查询');
                updateMultiSelectDisplay('rep');
                updateSelectAllState('rep');
                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });
        
        // 更新显示和状态
        updateMultiSelectDisplay('rep');
        updateSelectAllState('rep');
        
        console.log('销售员选项填充完成，共', reps.length, '个选项');
        
        if (!isAdmin) {
            console.log('普通用户，已默认选中自己的销售员选项:', currentUserRepCode);
            console.log('✅ 普通用户享有完整的数据分析功能，可自由调整筛选条件');
            
            // 显示用户友好提示
            setTimeout(() => {
                const notification = document.createElement('div');
                notification.className = 'user-notification';
                notification.style.cssText = `
                    position: fixed;
                    top: 70px;
                    right: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 9999;
                    max-width: 300px;
                    animation: slideInRight 0.3s ease;
                `;
                notification.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-info-circle" style="font-size: 16px;"></i>
                        <div>
                            <div style="font-weight: bold; margin-bottom: 4px;">功能提示</div>
                            <div style="font-size: 12px; opacity: 0.9;">您享有完整的数据分析功能，销售员筛选器已默认选中您的账户</div>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" 
                                style="background: none; border: none; color: white; cursor: pointer; font-size: 18px; padding: 0; margin-left: auto;">×</button>
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                // 5秒后自动隐藏
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.style.animation = 'slideOutRight 0.3s ease';
                        setTimeout(() => notification.remove(), 300);
                    }
                }, 5000);
            }, 1000);
        } else {
            console.log('管理员用户，销售员筛选框保持正常功能');
        }
        
        // 确保所有用户的销售员筛选框功能都正常
        const repMultiSelect = document.getElementById('repMultiSelect');
        const repDisplay = document.getElementById('repDisplay');
        const repDropdown = document.getElementById('repDropdown');
        const selectAllReps = document.getElementById('selectAllReps');
        
        if (repDisplay) {
            repDisplay.style.pointerEvents = 'auto';
            repDisplay.style.opacity = '1';
            repDisplay.style.cursor = 'pointer';
        }
        
        if (selectAllReps) {
            selectAllReps.disabled = false;
            selectAllReps.parentElement.style.opacity = '1';
            selectAllReps.parentElement.style.cursor = 'pointer';
        }
        
        if (repDropdown) {
            repDropdown.style.pointerEvents = 'auto';
            repDropdown.style.opacity = '1';
        }
    }

    function populateRegionProductLineOptions(regionProductLines) {
        console.log('=== populateRegionProductLineOptions 被调用 ===');
        console.log('传入的地区产品线数据:', regionProductLines);
        
        const regionProductLineOptions = document.getElementById('regionProductLineOptions');
        console.log('查找regionProductLineOptions元素:', regionProductLineOptions);
        
        if (!regionProductLineOptions) {
            console.error('regionProductLineOptions元素未找到');
            return;
        }

        regionProductLineOptions.innerHTML = '';
        console.log('清空了regionProductLineOptions内容');

        regionProductLines.forEach((item, index) => {
            const label = document.createElement('label');
            const regionProductLine = item.地区产品线 || item.regionProductLine;
            console.log(`添加地区产品线 ${index + 1}:`, regionProductLine);
            label.innerHTML = `
                <input type="checkbox" value="${regionProductLine}">
                <span>${regionProductLine}</span>
            `;
            regionProductLineOptions.appendChild(label);
        });

        // 重新绑定事件
        regionProductLineOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                console.log('🔄 地区产品线选择发生变化，触发联动查询');
                updateMultiSelectDisplay('regionProductLine');
                updateSelectAllState('regionProductLine');
                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });
        
        console.log('地区产品线选项填充完成，共', regionProductLines.length, '个选项');
        console.log('最终HTML内容:', regionProductLineOptions.innerHTML);
    }

    function populateCityOptions(cities) {
        console.log('=== populateCityOptions 被调用 ===');
        console.log('传入的城市数据:', cities);

        const cityOptions = document.getElementById('cityOptions');
        console.log('查找cityOptions元素:', cityOptions);

        if (!cityOptions) {
            console.error('cityOptions元素未找到');
            return;
        }

        cityOptions.innerHTML = '';
        console.log('清空了cityOptions内容');

        cities.forEach((item, index) => {
            const label = document.createElement('label');
            const city = item.城市 || item.city;
            console.log(`添加城市 ${index + 1}:`, city);
            label.innerHTML = `
                <input type="checkbox" value="${city}">
                <span>${city}</span>
            `;
            cityOptions.appendChild(label);
        });

        // 重新绑定事件
        cityOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                console.log('🔄 城市选择发生变化，触发联动查询');
                updateMultiSelectDisplay('city');
                updateSelectAllState('city');
                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });

        console.log('城市选项填充完成，共', cities.length, '个选项');
        console.log('最终HTML内容:', cityOptions.innerHTML);
    }

    function populateManagerOptions(managers) {
        console.log('=== populateManagerOptions 被调用 ===');
        console.log('传入的经理数据:', managers);

        const managerOptions = document.getElementById('managerOptions');
        console.log('查找managerOptions元素:', managerOptions);

        if (!managerOptions) {
            console.error('managerOptions元素未找到');
            return;
        }

        managerOptions.innerHTML = '';
        console.log('清空了managerOptions内容');

        managers.forEach((item, index) => {
            const label = document.createElement('label');
            const manager = item.经理 || item.manager;
            console.log(`添加经理 ${index + 1}:`, manager);
            label.innerHTML = `
                <input type="checkbox" value="${manager}">
                <span>${manager}</span>
            `;
            managerOptions.appendChild(label);
        });

        // 重新绑定事件
        managerOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                console.log('🔄 经理选择发生变化，触发联动查询');
                updateMultiSelectDisplay('manager');
                updateSelectAllState('manager');
                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });

        console.log('经理选项填充完成，共', managers.length, '个选项');
        console.log('最终HTML内容:', managerOptions.innerHTML);
    }

    async function loadSalesTable(page = 1, filters = {}) {
        const token = localStorage.getItem('authToken');
        const salesTableBody = document.getElementById('salesTableBody');

        // 先清空销售数据表格
        console.log('清空销售数据表格...');
        clearSalesTable();

        if (salesTableBody) {
            salesTableBody.innerHTML = '<tr><td colspan="7" class="loading">加载中...</td></tr>';
        }

        try {
            const params = new URLSearchParams({
                page: page,
                limit: 20,
                ...filters
            });

            const response = await fetch(`/api/sales?${params}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (!response.ok) {
                throw new Error('获取销售数据失败');
            }

            const data = await response.json();

            if (data.success) {
                console.log('销售数据样本:', data.data.slice(0, 3)); // 调试信息
                renderSalesTable(data.data);
                renderPagination(data.pagination);
            }
        } catch (error) {
            console.error('加载销售数据失败:', error);
            if (salesTableBody) {
                salesTableBody.innerHTML = '<tr><td colspan="7" class="error">加载数据失败</td></tr>';
            }
        }
    }

    function renderSalesTable(salesData) {
        const salesTableBody = document.getElementById('salesTableBody');

        if (!salesTableBody) return;

        if (!salesData || salesData.length === 0) {
            salesTableBody.innerHTML = '<tr><td colspan="7" class="no-data">暂无数据</td></tr>';
            return;
        }

        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(amount || 0);
        };

        const formatNumber = (num) => {
            return new Intl.NumberFormat('zh-CN').format(num || 0);
        };

        // 格式化月份显示
        const formatMonth = (monthValue) => {
            if (!monthValue) return '-';
            const monthStr = String(monthValue);
            // 如果是6位数格式 (如202401)，提取月份部分
            if (monthStr.length === 6) {
                return monthStr.slice(-2);
            }
            // 如果是1-2位数，直接使用
            return monthStr.padStart(2, '0');
        };

        const html = salesData.map(item => `
            <tr>
                <td>${item.产品 || '-'}</td>
                <td title="${item.医院}">${(item.医院 || '-').length > 20 ? (item.医院 || '-').substring(0, 20) + '...' : (item.医院 || '-')}</td>
                <td>${item.城市 || '-'}</td>
                <td>${formatNumber(item.销量盒折算后)}</td>
                <td>${formatCurrency(item.销售金额折算后)}</td>
                <td>${item.年份}年${formatMonth(item.月份)}月</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn-small action-btn-view" onclick="viewSalesDetail('${item.产品编码concat}', '${item.最终客户编码}')">
                            查看
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        salesTableBody.innerHTML = html;
    }

    function renderPagination(pagination) {
        const paginationInfo = document.getElementById('paginationInfo');
        const paginationControls = document.getElementById('paginationControls');

        if (!paginationInfo || !paginationControls) return;

        // 更新分页信息
        const start = (pagination.page - 1) * pagination.limit + 1;
        const end = Math.min(pagination.page * pagination.limit, pagination.total);
        paginationInfo.textContent = `显示 ${start} - ${end} 条，共 ${pagination.total} 条记录`;

        // 生成分页按钮
        let paginationHTML = '';

        // 上一页按钮
        paginationHTML += `
            <button class="pagination-btn" ${pagination.page <= 1 ? 'disabled' : ''}
                    onclick="changePage(${pagination.page - 1})">
                上一页
            </button>
        `;

        // 页码按钮
        const maxVisiblePages = 5;
        let startPage = Math.max(1, pagination.page - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === pagination.page ? 'active' : ''}"
                        onclick="changePage(${i})">
                    ${i}
                </button>
            `;
        }

        // 下一页按钮
        paginationHTML += `
            <button class="pagination-btn" ${pagination.page >= pagination.totalPages ? 'disabled' : ''}
                    onclick="changePage(${pagination.page + 1})">
                下一页
            </button>
        `;

        paginationControls.innerHTML = paginationHTML;
    }

    function setupSalesFilters() {
        const searchBtn = document.getElementById('searchBtn');
        const resetBtn = document.getElementById('resetBtn');

        if (searchBtn) {
            searchBtn.addEventListener('click', performSearch);
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', resetFilters);
        }
    }



    function performSearch() {
        const filters = getSelectedFilters();
        console.log('=== 执行搜索 ===');
        console.log('筛选条件:', filters);
        console.log('筛选条件JSON:', JSON.stringify(filters));

        // 验证月份必选
        if (!filters.months || filters.months.length === 0) {
            alert('请先选择月份！');
            console.log('搜索失败：月份未选择');
            return;
        }

        // 先清空所有数据表格和图表
        console.log('清空现有数据...');
        clearSalesTable();
        resetProductAnalysisTable();

        hideMonthlyComparisonChart();
        hideMonthlyMomChart();
        hideSalesTargetComparisonChart();

        // 重新加载所有数据
        console.log('重新加载数据...');

        // 加载销售数据表格
        loadSalesTable(1, filters);

        // 加载产品销售分析数据（包含产品同期销售额对比表和产品环比销售对比）
        loadProductAnalysisData(filters);

        // 加载月份对比柱状图数据
        window.loadMonthlyComparisonChart(filters);

        // 加载月份环比增长图表数据
        window.loadMonthlyMomChart(filters);

        // 加载医院销量占比图表数据
        loadHospitalComparisonChart(filters);
        loadHospitalMomChart(filters);

        // 加载销售指标对比图表数据
        loadSalesTargetComparisonChart(filters);
    }

    function resetFilters() {
        // 重置多选筛选器
        resetMultiSelectFilter('product');
        resetMultiSelectFilter('month');
        resetMultiSelectFilter('terminal');
        resetMultiSelectFilter('regionProductLine');
        
        // 销售员筛选器需要特别处理
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const isAdmin = userInfo.role === 'admin';
        
        if (isAdmin) {
            // 管理员可以完全重置销售员筛选器
            resetMultiSelectFilter('rep');
        } else {
            // 普通用户重置后需要重新选中自己
            resetMultiSelectFilter('rep');
            
            // 延迟重新选中自己的选项
            setTimeout(() => {
                const currentUserRepCode = userInfo.full_name || userInfo.username;
                const repDropdown = document.getElementById('repDropdown');
                if (repDropdown) {
                    const userCheckbox = repDropdown.querySelector(`input[value="${currentUserRepCode}"]`);
                    if (userCheckbox) {
                        userCheckbox.checked = true;
                        updateMultiSelectDisplay('rep');
                        updateSelectAllState('rep');
                        console.log('重置后重新选中普通用户的销售员选项:', currentUserRepCode);
                        console.log('💡 提示：您可以取消销售员选择来查看整体数据，系统会在查询时自动确保数据安全');
                    }
                }
            }, 100);
        }

        // 重置表格和汇总数据
        resetProductAnalysisTable();

        resetSummaryCards();

        // 清空销售数据表格，等待用户重新筛选
        clearSalesTable();

        // 隐藏月份对比柱状图
        hideMonthlyComparisonChart();

        // 隐藏销售指标对比图表
        hideSalesTargetComparisonChart();
        
        console.log('筛选器已重置，销售指标对比图表已隐藏');
    }





    function getMultiSelectValues(type) {
        const dropdown = document.getElementById(`${type}Dropdown`);
        if (!dropdown) return [];

        const checkboxes = dropdown.querySelectorAll('.options-list input[type="checkbox"]:checked');
        const values = Array.from(checkboxes).map(cb => cb.value);
        console.log(`${type} 选中的值:`, values);
        return values;
    }
    
    // 暴露到全局作用域
    window.getMultiSelectValues = getMultiSelectValues;

    function resetMultiSelectFilter(type) {
        const dropdown = document.getElementById(`${type}Dropdown`);
        if (!dropdown) return;

        // 取消所有选择
        const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = false);

        // 更新显示
        updateMultiSelectDisplay(type);
        updateSelectAllState(type);
    }

    // 加载产品销售分析数据
    async function loadProductAnalysisData(filters = {}) {
        const token = localStorage.getItem('authToken');
        const productAnalysisBody = document.getElementById('productAnalysisBody');

        // 记录数据处理开始
        debugMonitor.logDataProcessing('LOAD_PRODUCT_ANALYSIS_START', filters, {
            productName: 'All Products',
            filtersApplied: Object.keys(filters).length
        });

        // 先清空产品分析表格和相关图表
        console.log('清空产品分析表格和图表...');
        resetProductAnalysisTable();

        hideAchievementDonutChart();

        if (productAnalysisBody) {
            productAnalysisBody.innerHTML = '<tr><td colspan="8" class="loading">加载中...</td></tr>';
        }

        try {
            console.log('发送销售分析请求，筛选条件:', JSON.stringify(filters));

            // 使用性能监控器监控API调用
            const data = await performanceMonitor.monitorAPICall('sales-analysis', async () => {
                const response = await fetch('/api/sales-analysis', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    body: JSON.stringify({
                        ...filters,
                        _timestamp: Date.now() // 添加时间戳避免缓存
                    })
                });

                if (!response.ok) {
                    throw new Error('获取销售分析数据失败');
                }

                return await response.json();
            }, { debug: true });

            console.log('销售分析数据响应:', data);
            console.log('👥 人员数据详情:', data.data?.personnel);
            console.log('👥 人员数据数量:', data.data?.personnel?.length || 0);
            if (data.data?.personnel && data.data.personnel.length > 0) {
                console.log('👥 人员数据样本:', data.data.personnel.slice(0, 3));
            }
            
            // 修复API响应数据
            const correctedData = dataCorrector.correctAPIResponse(data);
            if (correctedData._corrected) {
                console.log('API响应数据已修复:', correctedData._corrections);
                debugMonitor.logDataProcessing('API_DATA_CORRECTED', correctedData._corrections, {
                    productName: 'All Products'
                });
            }

            // 验证修复后的API响应数据结构
            const apiValidation = dataValidator.validateAPIResponse(correctedData);
            if (!apiValidation.isValid) {
                console.error('API响应数据验证失败:', apiValidation.errors);
                debugMonitor.logDataProcessing('API_VALIDATION_FAILED', apiValidation, {
                    productName: 'All Products'
                });
            }

            // 添加数据验证日志
            if (correctedData.data && correctedData.data.products) {
                console.log('产品数据详情:');
                
                // 验证产品数据
                const productValidation = dataValidator.validateProductAnalysisData(correctedData.data.products);
                debugMonitor.logDataProcessing('PRODUCT_DATA_VALIDATION', productValidation, {
                    productName: 'All Products',
                    totalProducts: correctedData.data.products.length,
                    validProducts: productValidation.validProductCount
                });

                // 获取数据修复统计
                const correctionStats = dataCorrector.getCorrectionStats(correctedData.data.products);
                debugMonitor.logDataProcessing('DATA_CORRECTION_STATS', correctionStats, {
                    productName: 'All Products'
                });

                // 特别关注美思信产品
                correctedData.data.products.forEach(product => {
                    if (product.name === '美思信') {
                        console.log('美思信API响应数据:', {
                            name: product.name,
                            actual: product.actual,
                            target: product.target,
                            lastYearSales: product.lastYearSales,
                            lastPeriodSales: product.lastPeriodSales,
                            samePeriodGrowth: product.samePeriodGrowth,
                            sequentialGrowth: product.sequentialGrowth,
                            _corrected: product._corrected,
                            _corrections: product._corrections
                        });

                        // 验证美思信产品数据
                        const meisixinValidation = dataValidator.validateSpecificProduct(product, '美思信');
                        debugMonitor.logDataProcessing('MEISIXIN_VALIDATION', meisixinValidation, {
                            productName: '美思信'
                        });

                        // 检测美思信数据异常
                        const anomalies = debugMonitor.detectAnomalies(product);
                        if (anomalies.length > 0) {
                            console.warn('美思信产品数据异常:', anomalies);
                        }

                        // 如果美思信数据被修复，记录详细信息
                        if (product._corrected) {
                            console.log('🔧 美思信数据修复详情:', product._corrections);
                        }
                    }
                });
            }

            // 使用修复后的数据
            const finalData = correctedData;

            if (finalData.success) {
                // 使用性能监控器监控数据处理
                await performanceMonitor.monitorDataProcessing('product-analysis-processing', async (products) => {
                    // 重新渲染产品分析表格（包含产品同期销售额对比和产品环比销售对比）
                    // 传递完整的分析数据，包括销售员数据
                    renderProductAnalysisTable(products || [], finalData.data);

                    updateSummaryCards(finalData.data.summary || {});
                    // 渲染气泡图，传入产品和人员数据
                    renderBubbleChartsInDashboard(products || [], finalData.data.personnel || []);

                    return products;
                }, finalData.data.products, { debug: true });

                debugMonitor.logDataProcessing('LOAD_PRODUCT_ANALYSIS_SUCCESS', finalData.data, {
                    productName: 'All Products',
                    productsCount: finalData.data.products?.length || 0
                });
            } else {
                throw new Error(finalData.error || '获取数据失败');
            }
        } catch (error) {
            console.error('加载销售分析数据失败:', error);
            
            debugMonitor.logDataProcessing('LOAD_PRODUCT_ANALYSIS_ERROR', error, {
                productName: 'All Products',
                errorMessage: error.message
            });

            if (productAnalysisBody) {
                productAnalysisBody.innerHTML = '<tr><td colspan="8" class="error">加载数据失败: ' + error.message + '</td></tr>';
            }

            hideAchievementDonutChart();
        }
    }

    // 渲染产品销售分析表格
    function renderProductAnalysisTable(products, analysisData) {
        const productAnalysisBody = document.getElementById('productAnalysisBody');

        if (!productAnalysisBody) return;

        // 先清空表格内容
        console.log('清空产品分析表格内容...');
        productAnalysisBody.innerHTML = '';

        if (!products || products.length === 0) {
            productAnalysisBody.innerHTML = '<tr><td colspan="8" class="no-data">暂无数据</td></tr>';
            hideAchievementDonutChart();
            return;
        }

        // 过滤掉所有关键数据都为0的产品
        const filteredProducts = products.filter(product => {
            const actual = product.actual || product.actualAmount || 0;
            const target = product.target || product.targetAmount || 0;
            const lastYearSales = product.lastYearSales || product.lastYearAmount || 0;
            const lastPeriodSales = product.lastPeriodSales || product.lastPeriodAmount || 0;

            // 如果实际销售额、目标金额、去年同期、上期销售额都为0，则过滤掉
            return actual > 0 || target > 0 || lastYearSales > 0 || lastPeriodSales > 0;
        });

        if (filteredProducts.length === 0) {
            productAnalysisBody.innerHTML = '<tr><td colspan="8" class="no-data">暂无有效数据</td></tr>';
            hideAchievementDonutChart();
            return;
        }

        const formatNumber = (num) => {
            return new Intl.NumberFormat('zh-CN').format(num || 0);
        };

        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(amount || 0);
        };

        // 按实际销售金额进行降序排序
        const sortedProducts = filteredProducts.sort((a, b) => {
            const actualA = a.actual || a.actualAmount || 0;
            const actualB = b.actual || b.actualAmount || 0;
            return actualB - actualA;
        });

        const html = sortedProducts.map(product => {
            console.log(`渲染产品 ${product.name}:`, {
                actual: product.actual,
                target: product.target,
                lastYearSales: product.lastYearSales,
                lastPeriodSales: product.lastPeriodSales,
                samePeriodGrowth: product.samePeriodGrowth,
                sequentialGrowth: product.sequentialGrowth
            });

            // 修改达成率计算逻辑：当指标为0时显示"-"
            let achievementDisplay, achievementColor;
            if (product.target > 0) {
                const achievementRate = (product.actual / product.target * 100).toFixed(1);
                achievementDisplay = achievementRate + '%';
                achievementColor = parseFloat(achievementRate) >= 100 ? '#22c55e' : '#ef4444';
            } else {
                achievementDisplay = '-';
                achievementColor = '#9ca3af'; // 灰色
            }

            // 使用后端返回的增长率数据，而不是重新计算
            const yoyGrowth = product.samePeriodGrowth !== undefined && product.samePeriodGrowth !== null ? product.samePeriodGrowth : '0.0%';
            const momGrowth = product.sequentialGrowth !== undefined && product.sequentialGrowth !== null ? product.sequentialGrowth : '0.0%';
            
            // 调试日志：检查增长率数据
            if (product.name === '美思信') {
                console.log('美思信产品数据调试:', {
                    name: product.name,
                    actual: product.actual,
                    lastYearSales: product.lastYearSales,
                    lastPeriodSales: product.lastPeriodSales,
                    samePeriodGrowth: product.samePeriodGrowth,
                    sequentialGrowth: product.sequentialGrowth,
                    yoyGrowth: yoyGrowth,
                    momGrowth: momGrowth
                });
            }

            return `
                <tr>
                    <td title="${product.name || product.productName}">${product.name || product.productName || '-'}</td>
                    <td>${formatCurrency(product.actual || product.actualAmount || 0)}</td>
                    <td>${formatCurrency(product.target || product.targetAmount || 0)}</td>
                    <td style="color: ${achievementColor}">${achievementDisplay}</td>
                    <td>${formatCurrency(product.lastYearSales || product.lastYearAmount || 0)}</td>
                    <td style="color: ${getGrowthColor(yoyGrowth)}">${yoyGrowth}</td>
                    <td>${formatCurrency(product.lastPeriodSales || product.lastPeriodAmount || 0)}</td>
                    <td style="color: ${getGrowthColor(momGrowth)}">${momGrowth}</td>
                </tr>
            `;
        }).join('');

        // 计算汇总数据
        const totalActual = filteredProducts.reduce((sum, product) => sum + (product.actual || product.actualAmount || 0), 0);
        const totalTarget = filteredProducts.reduce((sum, product) => sum + (product.target || product.targetAmount || 0), 0);
        const totalLastYear = filteredProducts.reduce((sum, product) => sum + (product.lastYearSales || product.lastYearAmount || 0), 0);
        const totalLastPeriod = filteredProducts.reduce((sum, product) => sum + (product.lastPeriodSales || product.lastPeriodAmount || 0), 0);



        // 计算汇总增长率 - 修改达成率逻辑
        let totalAchievementDisplay, totalAchievementColor;
        if (totalTarget > 0) {
            const totalAchievementRate = (totalActual / totalTarget * 100).toFixed(1);
            totalAchievementDisplay = totalAchievementRate + '%';
            totalAchievementColor = parseFloat(totalAchievementRate) >= 100 ? '#22c55e' : '#ef4444';
        } else {
            totalAchievementDisplay = '-';
            totalAchievementColor = '#9ca3af'; // 灰色
        }

        // 修复汇总增长率计算逻辑，与后端保持一致
        let totalYoyGrowthDisplay, totalYoyGrowthColor;
        if (totalLastYear === 0) {
            // 去年同期为0时，无法计算增长率，显示"-"
            totalYoyGrowthDisplay = '-';
            totalYoyGrowthColor = '#9ca3af'; // 灰色
        } else if (totalLastYear > 0) {
            // 去年同期为正数：正常计算
            const totalYoyGrowthRate = ((totalActual - totalLastYear) / totalLastYear * 100);
            totalYoyGrowthDisplay = (totalYoyGrowthRate >= 0 ? '+' : '') + totalYoyGrowthRate.toFixed(1) + '%';
            totalYoyGrowthColor = totalYoyGrowthRate >= 0 ? '#22c55e' : '#ef4444';
        } else {
            // 去年同期为负数：特殊处理
            if (totalActual >= 0) {
                // 从负数变为正数：改善，使用绝对值计算
                const improvementRate = ((totalActual - totalLastYear) / Math.abs(totalLastYear) * 100);
                totalYoyGrowthDisplay = '+' + improvementRate.toFixed(1) + '%';
                totalYoyGrowthColor = '#22c55e'; // 绿色，表示改善
            } else {
                // 都是负数：比较亏损程度
                const absChange = Math.abs(totalActual) - Math.abs(totalLastYear);
                const changeRate = (absChange / Math.abs(totalLastYear)) * 100;
                if (absChange > 0) {
                    // 亏损增加
                    totalYoyGrowthDisplay = '-' + changeRate.toFixed(1) + '%';
                    totalYoyGrowthColor = '#ef4444'; // 红色
                } else if (absChange < 0) {
                    // 亏损减少（改善）
                    totalYoyGrowthDisplay = '+' + Math.abs(changeRate).toFixed(1) + '%';
                    totalYoyGrowthColor = '#22c55e'; // 绿色
                } else {
                    totalYoyGrowthDisplay = '0.0%';
                    totalYoyGrowthColor = '#9ca3af'; // 灰色
                }
            }
        }

        let totalMomGrowthDisplay, totalMomGrowthColor;
        if (totalLastPeriod === 0) {
            // 上期为0时，无法计算增长率，显示"-"
            totalMomGrowthDisplay = '-';
            totalMomGrowthColor = '#9ca3af'; // 灰色
        } else if (totalLastPeriod > 0) {
            // 上期为正数：正常计算
            const totalMomGrowthRate = ((totalActual - totalLastPeriod) / totalLastPeriod * 100);
            totalMomGrowthDisplay = (totalMomGrowthRate >= 0 ? '+' : '') + totalMomGrowthRate.toFixed(1) + '%';
            totalMomGrowthColor = totalMomGrowthRate >= 0 ? '#22c55e' : '#ef4444';
        } else {
            // 上期为负数：特殊处理
            if (totalActual >= 0) {
                // 从负数变为正数：改善
                const improvementRate = ((totalActual - totalLastPeriod) / Math.abs(totalLastPeriod) * 100);
                totalMomGrowthDisplay = '+' + improvementRate.toFixed(1) + '%';
                totalMomGrowthColor = '#22c55e'; // 绿色
            } else {
                // 都是负数：比较亏损程度
                const absChange = Math.abs(totalActual) - Math.abs(totalLastPeriod);
                const changeRate = (absChange / Math.abs(totalLastPeriod)) * 100;
                if (absChange > 0) {
                    totalMomGrowthDisplay = '-' + changeRate.toFixed(1) + '%';
                    totalMomGrowthColor = '#ef4444';
                } else if (absChange < 0) {
                    totalMomGrowthDisplay = '+' + Math.abs(changeRate).toFixed(1) + '%';
                    totalMomGrowthColor = '#22c55e';
                } else {
                    totalMomGrowthDisplay = '0.0%';
                    totalMomGrowthColor = '#9ca3af';
                }
            }
        }

        // 添加汇总行
        const summaryRow = `
            <tr style="background-color: #f8f9fa; font-weight: bold; border-top: 2px solid #dee2e6;">
                <td style="font-weight: bold;">汇总</td>
                <td style="font-weight: bold;">${formatCurrency(totalActual)}</td>
                <td style="font-weight: bold;">${formatCurrency(totalTarget)}</td>
                <td style="color: ${totalAchievementColor}; font-weight: bold;">${totalAchievementDisplay}</td>
                <td style="font-weight: bold;">${formatCurrency(totalLastYear)}</td>
                <td style="color: ${totalYoyGrowthColor}; font-weight: bold;">${totalYoyGrowthDisplay}</td>
                <td style="font-weight: bold;">${formatCurrency(totalLastPeriod)}</td>
                <td style="color: ${totalMomGrowthColor}; font-weight: bold;">${totalMomGrowthDisplay}</td>
            </tr>
        `;

        productAnalysisBody.innerHTML = html + summaryRow;

        // 显示图表区域
        showChartsSection();

        // 更新图表 - 修改达成率图表逻辑
        if (totalTarget > 0) {
            const totalAchievementRate = (totalActual / totalTarget * 100).toFixed(1);
            updateAchievementDonutChart(totalAchievementRate, totalActual, totalTarget);
        } else {
            // 当指标为0时隐藏或显示空状态
            updateAchievementDonutChart(0, totalActual, totalTarget);
        }
        updateProductShareChart(filteredProducts);
        updateYoyComparisonChart(totalActual, totalLastYear);
        updateMomComparisonChart(totalActual, totalLastPeriod);

        // 显示销售员业绩占比图 - 所有用户可见
        showDoublePieChartForAdmin(analysisData);

        // 确保图表布局正确
        forceChartsLayout();
    }

    // 汇总卡片已移除，此函数不再需要
    function updateSummaryCards(summary) {
        // 汇总卡片HTML已移除，此函数保留为空以避免错误
        return;
    }

    // 重置产品分析表格
    function resetProductAnalysisTable() {
        const productAnalysisBody = document.getElementById('productAnalysisBody');
        if (productAnalysisBody) {
            productAnalysisBody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666; padding: 40px;">请选择查询条件并点击搜索</td></tr>';
        }

        // 隐藏总达成率环形图
        hideAchievementDonutChart();

        // 隐藏月份图表容器
        hideMonthlyChartsContainer();

        // 隐藏双层饼图
        hideDoublePieChart();
    }
    
    // 初始化图表布局
    setTimeout(() => {
        forceChartsLayout();
    }, 100);












    // 页面加载完成后的初始化（移除自动测试，避免干扰正常逻辑）
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成');
        // 不再自动执行测试，避免干扰正常的搜索逻辑
    });

    // 直接测试销售员表格功能
    window.testPersonnelTableDirect = function() {
        console.log('=== 直接测试销售员表格功能 ===');

        // 测试数据
        const testData = [
            {
                name: '张东明',
                actual: 1551721.405575,
                target: 1443192.7,
                lastYearSales: 1203388.87773,
                lastPeriodSales: 1294545.276625
            },
            {
                name: '杨宗祥',
                actual: 790342.644506,
                target: 825777,
                lastYearSales: 593745.415016,
                lastPeriodSales: 522676.40392749995
            },
            {
                name: '陈迪',
                actual: 727412.16872,
                target: 684298.3,
                lastYearSales: 496659.959343,
                lastPeriodSales: 477343.1736185
            },
            {
                name: '刘雯丽',
                actual: 427942.3253975,
                target: 556193.4,
                lastYearSales: 481089.2302085,
                lastPeriodSales: 447854.9645935
            }
        ];

        console.log('测试数据:', testData);
        console.log('调用renderPersonnelAnalysisTable函数...');

        try {
            renderPersonnelAnalysisTable(testData);
            console.log('测试完成');
            alert('销售员表格测试完成，请查看表格内容。\n\n提示：点击销售员名字可以查看该销售员的产品明细！');
        } catch (error) {
            console.error('测试失败:', error);
            alert('测试失败: ' + error.message);
        }
    };

    // 测试双层环形图
    window.testDoublePieChart = function() {
        console.log('=== 测试双层环形图 ===');

        // 测试数据
        const testData = [
            {
                name: '张东明',
                actual: 1551721.405575,
                target: 1443192.7,
                lastYearSales: 1203388.87773,
                lastPeriodSales: 1121234.5
            },
            {
                name: '李小华',
                actual: 1234567.89,
                target: 1500000,
                lastYearSales: 1100000,
                lastPeriodSales: 1050000
            },
            {
                name: '王大明',
                actual: 987654.32,
                target: 800000,
                lastYearSales: 750000,
                lastPeriodSales: 720000
            },
            {
                name: '赵小红',
                actual: 654321.12,
                target: 700000,
                lastYearSales: 481089.2302085,
                lastPeriodSales: 447854.9645935
            }
        ];

        console.log('双层环形图测试数据:', testData);

        try {
            // 显示饼图容器
            const pieChartItem = document.getElementById('pieChartItem');
            if (pieChartItem) {
                pieChartItem.style.display = 'block';
                pieChartItem.style.visibility = 'visible';
            }

            // 创建双层环形图
            createDoublePieChart(testData);
            console.log('双层环形图测试完成');
            alert('双层环形图测试完成！\n内圈显示销售额占比，外圈显示指标完成率。');
        } catch (error) {
            console.error('双层环形图测试失败:', error);
            alert('测试失败: ' + error.message);
        }
    };

    // 汇总卡片已移除，此函数不再需要
    function resetSummaryCards() {
        // 汇总卡片HTML已移除，此函数保留为空以避免错误
        return;
    }

    // 全局函数，供HTML调用
    window.changePage = function(page) {
        const filters = getSelectedFilters();
        loadSalesTable(page, filters);
    };

    // 动态分析表格相关函数
    window.updateDimensionHierarchy = function() {
        updateDimensionOptions();
        const selectedDimensions = getSelectedDimensionHierarchy();
        updateDimensionHint(selectedDimensions);

        if (selectedDimensions.length > 0) {
            updateDynamicTableHeader(selectedDimensions);
        } else {
            clearDynamicAnalysisTable();
        }
    };

    window.refreshDynamicAnalysis = function() {
        // 首先检查顶部筛选条件
        const filters = getSelectedFilters();
        const hasFilters = filters.products?.length > 0 ||
                          filters.months?.length > 0 ||
                          filters.quarters?.length > 0 ||
                          filters.terminals?.length > 0 ||
                          filters.reps?.length > 0 ||
                          filters.regionProductLines?.length > 0;

        if (!hasFilters) {
            alert('请先在顶部设置筛选条件（如选择产品、月份、销售员等），然后再进行多维度分析');
            return;
        }

        const selectedDimensions = getSelectedDimensionHierarchy();
        if (selectedDimensions.length > 0) {
            console.log('开始生成多维度分析...');
            console.log('使用的筛选条件:', filters);
            console.log('分析维度:', selectedDimensions);
            loadDynamicAnalysisData(selectedDimensions);
        } else {
            alert('请先选择分析维度');
        }
    };

    window.clearDimensionHierarchy = function() {
        document.getElementById('dimension1').value = '';
        document.getElementById('dimension2').value = '';
        document.getElementById('dimension3').value = '';
        document.getElementById('dimension4').value = '';
        updateDimensionOptions();
        clearDynamicAnalysisTable();
        updateDimensionHint([]);
    };

    function getSelectedDimensionHierarchy() {
        const dimensions = [];
        for (let i = 1; i <= 4; i++) {
            const select = document.getElementById(`dimension${i}`);
            if (select && select.value) {
                dimensions.push(select.value);
            }
        }
        return dimensions;
    }

    function updateDimensionOptions() {
        const allOptions = [
            { value: 'product', text: '产品' },
            { value: 'personnel', text: '销售员' },
            { value: 'month', text: '月份' },
            { value: 'terminal', text: '医院' }
        ];

        const selectedValues = getSelectedDimensionHierarchy();

        // 更新每个下拉框的选项
        for (let i = 1; i <= 4; i++) {
            const select = document.getElementById(`dimension${i}`);
            if (!select) continue;

            const currentValue = select.value;

            // 清空选项
            select.innerHTML = '<option value="">请选择</option>';

            // 添加可用选项（排除已选择的）
            allOptions.forEach(option => {
                if (!selectedValues.includes(option.value) || option.value === currentValue) {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.text;
                    if (option.value === currentValue) {
                        optionElement.selected = true;
                    }
                    select.appendChild(optionElement);
                }
            });

            // 如果当前层级之前的层级没有选择，则禁用当前层级
            if (i > 1) {
                const prevSelect = document.getElementById(`dimension${i-1}`);
                if (!prevSelect || !prevSelect.value) {
                    select.disabled = true;
                    select.value = '';
                } else {
                    select.disabled = false;
                }
            }
        }
    }

    function updateDynamicTableHeader(dimensions) {
        const thead = document.getElementById('dynamicAnalysisHead');
        if (!thead) return;

        const legendDiv = document.getElementById('hierarchyLegend');

        if (dimensions.length === 0) {
            thead.innerHTML = '<tr><th>请选择分析维度</th></tr>';
            if (legendDiv) legendDiv.style.display = 'none';
            return;
        }

        // 显示层级说明
        if (legendDiv && dimensions.length > 1) {
            legendDiv.style.display = 'block';
        } else if (legendDiv) {
            legendDiv.style.display = 'none';
        }

        let headerHtml = '<tr>';

        // 添加层级维度列
        headerHtml += '<th style="width: 300px; text-align: left; padding: 12px 8px;">分析层级</th>';

        // 添加数据列 - 使用与产品分析表相同的列名
        headerHtml += `
            <th style="text-align: right; padding: 12px 8px;">盒数</th>
            <th style="text-align: right; padding: 12px 8px;">实际销售金额折算后</th>
            <th style="text-align: right; padding: 12px 8px;">指标金额</th>
            <th style="text-align: right; padding: 12px 8px;">达成率</th>
            <th style="text-align: right; padding: 12px 8px;">同期销售</th>
            <th style="text-align: right; padding: 12px 8px;">同期增长</th>
            <th style="text-align: right; padding: 12px 8px;">上期销售</th>
            <th style="text-align: right; padding: 12px 8px;">上期增长</th>
        `;

        headerHtml += '</tr>';
        thead.innerHTML = headerHtml;
    }

    function updateDimensionHint(dimensions) {
        const hint = document.getElementById('dimensionHint');
        if (!hint) return;

        if (dimensions.length === 0) {
            hint.textContent = '请按层级顺序选择分析维度，系统将生成层级结构的分析表格';
            hint.style.color = '#6b7280';
        } else {
            const dimensionNames = dimensions.map(d => {
                switch(d) {
                    case 'product': return '产品';
                    case 'personnel': return '销售员';
                    case 'month': return '月份';
                    case 'terminal': return '医院';
                    default: return d;
                }
            });
            hint.textContent = `层级结构：${dimensionNames.join(' → ')}`;
            hint.style.color = '#059669';
        }
    }

    function clearDynamicAnalysisTable() {
        const tbody = document.getElementById('dynamicAnalysisBody');
        if (!tbody) return;

        tbody.innerHTML = `<tr><td colspan="8" style="text-align: center; padding: 40px; color: #6b7280;">
            <div style="margin-bottom: 10px;">📊 多维度分析表</div>
            <div style="font-size: 14px; margin-bottom: 8px;">1. 请先在顶部设置筛选条件（产品、月份、销售员等）</div>
            <div style="font-size: 14px; margin-bottom: 8px;">2. 然后选择分析维度</div>
            <div style="font-size: 14px;">3. 点击"生成分析"查看结果</div>
        </td></tr>`;
    }

    function loadDynamicAnalysisData(dimensions) {
        const tbody = document.getElementById('dynamicAnalysisBody');
        if (!tbody) return;

        // 显示加载状态
        tbody.innerHTML = `<tr><td colspan="9" style="text-align: center; padding: 40px; color: #6b7280;">正在加载数据...</td></tr>`;

        // 获取顶部筛选条件
        const filters = getSelectedFilters();

        console.log('=== 多维分析筛选条件检查 ===');
        console.log('获取到的筛选条件:', filters);
        console.log('products:', filters.products);
        console.log('months:', filters.months);
        console.log('quarters:', filters.quarters);
        console.log('terminals:', filters.terminals);
        console.log('reps:', filters.reps);
        console.log('regionProductLines:', filters.regionProductLines);

        // 检查是否有筛选条件
        const hasFilters = filters.products?.length > 0 ||
                          filters.months?.length > 0 ||
                          filters.quarters?.length > 0 ||
                          filters.terminals?.length > 0 ||
                          filters.reps?.length > 0 ||
                          filters.regionProductLines?.length > 0;

        console.log('是否有筛选条件:', hasFilters);

        if (!hasFilters) {
            console.log('没有筛选条件，显示提示信息');
            tbody.innerHTML = `<tr><td colspan="9" style="text-align: center; padding: 40px; color: #f59e0b;">
                <div style="margin-bottom: 10px;">⚠️ 请先在顶部设置筛选条件</div>
                <div style="font-size: 14px; color: #6b7280;">多维度分析需要基于具体的筛选条件进行分析</div>
            </td></tr>`;
            return;
        }

        console.log('多维度分析使用的筛选条件:', filters);
        console.log('分析维度:', dimensions);

        const requestData = {
            ...filters,
            dimensions: dimensions
        };

        const token = localStorage.getItem('authToken');
        fetch('/api/dynamic-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('=== 多维分析API响应 ===');
            console.log('响应数据:', data);
            if (data.success) {
                console.log('数据样本:', data.data.slice(0, 2));
                renderDynamicAnalysisTable(data.data, dimensions);
            } else {
                throw new Error(data.error || '获取数据失败');
            }
        })
        .catch(error => {
            console.error('加载动态分析数据失败:', error);
            tbody.innerHTML = `<tr><td colspan="9" style="text-align: center; padding: 40px; color: #ef4444;">加载数据失败: ${error.message}</td></tr>`;
        });
    }

    function renderDynamicAnalysisTable(data, dimensions) {
        const tbody = document.getElementById('dynamicAnalysisBody');
        if (!tbody) return;

        if (!data || data.length === 0) {
            tbody.innerHTML = `<tr><td colspan="8" style="text-align: center; padding: 40px; color: #6b7280;">暂无数据</td></tr>`;
            return;
        }

        // 保存数据引用供展开/收起功能使用
        window.currentHierarchyData = data;

        // 构建层级结构
        const hierarchyTree = buildHierarchyTree(data, dimensions);
        window.currentHierarchyTree = hierarchyTree;

        console.log('选择的维度:', dimensions);
        console.log('构建的层级树:', hierarchyTree);
        console.log('原始数据样本:', data.slice(0, 3));

        // 计算汇总数据
        const summaryData = calculateSummaryData(data);
        console.log('汇总数据:', summaryData);

        // 生成汇总行
        const summaryRow = renderSummaryRow(summaryData, dimensions);

        // 渲染层级结构（默认只显示第一层）
        const rows = renderHierarchyRows(hierarchyTree, dimensions, 0);

        // 将汇总行放在最顶部
        tbody.innerHTML = summaryRow + rows;
        console.log('动态分析表格渲染完成，共', data.length, '条记录，维度数量:', dimensions.length);
    }

    // 计算汇总数据
    function calculateSummaryData(data) {
        const summary = {
            boxes: 0,
            actual: 0,
            target: 0,
            lastYearSales: 0,
            lastPeriodSales: 0,
            achievementRate: 0,
            samePeriodGrowth: 0,
            sequentialGrowth: 0
        };

        let validSamePeriodCount = 0;
        let validSequentialCount = 0;
        let totalSamePeriodGrowth = 0;
        let totalSequentialGrowth = 0;

        data.forEach(item => {
            summary.boxes += (item.boxes || 0);
            summary.actual += (item.actual || 0);
            summary.target += (item.target || 0);
            summary.lastYearSales += (item.lastYearSales || 0);
            summary.lastPeriodSales += (item.lastPeriodSales || 0);

            // 累计增长率（用于计算平均值）
            if (item.samePeriodGrowth && item.samePeriodGrowth !== '-' && !isNaN(parseFloat(item.samePeriodGrowth))) {
                totalSamePeriodGrowth += parseFloat(item.samePeriodGrowth);
                validSamePeriodCount++;
            }
            if (item.sequentialGrowth && item.sequentialGrowth !== '-' && !isNaN(parseFloat(item.sequentialGrowth))) {
                totalSequentialGrowth += parseFloat(item.sequentialGrowth);
                validSequentialCount++;
            }
        });

        // 计算达成率
        summary.achievementRate = summary.target > 0 ? (summary.actual / summary.target * 100) : 0;

        // 计算平均增长率
        summary.samePeriodGrowth = validSamePeriodCount > 0 ? (totalSamePeriodGrowth / validSamePeriodCount) : 0;
        summary.sequentialGrowth = validSequentialCount > 0 ? (totalSequentialGrowth / validSequentialCount) : 0;

        return summary;
    }

    // 渲染汇总行
    function renderSummaryRow(summaryData, dimensions) {
        const formatNumber = (num) => {
            if (num === 0) return '¥0.0';
            return `¥${(num / 10000).toFixed(1)}万`;
        };

        const formatPercentage = (num) => {
            if (num === 0) return '0.0%';
            const sign = num >= 0 ? '+' : '';
            return `${sign}${num.toFixed(1)}%`;
        };

        const getPercentageColor = (num) => {
            if (num > 0) return 'color: #059669;'; // 绿色
            if (num < 0) return 'color: #dc2626;'; // 红色
            return 'color: #6b7280;'; // 灰色
        };

        const getAchievementColor = (rate) => {
            if (rate >= 100) return 'color: #059669;'; // 绿色
            if (rate >= 80) return 'color: #d97706;'; // 橙色
            return 'color: #dc2626;'; // 红色
        };

        return `
            <tr style="background-color: #f3f4f6; font-weight: 600; border-bottom: 2px solid #d1d5db;">
                <td style="padding: 12px 8px; border-right: 1px solid #e5e7eb;">
                    <span style="color: #374151; font-size: 14px;">📊 汇总</span>
                </td>
                <td style="padding: 12px 8px; text-align: right; border-right: 1px solid #e5e7eb; color: #374151;">
                    ${summaryData.boxes.toLocaleString()}
                </td>
                <td style="padding: 12px 8px; text-align: right; border-right: 1px solid #e5e7eb; color: #374151;">
                    ${formatNumber(summaryData.actual)}
                </td>
                <td style="padding: 12px 8px; text-align: right; border-right: 1px solid #e5e7eb; color: #374151;">
                    ${formatNumber(summaryData.target)}
                </td>
                <td style="padding: 12px 8px; text-align: right; border-right: 1px solid #e5e7eb; ${getAchievementColor(summaryData.achievementRate)}">
                    ${summaryData.achievementRate.toFixed(1)}%
                </td>
                <td style="padding: 12px 8px; text-align: right; border-right: 1px solid #e5e7eb; color: #374151;">
                    ${formatNumber(summaryData.lastYearSales)}
                </td>
                <td style="padding: 12px 8px; text-align: right; border-right: 1px solid #e5e7eb; ${getPercentageColor(summaryData.samePeriodGrowth)}">
                    ${formatPercentage(summaryData.samePeriodGrowth)}
                </td>
                <td style="padding: 12px 8px; text-align: right; border-right: 1px solid #e5e7eb; color: #374151;">
                    ${formatNumber(summaryData.lastPeriodSales)}
                </td>
                <td style="padding: 12px 8px; text-align: right; ${getPercentageColor(summaryData.sequentialGrowth)}">
                    ${formatPercentage(summaryData.sequentialGrowth)}
                </td>
            </tr>
        `;
    }

    function buildHierarchyTree(data, dimensions) {
        const tree = {};

        data.forEach(item => {
            let currentLevel = tree;

            // 按维度层级构建树结构
            dimensions.forEach((dimension, index) => {
                const key = getDimensionValue(item, dimension);

                if (!currentLevel[key]) {
                    currentLevel[key] = {
                        data: {
                            boxes: 0,
                            actual: 0,
                            target: 0,
                            lastYearSales: 0,
                            lastPeriodSales: 0,
                            samePeriodGrowth: '-',
                            sequentialGrowth: '-',
                            // 用于累加增长率计算的辅助字段
                            itemCount: 0,
                            growthRates: {
                                samePeriod: [],
                                sequential: []
                            }
                        },
                        children: {},
                        level: index,
                        dimension: dimension,
                        key: key,
                        expanded: false, // 默认收起
                        hasChildren: false
                    };
                }

                // 累加数据到当前层级
                currentLevel[key].data.boxes += (item.boxes || 0);
                currentLevel[key].data.actual += (item.actual || 0);
                currentLevel[key].data.target += (item.target || 0);
                currentLevel[key].data.lastYearSales += (item.lastYearSales || 0);
                currentLevel[key].data.lastPeriodSales += (item.lastPeriodSales || 0);

                // 直接使用后端返回的增长率数据（如果是叶子节点）
                if (index === dimensions.length - 1) {
                    // 叶子节点：直接使用后端计算的增长率
                    if (item.samePeriodGrowth && item.samePeriodGrowth !== '-') {
                        currentLevel[key].data.samePeriodGrowth = item.samePeriodGrowth;
                    }
                    if (item.sequentialGrowth && item.sequentialGrowth !== '-') {
                        currentLevel[key].data.sequentialGrowth = item.sequentialGrowth;
                    }
                }

                // 调试信息
                if (index === 0) { // 只在第一层打印调试信息
                    console.log(`累加到 ${key}:`, {
                        actual: currentLevel[key].data.actual,
                        target: currentLevel[key].data.target,
                        lastYear: currentLevel[key].data.lastYearSales,
                        lastPeriod: currentLevel[key].data.lastPeriodSales,
                        samePeriodGrowth: item.samePeriodGrowth,
                        sequentialGrowth: item.sequentialGrowth
                    });
                }

                // 如果不是最后一层，标记有子节点
                if (index < dimensions.length - 1) {
                    currentLevel[key].hasChildren = true;
                    currentLevel = currentLevel[key].children;
                }
            });
        });

        // 计算每层的增长率
        calculateHierarchyGrowthRates(tree);

        return tree;
    }

    function calculateHierarchyGrowthRates(tree) {
        Object.values(tree).forEach(node => {
            if (node.data) {
                // 使用与后端相同的增长率计算逻辑
                node.data.samePeriodGrowth = calculateGrowthRateForHierarchy(
                    node.data.actual,
                    node.data.lastYearSales
                );

                node.data.sequentialGrowth = calculateGrowthRateForHierarchy(
                    node.data.actual,
                    node.data.lastPeriodSales
                );

                // 确保数值格式正确
                node.data.actual = node.data.actual || 0;
                node.data.target = node.data.target || 0;
                node.data.lastYearSales = node.data.lastYearSales || 0;
                node.data.lastPeriodSales = node.data.lastPeriodSales || 0;
            }

            // 递归处理子节点
            if (Object.keys(node.children).length > 0) {
                calculateHierarchyGrowthRates(node.children);
            }
        });
    }

    // 与后端完全一致的增长率计算函数
    function calculateGrowthRateForHierarchy(current, previous) {
        // 情况1：去年同期为0
        if (previous === 0) {
            // 无论当期是多少，都显示"-"，因为无法计算增长率
            return "-";
        }

        // 情况2：去年同期为正数（正常情况）
        if (previous > 0) {
            const growthRate = ((current - previous) / previous) * 100;
            return `${growthRate >= 0 ? '+' : ''}${growthRate.toFixed(1)}%`;
        }

        // 情况3：去年同期为负数（特殊处理）
        if (previous < 0) {
            if (current >= 0) {
                // 从负数变为正数：这是改善，计算改善幅度
                const improvementRate = ((current - previous) / Math.abs(previous)) * 100;
                return `+${improvementRate.toFixed(1)}%`;
            } else {
                // 都是负数：比较亏损程度变化
                const absChange = Math.abs(current) - Math.abs(previous);
                const changeRate = (absChange / Math.abs(previous)) * 100;

                if (absChange > 0) {
                    // 亏损增加
                    return `-${changeRate.toFixed(1)}%`;
                } else if (absChange < 0) {
                    // 亏损减少（改善）
                    return `+${Math.abs(changeRate).toFixed(1)}%`;
                } else {
                    return `0.0%`;
                }
            }
        }

        return "-";
    }

    function getDimensionValue(item, dimension) {
        switch(dimension) {
            case 'product': return item.product || '-';
            case 'personnel': return item.personnel || '-';
            case 'month': return formatMonth(item.month) || '-';
            case 'terminal': return item.terminal || '-';
            default: return '-';
        }
    }

    function formatMonth(month) {
        if (!month) return '-';
        const monthStr = String(month);
        if (monthStr.length === 6) {
            const year = monthStr.substring(0, 4);
            const mon = monthStr.substring(4, 6);
            return `${year}年${parseInt(mon)}月`;
        }
        return monthStr;
    }

    function renderHierarchyRows(tree, dimensions, level, parentPath = '') {
        let rows = '';

        Object.keys(tree).sort((a, b) => {
            // 按实际销售金额折算后降序排列
            const aActual = tree[a].data.actual || 0;
            const bActual = tree[b].data.actual || 0;
            return bActual - aActual;
        }).forEach((key, index) => {
            const node = tree[key];
            const currentPath = parentPath ? `${parentPath}-${index}` : `${index}`;
            const indent = '　'.repeat(level); // 使用全角空格缩进

            // 每个节点都有数据
            const item = node.data;

            // 调试信息：检查数据内容
            if (level === 0) {
                console.log(`渲染层级 ${level} 节点 ${key}:`, {
                    actual: item.actual,
                    target: item.target,
                    lastYearSales: item.lastYearSales,
                    lastPeriodSales: item.lastPeriodSales,
                    samePeriodGrowth: item.samePeriodGrowth,
                    sequentialGrowth: item.sequentialGrowth
                });
            }

            // 计算达成率 - 使用与产品分析表相同的逻辑
            let achievementDisplay, achievementColor;
            if (item.target > 0) {
                const achievementRate = (item.actual / item.target * 100).toFixed(1);
                achievementDisplay = achievementRate + '%';
                achievementColor = parseFloat(achievementRate) >= 100 ? '#22c55e' : '#ef4444';
            } else {
                achievementDisplay = '-';
                achievementColor = '#9ca3af'; // 灰色
            }

            // 数字格式化函数 - 使用与产品分析表相同的货币格式
            const formatCurrency = (amount) => {
                return new Intl.NumberFormat('zh-CN', {
                    style: 'currency',
                    currency: 'CNY',
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                }).format(amount || 0);
            };

            // 盒数格式化函数
            const formatBoxes = (boxes) => {
                return new Intl.NumberFormat('zh-CN', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 1
                }).format(boxes || 0);
            };

            // 增长率颜色函数 - 使用与产品分析表相同的逻辑
            const getGrowthColor = (growthStr) => {
                if (!growthStr || growthStr === '-') return '#6b7280';
                const growth = parseFloat(growthStr.replace(/[+%]/g, ''));
                return growth >= 0 ? '#22c55e' : '#ef4444';
            };

            // 展开/收起图标 - 根据层级使用不同图标
            let expandIcon = '';
            let clickHandler = '';
            if (node.hasChildren) {
                if (level === 0) {
                    // 第一层使用方括号
                    expandIcon = node.expanded ? '[-]' : '[+]';
                } else if (level === 1) {
                    // 第二层使用三角形
                    expandIcon = node.expanded ? '▼' : '▶';
                } else {
                    // 第三层及以上使用小圆点
                    expandIcon = node.expanded ? '●' : '○';
                }
                clickHandler = `onclick="toggleHierarchyNode('${currentPath}')" style="cursor: pointer;"`;
            }

            // 根据层级设置背景色和样式
            let bgColor, fontWeight, borderLeft;
            switch(level) {
                case 0:
                    bgColor = '#e0f2fe'; // 浅蓝色
                    fontWeight = '700';
                    borderLeft = '4px solid #0284c7';
                    break;
                case 1:
                    bgColor = '#f0f9ff'; // 更浅的蓝色
                    fontWeight = '600';
                    borderLeft = '3px solid #0ea5e9';
                    break;
                case 2:
                    bgColor = '#f8fafc'; // 浅灰色
                    fontWeight = '500';
                    borderLeft = '2px solid #64748b';
                    break;
                default:
                    bgColor = '#ffffff';
                    fontWeight = '400';
                    borderLeft = '1px solid #cbd5e1';
            }

            rows += `
                <tr id="hierarchy-row-${currentPath}" style="background-color: ${bgColor}; font-weight: ${fontWeight}; border-left: ${borderLeft};">
                    <td style="padding: 12px 8px 12px ${level * 30 + 15}px;" ${clickHandler}>
                        <div class="hierarchy-icon-container">
                            ${expandIcon ? `<span class="hierarchy-icon" title="${node.expanded ? '点击收起' : '点击展开'}">${expandIcon}</span>` : '<span style="width: 20px; display: inline-block;"></span>'}
                            <span class="hierarchy-text" style="color: ${level === 0 ? '#1e40af' : (level === 1 ? '#0369a1' : '#374151')};" title="${key}">${key}</span>
                        </div>
                    </td>
                    <td style="font-weight: ${level <= 1 ? '600' : '400'}; padding: 12px 8px;">${formatBoxes(item.boxes)}</td>
                    <td style="font-weight: ${level <= 1 ? '600' : '400'}; padding: 12px 8px;">${formatCurrency(item.actual)}</td>
                    <td style="font-weight: ${level <= 1 ? '600' : '400'}; padding: 12px 8px;">${formatCurrency(item.target)}</td>
                    <td style="color: ${achievementColor}; font-weight: ${level <= 1 ? '600' : '400'}; padding: 12px 8px;">${achievementDisplay}</td>
                    <td style="padding: 12px 8px;">${formatCurrency(item.lastYearSales || 0)}</td>
                    <td style="color: ${getGrowthColor(item.samePeriodGrowth)}; padding: 12px 8px;">${item.samePeriodGrowth || '-'}</td>
                    <td style="padding: 12px 8px;">${formatCurrency(item.lastPeriodSales || 0)}</td>
                    <td style="color: ${getGrowthColor(item.sequentialGrowth)}; padding: 12px 8px;">${item.sequentialGrowth || '-'}</td>
                </tr>
            `;

            // 如果有子节点且已展开，递归渲染子节点
            if (node.hasChildren && node.expanded) {
                const childRows = renderHierarchyRows(node.children, dimensions, level + 1, currentPath);
                rows += childRows;
            }
        });

        return rows;
    }

    // 全局函数：切换层级节点的展开/收起状态
    window.toggleHierarchyNode = function(nodePath) {
        console.log('切换节点:', nodePath);

        // 找到对应的节点并切换状态
        const pathParts = nodePath.split('-').map(p => parseInt(p));
        let currentTree = window.currentHierarchyTree;
        let targetNode = null;

        console.log('路径部分:', pathParts);
        console.log('当前树结构:', currentTree);

        // 根据路径找到目标节点
        try {
            pathParts.forEach((index, depth) => {
                // 使用与渲染时相同的排序逻辑：按销售额降序排列
                const keys = Object.keys(currentTree).sort((a, b) => {
                    const aActual = currentTree[a].data.actual || 0;
                    const bActual = currentTree[b].data.actual || 0;
                    return bActual - aActual;
                });
                console.log(`层级 ${depth}, 索引 ${index}, 可用键:`, keys);

                if (index >= 0 && index < keys.length) {
                    const key = keys[index];
                    targetNode = currentTree[key];
                    console.log(`找到节点:`, key, targetNode);

                    if (depth < pathParts.length - 1) {
                        currentTree = targetNode.children;
                    }
                } else {
                    throw new Error(`索引 ${index} 超出范围，可用键数量: ${keys.length}`);
                }
            });

            if (targetNode && targetNode.hasChildren) {
                targetNode.expanded = !targetNode.expanded;
                console.log('节点状态切换为:', targetNode.expanded);

                // 重新渲染表格（包含汇总行）
                const dimensions = getSelectedDimensionHierarchy();
                const tbody = document.getElementById('dynamicAnalysisBody');
                if (tbody && window.currentHierarchyData) {
                    // 计算汇总数据
                    const summaryData = calculateSummaryData(window.currentHierarchyData);

                    // 生成汇总行
                    const summaryRow = renderSummaryRow(summaryData, dimensions);

                    // 渲染层级结构
                    const rows = renderHierarchyRows(window.currentHierarchyTree, dimensions, 0);

                    // 将汇总行放在最顶部
                    tbody.innerHTML = summaryRow + rows;
                }
            } else {
                console.log('目标节点没有子节点或不存在');
            }
        } catch (error) {
            console.error('切换节点时出错:', error);
        }
    };

    window.viewSalesDetail = function(productCode, customerCode) {
        // TODO: 实现销售详情查看
        alert(`查看产品 ${productCode} 在客户 ${customerCode} 的销售详情`);
    };

    // 初始化多选筛选器
    function initMultiSelectFilters() {
        const multiSelects = ['product', 'month', 'terminal', 'rep', 'regionProductLine', 'city', 'manager'];

        multiSelects.forEach(type => {
            const container = document.getElementById(`${type}MultiSelect`);
            const display = document.getElementById(`${type}Display`);
            const dropdown = document.getElementById(`${type}Dropdown`);

            if (container && display && dropdown) {
                // 点击显示/隐藏下拉框
                display.addEventListener('click', (e) => {
                    e.stopPropagation();

                    // 检查筛选器是否被禁用
                    if (container.classList.contains('disabled')) {
                        console.log(`${type} 筛选器已被禁用，忽略点击事件`);
                        return;
                    }

                    closeAllMultiSelects();
                    container.classList.toggle('active');
                });

                // 阻止下拉框点击事件冒泡
                dropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }
        });

        // 点击外部关闭所有下拉框
        document.addEventListener('click', closeAllMultiSelects);
    }

    // 初始化多选筛选器事件（在选项加载后调用）
    function initMultiSelectEvents() {
        const multiSelects = ['product', 'month', 'terminal', 'rep', 'regionProductLine'];

        multiSelects.forEach(type => {
            const dropdown = document.getElementById(`${type}Dropdown`);
            if (!dropdown) return;

            // 全选功能
            const selectAllCheckbox = document.getElementById(`selectAll${type.charAt(0).toUpperCase() + type.slice(1)}s`);
            if (selectAllCheckbox) {
                // 移除旧的事件监听器
                selectAllCheckbox.replaceWith(selectAllCheckbox.cloneNode(true));
                const newSelectAllCheckbox = document.getElementById(`selectAll${type.charAt(0).toUpperCase() + type.slice(1)}s`);

                newSelectAllCheckbox.addEventListener('change', () => {
                    const checkboxes = dropdown.querySelectorAll('.options-list input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = newSelectAllCheckbox.checked;
                    });
                    updateMultiSelectDisplay(type);
                    // 触发关联查询更新其他筛选器选项
                    debounceUpdateRelatedFilters();
                });
            }

            // 选项变化事件
            const optionCheckboxes = dropdown.querySelectorAll('.options-list input[type="checkbox"]');
            optionCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    updateMultiSelectDisplay(type);
                    updateSelectAllState(type);
                    // 触发关联查询更新其他筛选器选项
                    debounceUpdateRelatedFilters();
                });
            });
        });

        // 月份选项从服务器加载，不使用硬编码
        // loadMonthOptions(); // 已注释，使用服务器数据

        // 加载医院选项
        loadTerminalOptions();

        // 加载产品选项
        loadProductOptions();

        // 地区产品线选项通过主筛选器加载机制处理，不需要单独调用
    }

    function closeAllMultiSelects() {
        document.querySelectorAll('.multi-select').forEach(select => {
            select.classList.remove('active');
        });
    }

    // 禁用除月份外的所有筛选器
    function disableNonMonthFilters() {
        const filterTypes = ['rep', 'regionProductLine', 'product', 'terminal', 'city', 'manager'];
        console.log('开始禁用筛选器，类型列表:', filterTypes);

        filterTypes.forEach(type => {
            const container = document.getElementById(`${type}MultiSelect`);
            const display = document.getElementById(`${type}Display`);

            console.log(`处理 ${type} 筛选器:`, {
                container: container ? '找到' : '未找到',
                display: display ? '找到' : '未找到'
            });

            if (container && display) {
                container.classList.add('disabled');
                display.style.opacity = '0.5';
                display.style.pointerEvents = 'none';

                // 更新显示文本
                const placeholder = display.querySelector('.placeholder');
                if (placeholder) {
                    placeholder.textContent = '请先选择月份';
                    placeholder.style.color = '#999';
                }

                // 清空选择
                clearFilterSelection(type);

                console.log(`${type} 筛选器已禁用，类名:`, container.className);
            } else {
                console.log(`警告：${type} 筛选器的DOM元素未找到`);
            }
        });

        console.log('已禁用除月份外的所有筛选器');
    }

    // 启用除月份外的所有筛选器
    function enableNonMonthFilters() {
        const filterTypes = ['rep', 'regionProductLine', 'product', 'terminal', 'city', 'manager'];
        const filterLabels = {
            'rep': '选择销售员',
            'regionProductLine': '选择地区产品线',
            'product': '选择产品',
            'terminal': '选择医院',
            'city': '选择城市',
            'manager': '选择经理'
        };

        filterTypes.forEach(type => {
            const container = document.getElementById(`${type}MultiSelect`);
            const display = document.getElementById(`${type}Display`);

            if (container && display) {
                container.classList.remove('disabled');
                display.style.opacity = '1';
                display.style.pointerEvents = 'auto';

                // 恢复显示文本
                const placeholder = display.querySelector('.placeholder');
                if (placeholder) {
                    placeholder.textContent = filterLabels[type];
                    placeholder.style.color = '';
                }
            }
        });

        console.log('已启用除月份外的所有筛选器');
    }

    // 清空筛选器选择
    function clearFilterSelection(type) {
        const dropdown = document.getElementById(`${type}Dropdown`);
        if (dropdown) {
            // 取消所有选择
            const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            // 更新显示
            updateMultiSelectDisplay(type);
            updateSelectAllState(type);
        }
    }

    // 初始化联动查询逻辑
    function initLinkedFilters() {
        console.log('初始化联动查询逻辑');

        // 延迟执行禁用逻辑，确保所有筛选器都已经初始化完成
        setTimeout(() => {
            console.log('开始执行禁用逻辑...');

            // 验证所有筛选器DOM元素是否存在
            const filterTypes = ['rep', 'regionProductLine', 'product', 'terminal', 'city', 'manager'];
            filterTypes.forEach(type => {
                const container = document.getElementById(`${type}MultiSelect`);
                const display = document.getElementById(`${type}Display`);
                console.log(`${type} 筛选器检查:`, {
                    container: container ? '存在' : '不存在',
                    display: display ? '存在' : '不存在'
                });
            });

            // 初始状态：禁用除月份外的所有筛选器
            disableNonMonthFilters();

            // 设置月份筛选器的特殊提示
            const monthDisplay = document.getElementById('monthDisplay');
            if (monthDisplay) {
                const placeholder = monthDisplay.querySelector('.placeholder');
                if (placeholder) {
                    placeholder.textContent = '请先选择月份（必选）';
                    placeholder.style.color = '#e74c3c';
                    placeholder.style.fontWeight = 'bold';
                }
            }

            // 验证经理筛选器是否被正确禁用
            const managerContainer = document.getElementById('managerMultiSelect');
            if (managerContainer) {
                console.log('经理筛选器容器找到，当前类名:', managerContainer.className);
                console.log('是否包含disabled类:', managerContainer.classList.contains('disabled'));
            } else {
                console.log('警告：未找到经理筛选器容器');
            }

            console.log('联动查询初始化完成：月份必选，其他筛选器已禁用');
        }, 1000);
    }



    function updateMultiSelectDisplay(type) {
        const display = document.getElementById(`${type}Display`);
        const dropdown = document.getElementById(`${type}Dropdown`);
        const checkboxes = dropdown.querySelectorAll('.options-list input[type="checkbox"]:checked');

        const placeholder = display.querySelector('.placeholder');
        let selectedItems = display.querySelector('.selected-items');

        if (!selectedItems) {
            selectedItems = document.createElement('div');
            selectedItems.className = 'selected-items';
            display.insertBefore(selectedItems, display.lastElementChild);
        }

        selectedItems.innerHTML = '';

        if (checkboxes.length === 0) {
            placeholder.style.display = 'block';
        } else {
            placeholder.style.display = 'none';

            Array.from(checkboxes).slice(0, 3).forEach(checkbox => {
                const item = document.createElement('span');
                item.className = 'selected-item';
                item.innerHTML = `
                    ${checkbox.nextElementSibling.textContent}
                    <span class="remove" onclick="removeSelectedItem('${type}', '${checkbox.value}')">×</span>
                `;
                selectedItems.appendChild(item);
            });

            if (checkboxes.length > 3) {
                const moreItem = document.createElement('span');
                moreItem.className = 'selected-item';
                moreItem.textContent = `+${checkboxes.length - 3}`;
                selectedItems.appendChild(moreItem);
            }
        }
    }

    function updateSelectAllState(type) {
        const dropdown = document.getElementById(`${type}Dropdown`);
        if (!dropdown) return;

        const selectAllCheckbox = document.getElementById(`selectAll${type.charAt(0).toUpperCase() + type.slice(1)}s`);
        if (!selectAllCheckbox) {
            console.log(`警告：未找到 ${type} 的全选复选框`);
            return;
        }

        const optionCheckboxes = dropdown.querySelectorAll('.options-list input[type="checkbox"]');
        const checkedBoxes = dropdown.querySelectorAll('.options-list input[type="checkbox"]:checked');

        if (checkedBoxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedBoxes.length === optionCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    function loadMonthOptions() {
        const monthOptions = document.getElementById('monthOptions');
        if (!monthOptions) return;

        const months = [
            { value: '01', label: '1月' },
            { value: '02', label: '2月' },
            { value: '03', label: '3月' },
            { value: '04', label: '4月' },
            { value: '05', label: '5月' },
            { value: '06', label: '6月' },
            { value: '07', label: '7月' },
            { value: '08', label: '8月' },
            { value: '09', label: '9月' },
            { value: '10', label: '10月' },
            { value: '11', label: '11月' },
            { value: '12', label: '12月' }
        ];

        monthOptions.innerHTML = months.map(month => `
            <label>
                <input type="checkbox" value="${month.value}">
                <span>${month.label}</span>
            </label>
        `).join('');

        // 重新绑定事件
        monthOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                updateMultiSelectDisplay('month');
                updateSelectAllState('month');
            });
        });
    }

    async function loadTerminalOptions() {
        const token = localStorage.getItem('authToken');

        try {
            const response = await fetch('/api/sales/terminals', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    populateTerminalOptions(data.data);
                }
            }
        } catch (error) {
            console.error('加载医院选项失败:', error);
        }
    }

    async function loadProductOptions() {
        const token = localStorage.getItem('authToken');

        try {
            const response = await fetch('/api/sales/filters', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data.products) {
                    populateProductOptions(data.data.products);
                }
            }
        } catch (error) {
            console.error('加载产品选项失败:', error);
        }
    }

    async function loadRegionProductLineOptions() {
        // 暂时不调用独立的API，等待后端在 /api/sales/filters 中添加 regionProductLines 字段
        console.log('地区产品线选项将通过 /api/sales/filters 加载，等待后端添加该字段');
        
        // TODO: 等待后端在 /api/sales/filters 响应中添加以下字段：
        // regionProductLines: [{ "地区产品线": "华东-产品线A" }, { "地区产品线": "华南-产品线B" }]
    }

    function populateTerminalOptions(terminals) {
        const terminalOptions = document.getElementById('terminalOptions');
        if (!terminalOptions || !terminals) return;

        terminalOptions.innerHTML = terminals.map(terminal => `
            <label>
                <input type="checkbox" value="${terminal.医院}">
                <span>${terminal.医院}</span>
            </label>
        `).join('');

        // 重新绑定事件
        terminalOptions.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                updateMultiSelectDisplay('terminal');
                updateSelectAllState('terminal');
                // 触发关联查询更新其他筛选器选项
                debounceUpdateRelatedFilters();
            });
        });
    }

    // 全局函数
    window.removeSelectedItem = function(type, value) {
        const dropdown = document.getElementById(`${type}Dropdown`);
        const checkbox = dropdown.querySelector(`input[value="${value}"]`);
        if (checkbox) {
            checkbox.checked = false;
            updateMultiSelectDisplay(type);
            updateSelectAllState(type);
        }
    };

    // 月份对比柱状图相关函数
    let monthlyComparisonChart = null;
    let monthlyMomChart = null;
    let hospitalSalesChart = null;
    let hospitalComparisonChart = null;
    let hospitalMomChart = null;
    
    // 图表数据类型状态
    let chartDataTypes = {
        comparison: 'amount',  // 同期对比图表的数据类型：amount(金额) 或 quantity(盒数)
        mom: 'amount',         // 环比对比图表的数据类型：amount(金额) 或 quantity(盒数)
        hospitalComparison: 'amount',  // 医院同期对比图表的数据类型
        hospitalMom: 'amount',         // 医院环比对比图表的数据类型
        salesTarget: 'amount'          // 销售指标对比图表的数据类型
    };
    
    // 数据类型切换处理函数 - 暴露到全局作用域
    window.handleDataTypeToggle = function(chartType, dataType) {
        console.log(`切换${chartType}图表数据类型为:`, dataType);
        
        // 更新状态
        chartDataTypes[chartType] = dataType;
        console.log('🔄 状态更新后的chartDataTypes:', chartDataTypes);
        
        // 更新按钮状态
        const toggleButtons = document.querySelectorAll(`[data-chart="${chartType}"]`);
        toggleButtons.forEach(btn => {
            if (btn.dataset.type === dataType) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
        
        // 重新加载图表数据
        const filters = window.getCurrentFilters();
        if (chartType === 'comparison') {
            if (typeof window.loadMonthlyComparisonChart === 'function') {
                window.loadMonthlyComparisonChart(filters, dataType);
            } else {
                console.error('window.loadMonthlyComparisonChart 函数未找到');
            }
        } else if (chartType === 'mom') {
            if (typeof window.loadMonthlyMomChart === 'function') {
                window.loadMonthlyMomChart(filters, dataType);
            } else {
                console.error('window.loadMonthlyMomChart 函数未找到');
            }
        }
    };

    // 医院图表数据类型切换处理函数 - 暴露到全局作用域
    window.handleHospitalDataTypeToggle = function(chartType, dataType) {
        console.log(`切换${chartType}图表数据类型为:`, dataType);

        // 更新状态
        chartDataTypes[chartType] = dataType;
        console.log('🔄 状态更新后的chartDataTypes:', chartDataTypes);

        // 更新按钮状态
        const toggleButtons = document.querySelectorAll(`[data-chart="${chartType}"]`);
        toggleButtons.forEach(btn => {
            if (btn.dataset.type === dataType) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // 重新加载图表数据
        const filters = window.getCurrentFilters();
        if (chartType === 'hospitalComparison') {
            loadHospitalComparisonChart(filters, dataType);
        } else if (chartType === 'hospitalMom') {
            loadHospitalMomChart(filters, dataType);
        }
    };

    // 销售指标图表数据类型切换处理函数 - 暴露到全局作用域
    window.handleSalesTargetDataTypeToggle = function(dataType) {
        console.log(`切换销售指标图表数据类型为:`, dataType);

        // 更新状态
        chartDataTypes.salesTarget = dataType;
        console.log('🔄 状态更新后的chartDataTypes:', chartDataTypes);

        // 更新按钮状态
        const toggleButtons = document.querySelectorAll(`[data-chart="salesTarget"]`);
        toggleButtons.forEach(btn => {
            if (btn.dataset.type === dataType) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        // 重新加载图表数据
        const filters = window.getCurrentFilters();
        loadSalesTargetComparisonChart(filters, dataType);
    };
    
    // 获取当前筛选条件 - 暴露到全局作用域
    window.getCurrentFilters = function() {
        return {
            products: window.getMultiSelectValues('product'),
            months: window.getMultiSelectValues('month'),
            quarters: window.getMultiSelectValues('quarter'),
            terminals: window.getMultiSelectValues('terminal'),
            reps: window.getMultiSelectValues('rep'),
            regionProductLines: window.getMultiSelectValues('regionProductLine'),
            cities: window.getMultiSelectValues('city'),
            managers: window.getMultiSelectValues('manager')
        };
    };

    // 加载月份对比柱状图数据 - 暴露到全局作用域
    window.loadMonthlyComparisonChart = async function(filters = {}, dataType = null) {
        const token = localStorage.getItem('authToken');
        
        // 如果没有指定数据类型，使用当前状态的数据类型
        const currentDataType = dataType || chartDataTypes.comparison;

        // 先清空月份对比图表
        console.log('清空月份对比图表...');

        // 确保图表变量被重置
        if (monthlyComparisonChart) {
            monthlyComparisonChart.destroy();
            monthlyComparisonChart = null;
        }

        try {
            console.log('🔄 发送月份对比图表请求');
            console.log('📊 筛选条件:', JSON.stringify(filters, null, 2));
            console.log('📊 数据类型:', currentDataType);
            console.log('🕐 时间戳:', Date.now());

            const requestBody = {
                ...filters,
                dataType: currentDataType, // 添加数据类型参数
                _timestamp: Date.now() // 添加时间戳避免缓存
            };

            console.log('📤 完整请求体:', JSON.stringify(requestBody, null, 2));

            const response = await fetch('/api/monthly-comparison-chart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error('获取月份对比数据失败');
            }

            const data = await response.json();
            console.log('📥 月份对比数据响应:', data);
            console.log('📊 图表数据数量:', data.data?.chartData?.length || 0);

            if (data.data?.chartData?.length > 0) {
                console.log('📈 产品同期对比数据样本:', data.data.chartData.slice(0, 3));
            }

            if (data.success) {
                if (data.data.shouldDisplay && data.data.chartData.length > 0) {
                    console.log('✅ 显示产品同期对比图表');
                    showMonthlyChartsContainer();
                    renderMonthlyComparisonChart(data.data.chartData, currentDataType);
                } else {
                    console.log('ℹ️ 没有产品同期对比数据可显示');
                    hideMonthlyComparisonChart();
                }
            } else {
                throw new Error(data.error || '获取月份对比数据失败');
            }
        } catch (error) {
            console.error('加载月份对比图表失败:', error);
            hideMonthlyComparisonChart();
        }
    };

    // 加载月份环比增长图表数据 - 暴露到全局作用域
    window.loadMonthlyMomChart = async function(filters = {}, dataType = null) {
        const token = localStorage.getItem('authToken');
        
        // 如果没有指定数据类型，使用当前状态的数据类型
        const currentDataType = dataType || chartDataTypes.mom;

        // 先清空月份环比图表
        console.log('清空月份环比图表...');

        // 确保图表变量被重置
        if (monthlyMomChart) {
            monthlyMomChart.destroy();
            monthlyMomChart = null;
        }

        try {
            console.log('🔄 发送月份环比增长图表请求');
            console.log('📊 筛选条件:', JSON.stringify(filters, null, 2));
            console.log('📊 数据类型:', currentDataType);
            console.log('🕐 时间戳:', Date.now());

            const requestBody = {
                ...filters,
                dataType: currentDataType, // 添加数据类型参数
                _timestamp: Date.now() // 添加时间戳避免缓存
            };

            console.log('📤 完整请求体:', JSON.stringify(requestBody, null, 2));

            const response = await fetch('/api/monthly-mom-chart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error('获取月份环比增长数据失败');
            }

            const data = await response.json();
            console.log('📥 月份环比增长数据响应:', data);
            console.log('📊 图表数据数量:', data.data?.chartData?.length || 0);

            if (data.data?.chartData?.length > 0) {
                console.log('📈 产品环比增长数据样本:', data.data.chartData.slice(0, 3));
                console.log('🔍 数据字段检查:', {
                    hasProduct: data.data.chartData[0]?.product !== undefined,
                    hasCurrentMonth: data.data.chartData[0]?.currentMonth !== undefined,
                    hasPreviousMonth: data.data.chartData[0]?.previousMonth !== undefined,
                    hasGrowthRate: data.data.chartData[0]?.growthRate !== undefined
                });
            }

            if (data.success) {
                if (data.data.shouldDisplay && data.data.chartData.length > 0) {
                    console.log('✅ 显示产品环比增长图表');
                    showMonthlyChartsContainer();
                    renderMonthlyMomChart(data.data.chartData, currentDataType);
                } else {
                    console.log('ℹ️ 没有产品环比增长数据可显示');
                    hideMonthlyMomChart();
                }
            } else {
                throw new Error(data.error || '获取月份环比增长数据失败');
            }
        } catch (error) {
            console.error('加载月份环比增长图表失败:', error);
            hideMonthlyMomChart();
        }
    };

    // 显示月份图表容器
    function showMonthlyChartsContainer() {
        console.log('📊 显示月份图表容器');
        const section = document.getElementById('monthlyChartsSection');
        const container = document.getElementById('monthlyChartsContainer');

        if (section) {
            section.style.display = 'block';
        }

        if (container) {
            container.style.display = 'grid';
            container.style.visibility = 'visible';
            console.log('✅ 月份图表容器已设置为显示');

            // 检查容器状态
            const styles = getComputedStyle(container);
            console.log('📋 容器状态:', {
                display: styles.display,
                visibility: styles.visibility,
                gridTemplateColumns: styles.gridTemplateColumns
            });
        } else {
            console.error('❌ monthlyChartsContainer 元素未找到');
        }

        // 确保折叠按钮状态正确
        const toggleBtn = document.getElementById('toggleMonthlyCharts');
        const toggleText = toggleBtn ? toggleBtn.querySelector('.toggle-text') : null;
        const content = document.getElementById('monthlyChartsContent');

        if (toggleBtn && toggleText && content) {
            // 默认展开状态
            content.classList.remove('collapsed');
            toggleBtn.classList.remove('collapsed');
            toggleText.textContent = '折叠';
            content.style.maxHeight = 'none';

            // 确保标题可见
            const sectionHeader = toggleBtn.closest('.section-header');
            const titleElement = sectionHeader ? sectionHeader.querySelector('h3, h4') : null;
            if (titleElement) {
                titleElement.style.opacity = '1';
            }
        }
    }

    // 隐藏月份图表容器
    function hideMonthlyChartsContainer() {
        console.log('🔒 隐藏月份图表容器并销毁图表');
        const section = document.getElementById('monthlyChartsSection');
        if (section) {
            section.style.display = 'none';
            console.log('✅ 月份图表区域已隐藏');
        } else {
            console.error('❌ monthlyChartsSection 元素未找到');
        }

        // 销毁现有图表
        if (monthlyComparisonChart) {
            console.log('销毁产品同期对比图表');
            monthlyComparisonChart.destroy();
            monthlyComparisonChart = null;
        }
        if (monthlyMomChart) {
            console.log('销毁产品环比增长图表');
            monthlyMomChart.destroy();
            monthlyMomChart = null;
        }
    }

    // 显示月份对比柱状图（保留兼容性）
    function showMonthlyComparisonChart() {
        showMonthlyChartsContainer();
    }

    // 隐藏月份对比柱状图（保留兼容性）
    function hideMonthlyComparisonChart() {
        console.log('🧹 隐藏产品同期对比图表');
        
        // 检查变量是否已经初始化，避免"before initialization"错误
        try {
            if (typeof monthlyComparisonChart !== 'undefined' && monthlyComparisonChart) {
                console.log('🗑️ 销毁现有产品同期对比图表实例');
                monthlyComparisonChart.destroy();
                monthlyComparisonChart = null;
            }
        } catch (error) {
            // 如果变量还没有初始化，忽略错误
            console.log('📝 monthlyComparisonChart 变量还未初始化，跳过销毁操作');
        }

        // 清空canvas
        const canvas = document.getElementById('monthlyComparisonChart');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            console.log('🧽 清空产品同期对比图表canvas');
        }

        // 延迟检查是否需要隐藏容器，给其他图表加载时间
        setTimeout(() => {
            if (!monthlyComparisonChart && !monthlyMomChart) {
                console.log('🔒 两个图表都为空，隐藏容器');
                hideMonthlyChartsContainer();
            }
        }, 100);
    }

    // 显示月份环比增长图表（保留兼容性）
    function showMonthlyMomChart() {
        showMonthlyChartsContainer();
    }

    // 隐藏月份环比增长图表（保留兼容性）
    function hideMonthlyMomChart() {
        console.log('🧹 隐藏产品环比增长图表');
        
        // 检查变量是否已经初始化，避免"before initialization"错误
        try {
            if (typeof monthlyMomChart !== 'undefined' && monthlyMomChart) {
                console.log('🗑️ 销毁现有产品环比增长图表实例');
                monthlyMomChart.destroy();
                monthlyMomChart = null;
            }
        } catch (error) {
            // 如果变量还没有初始化，忽略错误
            console.log('📝 monthlyMomChart 变量还未初始化，跳过销毁操作');
        }

        // 清空canvas
        const canvas = document.getElementById('monthlyMomChart');
        if (canvas) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            console.log('🧽 清空产品环比增长图表canvas');
        }

        // 延迟检查是否需要隐藏容器，给其他图表加载时间
        setTimeout(() => {
            if (!monthlyComparisonChart && !monthlyMomChart) {
                console.log('🔒 两个图表都为空，隐藏容器');
                hideMonthlyChartsContainer();
            }
        }, 100);
    }

    // 渲染产品对比柱状图
    function renderMonthlyComparisonChart(chartData, dataType = null) {
        const currentDataType = dataType || chartDataTypes.comparison;
        console.log('🎨 开始渲染产品同期对比柱状图，数据类型:', currentDataType);

        // 确保容器显示
        showMonthlyChartsContainer();

        const canvas = document.getElementById('monthlyComparisonChart');
        if (!canvas) {
            console.error('产品对比图表canvas元素未找到');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (monthlyComparisonChart) {
            monthlyComparisonChart.destroy();
            monthlyComparisonChart = null;
        }

        console.log('🎨 开始渲染产品同期对比柱状图');
        console.log('📊 图表数据:', chartData);
        console.log('📈 数据数量:', chartData.length);

        // 准备数据
        const labels = chartData.map(item => item.product);
        const currentYearData = chartData.map(item => item.currentYear);
        const lastYearData = chartData.map(item => item.lastYear);

        console.log('🏷️ 产品标签:', labels);
        console.log('📊 当期数据:', currentYearData);
        console.log('📊 同期数据:', lastYearData);

        // 根据数据类型选择格式化函数和标签
        const isAmountType = currentDataType === 'amount';
        const formatValue = isAmountType ? 
            (value) => new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(value) :
            (value) => new Intl.NumberFormat('zh-CN').format(value) + '盒';
        
        const currentLabel = isAmountType ? '当期销售额' : '当期销量';
        const previousLabel = isAmountType ? '同期销售额' : '同期销量';
        const yAxisTitle = isAmountType ? '销售额' : '销量(盒)';
        const chartTitle = isAmountType ? '产品同期销售额对比' : '产品同期销量对比';
        
        // 更新图表标题
        const titleElement = document.getElementById('monthlyComparisonTitle');
        if (titleElement) {
            titleElement.textContent = chartTitle;
        }

        // 准备增长率数据
        const growthRateData = chartData.map(item => parseFloat(item.growthRate || 0));

        // 创建图表
        monthlyComparisonChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: currentLabel,
                    data: currentYearData,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: previousLabel,
                    data: lastYearData,
                    backgroundColor: 'rgba(251, 191, 36, 0.8)',
                    borderColor: '#fbbf24',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: '同比增长率',
                    data: growthRateData,
                    type: 'line',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderColor: function(context) {
                        const value = context.parsed?.y || growthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    borderWidth: 3,
                    pointBackgroundColor: function(context) {
                        const value = context.parsed?.y || growthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1',
                    segment: {
                        borderColor: function(ctx) {
                            const value = ctx.p1.parsed.y;
                            return value >= 0 ? '#22c55e' : '#ef4444';
                        }
                    }
                }]
            },

            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartTitle,
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.dataset.label === '同比增长率') {
                                    return `${context.dataset.label}: ${parseFloat(context.parsed.y) >= 0 ? '+' : ''}${context.parsed.y.toFixed(1)}%`;
                                } else {
                                    const value = formatValue(context.parsed.y);
                                    return `${context.dataset.label}: ${value}`;
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatValue(value);
                            }
                        },
                        title: {
                            display: true,
                            text: yAxisTitle
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(1) + '%';
                            }
                        },
                        title: {
                            display: true,
                            text: '环比增长率 (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                    x: {
                        title: {
                            display: false
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        console.log('月份对比柱状图渲染完成');
        
        // 刷新数据标签控制按钮
        setTimeout(() => {
            if (window.chartDataLabelsController) {
                window.chartDataLabelsController.refreshButtons();
            }
        }, 100);
    }

    // 渲染产品环比增长柱状图
    function renderMonthlyMomChart(chartData, dataType = null) {
        const currentDataType = dataType || chartDataTypes.mom;
        console.log('🎨 开始渲染产品环比增长柱状图，数据类型:', currentDataType);
        console.log('🔍 传入的dataType参数:', dataType);
        console.log('🔍 chartDataTypes.mom状态:', chartDataTypes.mom);

        // 确保容器显示
        showMonthlyChartsContainer();

        const canvas = document.getElementById('monthlyMomChart');
        if (!canvas) {
            console.error('产品环比增长图表canvas元素未找到');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (monthlyMomChart) {
            monthlyMomChart.destroy();
            monthlyMomChart = null;
        }

        console.log('🎨 开始渲染产品环比增长柱状图');
        console.log('📊 图表数据:', chartData);
        console.log('📈 数据数量:', chartData.length);

        if (!chartData || chartData.length === 0) {
            console.warn('⚠️ 没有图表数据，无法渲染');
            return;
        }

        // 准备数据
        const labels = chartData.map(item => item.product);
        const currentMonthData = chartData.map(item => item.currentMonth || 0);
        const previousMonthData = chartData.map(item => item.previousMonth || 0);

        console.log('🏷️ 产品标签:', labels);
        console.log('📊 当期数据:', currentMonthData);
        console.log('📊 上期数据:', previousMonthData);

        // 根据数据类型选择格式化函数和标签
        const isAmountType = currentDataType === 'amount';
        const formatValue = isAmountType ? 
            (value) => new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(value) :
            (value) => new Intl.NumberFormat('zh-CN').format(value) + '盒';
        
        const currentLabel = isAmountType ? '当期销售额' : '当期销量';
        const previousLabel = isAmountType ? '上期销售额' : '上期销量';
        const yAxisTitle = isAmountType ? '销售额' : '销量(盒)';
        const chartTitle = isAmountType ? '产品环比销售额对比' : '产品环比销量对比';
        
        console.log('🏷️ 环比图表标签设置:', { currentLabel, previousLabel, yAxisTitle, chartTitle });
        console.log('📊 formatValue函数测试:', formatValue(1000));
        
        // 更新图表标题
        const titleElement = document.getElementById('monthlyMomTitle');
        if (titleElement) {
            titleElement.textContent = chartTitle;
        }

        // 准备环比增长率数据
        const momGrowthRateData = chartData.map(item => parseFloat(item.growthRate || 0));

        // 创建图表
        monthlyMomChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: currentLabel,
                    data: currentMonthData,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: previousLabel,
                    data: previousMonthData,
                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                    borderColor: '#10b981',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: '环比增长率',
                    data: momGrowthRateData,
                    type: 'line',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderColor: function(context) {
                        const value = context.parsed?.y || momGrowthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    borderWidth: 3,
                    pointBackgroundColor: function(context) {
                        const value = context.parsed?.y || momGrowthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1',
                    segment: {
                        borderColor: function(ctx) {
                            const value = ctx.p1.parsed.y;
                            return value >= 0 ? '#22c55e' : '#ef4444';
                        }
                    }
                }]
            },

            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartTitle,
                        font: {
                            size: 16
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.dataset.label === '环比增长率') {
                                    return `${context.dataset.label}: ${parseFloat(context.parsed.y) >= 0 ? '+' : ''}${context.parsed.y.toFixed(1)}%`;
                                } else {
                                    const value = formatValue(context.parsed.y);
                                    return `${context.dataset.label}: ${value}`;
                                }
                            },
                            afterLabel: function(context) {
                                // 产品环比增长不需要显示环比期间
                                return '';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatValue(value);
                            }
                        },
                        title: {
                            display: true,
                            text: yAxisTitle
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: false,
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(1) + '%';
                            }
                        },
                        title: {
                            display: true,
                            text: '环比增长率 (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                    x: {
                        title: {
                            display: false
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        console.log('月份环比增长柱状图渲染完成');
        
        // 刷新数据标签控制按钮
        setTimeout(() => {
            if (window.chartDataLabelsController) {
                window.chartDataLabelsController.refreshButtons();
            }
        }, 100);
    }

    // 医院销量占比柱状图相关函数

    // 加载医院同期对比柱状图数据
    async function loadHospitalComparisonChart(filters = {}, dataType = null) {
        const token = localStorage.getItem('authToken');
        if (!token) {
            console.error('未找到认证令牌');
            return;
        }

        // 如果没有指定数据类型，使用当前状态的数据类型
        const currentDataType = dataType || chartDataTypes.hospitalComparison;

        // 先清空医院同期对比图表
        console.log('清空医院同期对比图表...');

        // 确保图表变量被重置
        if (hospitalComparisonChart) {
            hospitalComparisonChart.destroy();
            hospitalComparisonChart = null;
        }

        try {
            console.log('🔄 发送医院同期对比图表请求');
            console.log('📊 筛选条件:', JSON.stringify(filters, null, 2));
            console.log('🕐 时间戳:', Date.now());

            const requestBody = {
                ...filters,
                dataType: currentDataType, // 添加数据类型参数
                _timestamp: Date.now() // 添加时间戳避免缓存
            };

            console.log('📤 完整请求体:', JSON.stringify(requestBody, null, 2));

            const response = await fetch('/api/hospital-comparison-chart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error('获取医院同期对比数据失败');
            }

            const data = await response.json();
            console.log('📥 医院同期对比数据响应:', data);
            console.log('📊 图表数据数量:', data.data?.chartData?.length || 0);

            if (data.data?.chartData?.length > 0) {
                console.log('📈 医院同期对比数据样本:', data.data.chartData.slice(0, 3));
            }

            if (data.success) {
                if (data.data.shouldDisplay && data.data.chartData.length > 0) {
                    console.log('✅ 显示医院同期对比图表');
                    showHospitalSalesChartContainer();
                    renderHospitalComparisonChart(data.data.chartData, currentDataType);
                } else {
                    console.log('ℹ️ 没有医院同期对比数据可显示');
                    hideHospitalComparisonChart();
                }
            } else {
                throw new Error(data.error || '获取医院同期对比数据失败');
            }
        } catch (error) {
            console.error('加载医院同期对比图表失败:', error);
            hideHospitalComparisonChart();
        }
    }

    // 加载医院环比增长图表数据
    async function loadHospitalMomChart(filters = {}, dataType = null) {
        const token = localStorage.getItem('authToken');
        if (!token) {
            console.error('未找到认证令牌');
            return;
        }

        // 如果没有指定数据类型，使用当前状态的数据类型
        const currentDataType = dataType || chartDataTypes.hospitalMom;

        // 先清空医院环比图表
        console.log('清空医院环比图表...');

        // 确保图表变量被重置
        if (hospitalMomChart) {
            hospitalMomChart.destroy();
            hospitalMomChart = null;
        }

        try {
            console.log('🔄 发送医院环比增长图表请求');
            console.log('📊 筛选条件:', JSON.stringify(filters, null, 2));
            console.log('🕐 时间戳:', Date.now());

            const requestBody = {
                ...filters,
                dataType: currentDataType, // 添加数据类型参数
                _timestamp: Date.now() // 添加时间戳避免缓存
            };

            console.log('📤 完整请求体:', JSON.stringify(requestBody, null, 2));

            const response = await fetch('/api/hospital-mom-chart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error('获取医院环比增长数据失败');
            }

            const data = await response.json();
            console.log('📥 医院环比增长数据响应:', data);
            console.log('📊 图表数据数量:', data.data?.chartData?.length || 0);

            if (data.data?.chartData?.length > 0) {
                console.log('📈 医院环比增长数据样本:', data.data.chartData.slice(0, 3));
            }

            if (data.success) {
                if (data.data.shouldDisplay && data.data.chartData.length > 0) {
                    console.log('✅ 显示医院环比增长图表');
                    showHospitalSalesChartContainer();
                    renderHospitalMomChart(data.data.chartData, currentDataType);
                } else {
                    console.log('ℹ️ 没有医院环比增长数据可显示');
                    hideHospitalMomChart();
                }
            } else {
                throw new Error(data.error || '获取医院环比增长数据失败');
            }
        } catch (error) {
            console.error('加载医院环比增长图表失败:', error);
            hideHospitalMomChart();
        }
    }

    // 渲染医院销量占比柱状图
    function renderHospitalSalesChart(chartData, metadata) {
        console.log('🎨 开始渲染医院销量占比柱状图');

        // 确保容器显示
        showHospitalSalesChartContainer();

        const canvas = document.getElementById('hospitalSalesChart');
        if (!canvas) {
            console.error('医院销量占比图表canvas元素未找到');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (hospitalSalesChart) {
            hospitalSalesChart.destroy();
            hospitalSalesChart = null;
        }

        console.log('🎨 开始渲染医院销量占比柱状图');
        console.log('📊 图表数据:', chartData);
        console.log('📈 数据数量:', chartData.length);
        console.log('📊 元数据:', metadata);

        if (!chartData || chartData.length === 0) {
            console.warn('⚠️ 没有图表数据，无法渲染');
            return;
        }

        // 准备数据
        const labels = chartData.map(item => item.hospital);
        const salesData = chartData.map(item => item.totalSales);
        const percentageData = chartData.map(item => parseFloat(item.percentage));

        console.log('🏷️ 医院标签:', labels);
        console.log('📊 销量数据:', salesData);
        console.log('📊 占比数据:', percentageData);

        // 格式化货币函数
        const formatCurrency = (value) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(value);
        };

        // 创建图表
        hospitalSalesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '销售金额',
                    data: salesData,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: '销量占比',
                    data: percentageData,
                    type: 'line',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderColor: '#22c55e',
                    borderWidth: 3,
                    pointBackgroundColor: '#22c55e',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: `医院销量占比分析（前${chartData.length}家医院，占总销量80%）`,
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 30
                        }
                    },
                    subtitle: {
                        display: true,
                        text: `总销量: ${formatCurrency(metadata.totalSales)} | 总医院数: ${metadata.hospitalCount}家`,
                        font: {
                            size: 12
                        },
                        padding: {
                            bottom: 20
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return `医院: ${context[0].label}`;
                            },
                            label: function(context) {
                                const datasetLabel = context.dataset.label;
                                const value = context.parsed.y;

                                if (datasetLabel === '销售金额') {
                                    return `${datasetLabel}: ${formatCurrency(value)}`;
                                } else if (datasetLabel === '销量占比') {
                                    return `${datasetLabel}: ${value}%`;
                                }
                                return `${datasetLabel}: ${value}`;
                            },
                            afterBody: function(context) {
                                const index = context[0].dataIndex;
                                const item = chartData[index];
                                return [
                                    `累计占比: ${item.cumulativePercentage}%`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '医院',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 0
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '销售金额 (¥)',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '销量占比 (%)',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });

        console.log('✅ 医院销量占比柱状图渲染完成');
        
        // 刷新数据标签控制按钮
        setTimeout(() => {
            if (window.chartDataLabelsController) {
                window.chartDataLabelsController.refreshButtons();
            }
        }, 100);
    }

    // 渲染医院同期对比柱状图
    function renderHospitalComparisonChart(chartData, dataType = null) {
        const currentDataType = dataType || chartDataTypes.hospitalComparison;
        console.log('🎨 开始渲染医院同期对比柱状图，数据类型:', currentDataType);

        // 确保容器显示
        showHospitalSalesChartContainer();

        const canvas = document.getElementById('hospitalComparisonChart');
        if (!canvas) {
            console.error('医院同期对比图表canvas元素未找到');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (hospitalComparisonChart) {
            hospitalComparisonChart.destroy();
            hospitalComparisonChart = null;
        }

        console.log('🎨 开始渲染医院同期对比柱状图');
        console.log('📊 图表数据:', chartData);
        console.log('📈 数据数量:', chartData.length);

        if (!chartData || chartData.length === 0) {
            console.warn('⚠️ 没有图表数据，无法渲染');
            return;
        }

        // 准备数据
        const labels = chartData.map(item => item.hospital);
        const currentYearData = chartData.map(item => item.currentYearSales || 0);
        const lastYearData = chartData.map(item => item.lastYearSales || 0);
        const growthRateData = chartData.map(item => {
            if (item.lastYearSales > 0) {
                return ((item.currentYearSales - item.lastYearSales) / item.lastYearSales * 100);
            }
            return 0;
        });

        // 为增长率数据点创建颜色数组
        const growthRateColors = growthRateData.map(value => value >= 0 ? '#22c55e' : '#ef4444');

        console.log('🏷️ 医院标签:', labels);
        console.log('📊 当期数据:', currentYearData);
        console.log('📊 同期数据:', lastYearData);
        console.log('📊 增长率数据:', growthRateData);
        console.log('🎨 增长率颜色数组:', growthRateColors);

        // 根据数据类型选择格式化函数和标签
        const isAmountType = currentDataType === 'amount';
        const formatValue = isAmountType ?
            (value) => new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(value) :
            (value) => new Intl.NumberFormat('zh-CN').format(value) + '盒';

        const currentLabel = isAmountType ? '当期销售额' : '当期销量';
        const previousLabel = isAmountType ? '同期销售额' : '同期销量';
        const yAxisTitle = isAmountType ? '销售额' : '销量(盒)';
        const chartTitle = isAmountType ? '医院同期销售额对比' : '医院同期销量对比';

        // 更新图表标题
        const titleElement = document.getElementById('hospitalComparisonTitle');
        if (titleElement) {
            titleElement.textContent = chartTitle;
        }

        // 创建图表
        hospitalComparisonChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: currentLabel,
                    data: currentYearData,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: previousLabel,
                    data: lastYearData,
                    backgroundColor: 'rgba(251, 191, 36, 0.8)',
                    borderColor: '#fbbf24',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: '同比增长率',
                    data: growthRateData,
                    type: 'line',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderColor: function(context) {
                        const value = context.parsed?.y || growthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    borderWidth: 3,
                    pointBackgroundColor: function(context) {
                        const value = context.parsed?.y || growthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1',
                    segment: {
                        borderColor: function(ctx) {
                            const value = ctx.p1.parsed.y;
                            return value >= 0 ? '#22c55e' : '#ef4444';
                        }
                    }
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: '医院同期销售对比分析',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 30
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return `医院: ${context[0].label}`;
                            },
                            label: function(context) {
                                if (context.datasetIndex === 2) {
                                    return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`;
                                } else {
                                    return `${context.dataset.label}: ${formatValue(context.parsed.y)}`;
                                }
                            },
                            labelColor: function(context) {
                                if (context.datasetIndex === 2) {
                                    // 增长率线：根据正负值设置颜色
                                    const value = context.parsed.y;
                                    return {
                                        borderColor: value >= 0 ? '#22c55e' : '#ef4444', // 绿色增长，红色下跌
                                        backgroundColor: value >= 0 ? '#22c55e' : '#ef4444'
                                    };
                                }
                                return {
                                    borderColor: context.dataset.borderColor,
                                    backgroundColor: context.dataset.backgroundColor
                                };
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '医院',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 0
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: yAxisTitle,
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            callback: function(value) {
                                return formatValue(value);
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '同比增长率 (%)',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        console.log('✅ 医院同期对比柱状图渲染完成');
        
        // 刷新数据标签控制按钮
        setTimeout(() => {
            if (window.chartDataLabelsController) {
                window.chartDataLabelsController.refreshButtons();
            }
        }, 100);
    }

    // 渲染医院环比增长柱状图
    function renderHospitalMomChart(chartData, dataType = null) {
        const currentDataType = dataType || chartDataTypes.hospitalMom;
        console.log('🎨 开始渲染医院环比增长柱状图，数据类型:', currentDataType);

        // 确保容器显示
        showHospitalSalesChartContainer();

        const canvas = document.getElementById('hospitalMomChart');
        if (!canvas) {
            console.error('医院环比增长图表canvas元素未找到');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (hospitalMomChart) {
            hospitalMomChart.destroy();
            hospitalMomChart = null;
        }

        console.log('🎨 开始渲染医院环比增长柱状图');
        console.log('📊 图表数据:', chartData);
        console.log('📈 数据数量:', chartData.length);

        if (!chartData || chartData.length === 0) {
            console.warn('⚠️ 没有图表数据，无法渲染');
            return;
        }

        // 准备数据
        const labels = chartData.map(item => item.hospital);
        const currentMonthData = chartData.map(item => item.currentMonthSales || 0);
        const previousMonthData = chartData.map(item => item.previousMonthSales || 0);
        const momGrowthRateData = chartData.map(item => {
            if (item.previousMonthSales > 0) {
                return ((item.currentMonthSales - item.previousMonthSales) / item.previousMonthSales * 100);
            }
            return 0;
        });

        // 为环比增长率数据点创建颜色数组
        const momGrowthRateColors = momGrowthRateData.map(value => value >= 0 ? '#22c55e' : '#ef4444');

        console.log('🏷️ 医院标签:', labels);
        console.log('📊 当期数据:', currentMonthData);
        console.log('📊 上期数据:', previousMonthData);
        console.log('📊 环比增长率数据:', momGrowthRateData);

        // 根据数据类型选择格式化函数和标签
        const isAmountType = currentDataType === 'amount';
        const formatValue = isAmountType ?
            (value) => new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(value) :
            (value) => new Intl.NumberFormat('zh-CN').format(value) + '盒';

        const currentLabel = isAmountType ? '当期销售额' : '当期销量';
        const previousLabel = isAmountType ? '上期销售额' : '上期销量';
        const yAxisTitle = isAmountType ? '销售额' : '销量(盒)';
        const chartTitle = isAmountType ? '医院环比销售额对比' : '医院环比销量对比';

        // 更新图表标题
        const titleElement = document.getElementById('hospitalMomTitle');
        if (titleElement) {
            titleElement.textContent = chartTitle;
        }

        // 创建图表
        hospitalMomChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: currentLabel,
                    data: currentMonthData,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: previousLabel,
                    data: previousMonthData,
                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                    borderColor: '#10b981',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: '环比增长率',
                    data: momGrowthRateData,
                    type: 'line',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderColor: function(context) {
                        const value = context.parsed?.y || momGrowthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    borderWidth: 3,
                    pointBackgroundColor: function(context) {
                        const value = context.parsed?.y || momGrowthRateData[context.dataIndex];
                        return value >= 0 ? '#22c55e' : '#ef4444';
                    },
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1',
                    segment: {
                        borderColor: function(ctx) {
                            const value = ctx.p1.parsed.y;
                            return value >= 0 ? '#22c55e' : '#ef4444';
                        }
                    }
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: '医院环比销售增长分析',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 30
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return `医院: ${context[0].label}`;
                            },
                            label: function(context) {
                                if (context.datasetIndex === 2) {
                                    return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`;
                                } else {
                                    return `${context.dataset.label}: ${formatValue(context.parsed.y)}`;
                                }
                            },
                            labelColor: function(context) {
                                if (context.datasetIndex === 2) {
                                    // 增长率线：根据正负值设置颜色
                                    const value = context.parsed.y;
                                    return {
                                        borderColor: value >= 0 ? '#22c55e' : '#ef4444', // 绿色增长，红色下跌
                                        backgroundColor: value >= 0 ? '#22c55e' : '#ef4444'
                                    };
                                }
                                return {
                                    borderColor: context.dataset.borderColor,
                                    backgroundColor: context.dataset.backgroundColor
                                };
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '医院',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 0
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: yAxisTitle,
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        ticks: {
                            callback: function(value) {
                                return formatValue(value);
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '环比增长率 (%)',
                            font: {
                                size: 14,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });

        console.log('✅ 医院环比增长柱状图渲染完成');
        
        // 刷新数据标签控制按钮
        setTimeout(() => {
            if (window.chartDataLabelsController) {
                window.chartDataLabelsController.refreshButtons();
            }
        }, 100);
    }

    // 显示医院销量占比图表容器
    function showHospitalSalesChartContainer() {
        const section = document.getElementById('hospitalSalesChartSection');
        if (section) {
            section.style.display = 'block';
            console.log('✅ 显示医院销量占比图表容器');
        }
    }

    // 隐藏医院销量占比图表容器
    function hideHospitalSalesChartContainer() {
        const section = document.getElementById('hospitalSalesChartSection');
        if (section) {
            section.style.display = 'none';
            console.log('❌ 隐藏医院销量占比图表容器');
        }
    }

    // 隐藏医院同期对比图表
    function hideHospitalComparisonChart() {
        const chartItem = document.getElementById('hospitalComparisonChartItem');
        if (chartItem) {
            chartItem.style.display = 'none';
        }
        if (hospitalComparisonChart) {
            hospitalComparisonChart.destroy();
            hospitalComparisonChart = null;
        }
    }

    // 隐藏医院环比增长图表
    function hideHospitalMomChart() {
        const chartItem = document.getElementById('hospitalMomChartItem');
        if (chartItem) {
            chartItem.style.display = 'none';
        }
        if (hospitalMomChart) {
            hospitalMomChart.destroy();
            hospitalMomChart = null;
        }
    }

    // 用户管理相关函数
    function loadUsersData() {
        // 检查管理员权限
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        if (userInfo.role !== 'admin') {
            showError('您没有权限访问用户管理功能');
            return;
        }

        const token = localStorage.getItem('authToken');
        fetch('/api/users', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('获取用户列表失败');
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                displayUsersTable(result.data);
            } else {
                throw new Error(result.error || '获取用户列表失败');
            }
        })
        .catch(error => {
            console.error('加载用户数据失败:', error);
            showError('加载用户数据失败: ' + error.message);
        });
    }

    function displayUsersTable(users) {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;

        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666; padding: 40px;">暂无用户数据</td></tr>';
            return;
        }

        const formatDate = (dateString) => {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleString('zh-CN');
        };

        const html = users.map(user => `
            <tr>
                <td>${user.username || '-'}</td>
                <td>${user.email || '-'}</td>
                <td>${user.full_name || '-'}</td>
                <td>
                    <span class="role-badge ${user.role === 'admin' ? 'admin' : 'user'}">
                        ${user.role === 'admin' ? '管理员' : '用户'}
                    </span>
                </td>
                <td>${user.department || '-'}</td>
                <td>
                    <span class="status-badge ${user.is_active ? 'active' : 'inactive'}">
                        ${user.is_active ? '启用' : '禁用'}
                    </span>
                </td>
                <td>${formatDate(user.created_at)}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline" onclick="editUser(${user.id})" title="编辑用户">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline" onclick="resetPassword(${user.id})" title="重置密码">
                            <i class="fas fa-key"></i>
                        </button>
                        ${user.role !== 'admin' ? `<button class="btn btn-sm btn-outline btn-danger" onclick="deleteUser(${user.id})" title="删除用户">
                            <i class="fas fa-trash"></i>
                        </button>` : ''}
                    </div>
                </td>
            </tr>
        `).join('');

        tbody.innerHTML = html;
    }

    // 用户管理模态框相关函数
    function openUserModal(isEdit = false, userData = null) {
        const modal = document.getElementById('userModal');
        const title = document.getElementById('userModalTitle');
        const form = document.getElementById('userForm');
        const passwordGroup = document.getElementById('passwordGroup');
        const statusGroup = document.getElementById('statusGroup');
        const usernameField = document.getElementById('username');
        
        // 重置表单
        form.reset();
        
        if (isEdit && userData) {
            title.textContent = '编辑用户';
            passwordGroup.style.display = 'none'; // 编辑时隐藏密码字段
            statusGroup.style.display = 'block'; // 编辑时显示状态字段
            usernameField.readOnly = true; // 编辑时用户名只读，不是禁用
            
            // 编辑时移除密码字段的required属性
            document.getElementById('password').required = false;
            
            // 填充表单数据
            document.getElementById('userId').value = userData.id;
            document.getElementById('username').value = userData.username || '';
            document.getElementById('fullName').value = userData.full_name || '';
            document.getElementById('email').value = userData.email || '';
            document.getElementById('role').value = userData.role || 'user';
            document.getElementById('department').value = userData.department || '';
            document.getElementById('isActive').value = userData.is_active ? '1' : '0';
            
            console.log('填充编辑表单数据:', userData);
        } else {
            title.textContent = '添加用户';
            passwordGroup.style.display = 'block'; // 新增时显示密码字段
            statusGroup.style.display = 'none'; // 新增时隐藏状态字段
            usernameField.readOnly = false; // 新增时用户名可修改
            
            // 新增时设置密码字段为必填
            document.getElementById('password').required = true;
            
            // 清空隐藏的用户ID字段，确保是添加操作
            document.getElementById('userId').value = '';
        }
        
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
    
    function closeUserModal() {
        const modal = document.getElementById('userModal');
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
    
    function openPasswordModal(userId) {
        const modal = document.getElementById('passwordModal');
        const form = document.getElementById('passwordForm');
        
        form.reset();
        document.getElementById('resetUserId').value = userId;
        
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
    
    function closePasswordModal() {
        const modal = document.getElementById('passwordModal');
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }

    // 全局函数供HTML调用
    window.openUserModal = openUserModal;
    window.closeUserModal = closeUserModal;
    window.openPasswordModal = openPasswordModal;
    window.closePasswordModal = closePasswordModal;

    window.editUser = async function(userId) {
        const token = localStorage.getItem('authToken');
        
        try {
            const response = await fetch(`/api/users/${userId}`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (!response.ok) {
                throw new Error('获取用户信息失败');
            }
            
            const result = await response.json();
            if (result.success) {
                openUserModal(true, result.data);
            } else {
                throw new Error(result.error || '获取用户信息失败');
            }
        } catch (error) {
            console.error('编辑用户失败:', error);
            showError('获取用户信息失败: ' + error.message);
        }
    };

    window.deleteUser = function(userId) {
        if (!confirm('确定要删除这个用户吗？删除后不可恢复！')) {
            return;
        }

        const token = localStorage.getItem('authToken');
        
        fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || '删除用户失败');
                });
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                showSuccess('用户删除成功');
                loadUsersData(); // 重新加载用户列表
            } else {
                throw new Error(result.error || '删除用户失败');
            }
        })
        .catch(error => {
            console.error('删除用户失败:', error);
            showError('删除用户失败: ' + error.message);
        });
    };

    window.resetPassword = function(userId) {
        openPasswordModal(userId);
    };

    function logout() {
        if (confirm('确定要退出登录吗？')) {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            window.location.href = '/login.html';
        }
    }

    function checkScreenSize() {
        if (window.innerWidth >= 1024) {
            // 桌面端：显示侧边栏，隐藏遮罩
            sidebar.classList.add('active');
            overlay.classList.remove('active');
            hamburgerBtn.classList.remove('active');
            document.body.style.overflow = '';
        } else {
            // 移动端：隐藏侧边栏
            if (!hamburgerBtn.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        }
    }

    function showError(message) {
        showMessage('错误: ' + message, 'error');
    }

    function showSuccess(message) {
        showMessage('成功: ' + message, 'success');
    }

    // Toast消息提示函数
    function showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessage = document.querySelector('.message-toast');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-toast message-${type}`;
        messageDiv.textContent = message;

        // 添加到页面
        document.body.appendChild(messageDiv);

        // 显示动画
        setTimeout(() => {
            messageDiv.classList.add('show');
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            messageDiv.classList.remove('show');
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, 3000);
    }

    // 将函数暴露到全局作用域
    window.showMessage = showMessage;
    window.showSuccess = showSuccess;
    window.showError = showError;

    // 设置用户管理功能
    function setupUserManagement() {
        // 添加用户按钮事件
        const addUserBtn = document.getElementById('addUserBtn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                openUserModal(false);
            });
        }

        // 用户表单提交事件
        const userForm = document.getElementById('userForm');
        if (userForm) {
            userForm.addEventListener('submit', handleUserFormSubmit);
        }

        // 密码表单提交事件
        const passwordForm = document.getElementById('passwordForm');
        if (passwordForm) {
            passwordForm.addEventListener('submit', handlePasswordFormSubmit);
        }

        // 模态框背景点击关闭
        const userModal = document.getElementById('userModal');
        const passwordModal = document.getElementById('passwordModal');
        
        if (userModal) {
            userModal.addEventListener('click', (e) => {
                if (e.target === userModal) {
                    closeUserModal();
                }
            });
        }

        if (passwordModal) {
            passwordModal.addEventListener('click', (e) => {
                if (e.target === passwordModal) {
                    closePasswordModal();
                }
            });
        }

        // 确认密码验证
        const confirmPassword = document.getElementById('confirmPassword');
        if (confirmPassword) {
            confirmPassword.addEventListener('input', validatePasswordMatch);
        }
    }

    // 处理用户表单提交
    async function handleUserFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const userData = Object.fromEntries(formData.entries());
        const isEdit = !!userData.id;
        
        console.log('提交用户表单:', userData, '编辑模式:', isEdit);

        const token = localStorage.getItem('authToken');
        const url = isEdit ? `/api/users/${userData.id}` : '/api/users';
        const method = isEdit ? 'PUT' : 'POST';

        // 编辑模式时移除密码字段
        if (isEdit) {
            delete userData.password;
            delete userData.id;
        }

        // 转换is_active为数值
        if (userData.is_active !== undefined) {
            userData.is_active = parseInt(userData.is_active);
        }

        console.log('发送到服务器的数据:', {
            url: url,
            method: method,
            userData: userData
        });

        try {
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            console.log('服务器响应状态:', response.status);

            if (!response.ok) {
                const errorData = await response.json();
                console.error('服务器错误响应:', errorData);
                throw new Error(errorData.error || `${isEdit ? '更新' : '创建'}用户失败`);
            }

            const result = await response.json();
            console.log('服务器成功响应:', result);
            
            if (result.success) {
                showSuccess(`用户${isEdit ? '更新' : '创建'}成功`);
                closeUserModal();
                loadUsersData(); // 重新加载用户列表
            } else {
                throw new Error(result.error || `${isEdit ? '更新' : '创建'}用户失败`);
            }
        } catch (error) {
            console.error(`${isEdit ? '更新' : '创建'}用户失败:`, error);
            showError(`${isEdit ? '更新' : '创建'}用户失败: ` + error.message);
        }
    }

    // 处理密码表单提交
    async function handlePasswordFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // 验证密码匹配
        const confirmPassword = document.getElementById('confirmPassword').value;
        if (data.password !== confirmPassword) {
            showError('两次输入的密码不一致');
            return;
        }

        const token = localStorage.getItem('authToken');

        try {
            const response = await fetch(`/api/users/${data.userId}/reset-password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ password: data.password })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '重置密码失败');
            }

            const result = await response.json();
            if (result.success) {
                showSuccess('密码重置成功');
                closePasswordModal();
            } else {
                throw new Error(result.error || '重置密码失败');
            }
        } catch (error) {
            console.error('重置密码失败:', error);
            showError('重置密码失败: ' + error.message);
        }
    }

    // 验证密码匹配
    function validatePasswordMatch() {
        const password = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const confirmInput = document.getElementById('confirmPassword');
        
        if (confirmPassword && password !== confirmPassword) {
            confirmInput.setCustomValidity('两次输入的密码不一致');
        } else {
            confirmInput.setCustomValidity('');
        }
    }

    // 图表功能已移除

    // 图表功能已移除

    // 鼓励话语数组
    const encouragementMessages = [
        "每一次努力都是成功的垫脚石！",
        "今天的汗水，是明天成功的见证！",
        "相信自己，你比想象中更强大！",
        "成功不是终点，失败不是末日，继续前进的勇气才最可贵！",
        "每一个小目标的达成，都是向梦想迈进的一大步！",
        "困难只是成功路上的风景，坚持走下去就能看到彩虹！",
        "你的努力，时间都看得见！",
        "今天比昨天更进步一点，就是最大的胜利！",
        "机会总是留给有准备的人，而你就是那个人！",
        "不要害怕慢，只要不停下脚步！",
        "每一次挑战都是成长的机会！",
        "相信过程，享受当下，成功自然会来！",
        "你的坚持，终将美好！",
        "今天的努力，是为了明天更好的自己！",
        "成功的路上并不拥挤，因为坚持的人不多！",
        "每一份付出都有意义，每一次努力都值得！",
        "保持热爱，奔赴山海！",
        "星光不负赶路人，时光不负有心人！",
        "愿你历尽千帆，归来仍是少年！",
        "路虽远，行则将至；事虽难，做则必成！"
    ];

    // 显示随机鼓励话语
    function showEncouragementMessage() {
        const encouragementText = document.getElementById('encouragementText');
        if (encouragementText) {
            const randomIndex = Math.floor(Math.random() * encouragementMessages.length);
            const message = encouragementMessages[randomIndex];

            // 添加打字机效果
            encouragementText.textContent = '';
            let i = 0;
            const typeWriter = () => {
                if (i < message.length) {
                    encouragementText.textContent += message.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                }
            };
            typeWriter();
        }
    }

    // 图表功能已移除

    // 图表功能已移除

    // 图表功能已移除
    function addCenterText(canvasId, text) {
        return;
    }

    // 图表功能已移除
    function addCenterTextWithDetails(canvasId, mainText, line1, line2) {
        return;
    }

    // 图表相关变量
    let achievementDonutChart = null;
    let productShareChart = null;
    let yoyComparisonChart = null;
    let momComparisonChart = null;


    // 创建总达成率环形图
    function createAchievementDonutChart(achievementRate) {
        const ctx = document.getElementById('achievementDonutChart');
        if (!ctx) return;

        const rate = parseFloat(achievementRate) || 0;
        const remaining = Math.max(0, 100 - rate);

        // 销毁现有图表
        if (achievementDonutChart) {
            achievementDonutChart.destroy();
        }

        achievementDonutChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [rate, remaining],
                    backgroundColor: [
                        rate >= 100 ? '#22c55e' : rate >= 80 ? '#f59e0b' : '#ef4444',
                        '#f1f5f9'
                    ],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });

        // 更新中心文本
        const centerText = document.getElementById('achievementCenterText');
        if (centerText) {
            const rateElement = centerText.querySelector('.achievement-rate');
            if (rateElement) {
                rateElement.textContent = `${rate.toFixed(1)}%`;
                rateElement.style.color = rate >= 100 ? '#22c55e' : rate >= 80 ? '#f59e0b' : '#ef4444';
            }
        }

        // 显示图表区域
        showChartsSection();
    }

    // 更新总达成率环形图
    function updateAchievementDonutChart(achievementRate, salesAmount = 0, targetAmount = 0) {
        createAchievementDonutChart(achievementRate);

        // 更新销售金额和指标金额显示
        const salesAmountElement = document.getElementById('achievementSalesAmount');
        const targetAmountElement = document.getElementById('achievementTargetAmount');

        if (salesAmountElement) {
            salesAmountElement.textContent = `¥${new Intl.NumberFormat('zh-CN').format(salesAmount || 0)}`;
        }

        if (targetAmountElement) {
            targetAmountElement.textContent = `¥${new Intl.NumberFormat('zh-CN').format(targetAmount || 0)}`;
        }
    }

    // 显示图表区域
    function showChartsSection() {
        console.log('显示图表区域');
        const chartsSection = document.getElementById('chartsSection');
        const chartsContainer = document.getElementById('chartsContainer');

        if (chartsSection) {
            chartsSection.style.display = 'block';
        }

        if (chartsContainer) {
            chartsContainer.style.display = 'grid';
            // 不再硬编码列数，让CSS媒体查询控制布局
            chartsContainer.style.gridTemplateRows = 'auto';
        }

        // 强制应用正确的布局
        setTimeout(() => {
            forceChartsLayout();
        }, 50);

        // 确保折叠按钮状态正确
        const toggleBtn = document.getElementById('toggleChartsContainer');
        const toggleText = toggleBtn ? toggleBtn.querySelector('.toggle-text') : null;
        const content = document.getElementById('chartsContainerContent');

        if (toggleBtn && toggleText && content) {
            // 默认展开状态
            content.classList.remove('collapsed');
            toggleBtn.classList.remove('collapsed');
            toggleText.textContent = '折叠';
            content.style.maxHeight = 'none';

            // 确保标题可见
            const sectionHeader = toggleBtn.closest('.section-header');
            const titleElement = sectionHeader ? sectionHeader.querySelector('h3, h4') : null;
            if (titleElement) {
                titleElement.style.opacity = '1';
            }
        }
    }

    // 隐藏所有图表
    function hideAllCharts() {
        const chartsSection = document.getElementById('chartsSection');
        if (chartsSection) {
            chartsSection.style.display = 'none';
        }

        if (achievementDonutChart) {
            achievementDonutChart.destroy();
            achievementDonutChart = null;
        }

        if (productShareChart) {
            productShareChart.destroy();
            productShareChart = null;
        }

        if (yoyComparisonChart) {
            yoyComparisonChart.destroy();
            yoyComparisonChart = null;
        }

        if (momComparisonChart) {
            momComparisonChart.destroy();
            momComparisonChart = null;
        }
    }

    // 保持向后兼容
    function hideAchievementDonutChart() {
        hideAllCharts();
    }

    // 生成图表颜色
    function generateChartColors(count) {
        const colors = [
            '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
            '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1',
            '#14b8a6', '#f43f5e', '#a855f7', '#10b981', '#f59e0b',
            '#3b82f6', '#ef4444', '#22c55e', '#8b5cf6', '#06b6d4'
        ];

        // 如果产品数量超过预定义颜色，生成随机颜色
        if (count > colors.length) {
            for (let i = colors.length; i < count; i++) {
                const hue = (i * 137.508) % 360; // 黄金角度分布
                colors.push(`hsl(${hue}, 70%, 50%)`);
            }
        }

        return colors.slice(0, count);
    }

    // 创建产品占比饼图 - 重新设计为简洁统一的样式
    function createProductShareChart(products) {
        console.log('🟢 dashboard.js createProductShareChart 被调用 🟢');
        console.log('🍰 创建产品销售占比图，产品数据:', products);
        const ctx = document.getElementById('productShareChart');
        if (!ctx) {
            console.error('❌ 找不到产品销售占比图canvas元素');
            return;
        }

        // 使用HTML中的固定尺寸，与其他环形图表保持一致
        console.log('🟢 产品图表使用HTML固定尺寸');
        if (!products || products.length === 0) {
            console.log('⚠️ 没有产品数据，无法创建产品销售占比图');
            return;
        }

        // 确保产品销售占比图容器可见
        const chartItem = ctx.closest('.chart-item');
        if (chartItem) {
            chartItem.style.display = 'flex';
            console.log('✅ 确保产品销售占比图容器可见');
        }

        // 计算总销售额
        const totalSales = products.reduce((sum, product) => sum + (product.actual || product.actualAmount || 0), 0);

        if (totalSales === 0) return;

        // 准备图表数据
        const chartData = products
            .map(product => ({
                name: product.name || product.productName || '未知产品',
                value: product.actual || product.actualAmount || 0,
                percentage: ((product.actual || product.actualAmount || 0) / totalSales * 100).toFixed(1)
            }))
            .filter(item => item.value > 0)
            .sort((a, b) => b.value - a.value);

        if (chartData.length === 0) return;

        const colors = generateChartColors(chartData.length);

        // 销毁现有图表
        if (productShareChart) {
            productShareChart.destroy();
        }

        productShareChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: chartData.map(item => item.name),
                datasets: [{
                    data: chartData.map(item => item.value),
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#333333'
                }]
            },
            options: {
                responsive: true, // 启用响应式，与销售员占比图保持一致
                maintainAspectRatio: true,
                aspectRatio: 1,
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10,
                        left: 10,
                        right: 10
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const item = chartData[context.dataIndex];
                                return `${item.name}: ${item.percentage}%`;
                            }
                        }
                    }
                },
                animation: {
                    onComplete: function() {
                        // 不再绘制引导线标签，改用卡片图例
                        // drawLabelsWithLines(this, chartData, colors);
                    }
                }
            }
        });

        // 更新中心文字和数据信息
        updateProductShareInfo(chartData, colors);
    }

    // 更新产品销售占比信息显示
    function updateProductShareInfo(chartData, colors) {
        // 更新中心文字
        const centerTextElement = document.getElementById('productShareCenterText');
        if (centerTextElement) {
            const ratioElement = centerTextElement.querySelector('.comparison-ratio');
            const labelElement = centerTextElement.querySelector('.comparison-label');

            if (chartData.length > 0) {
                // 显示最大占比
                const maxPercentage = Math.max(...chartData.map(item => parseFloat(item.percentage)));
                if (ratioElement) ratioElement.textContent = maxPercentage.toFixed(1) + '%';
                if (labelElement) labelElement.textContent = '最高占比';
            } else {
                if (ratioElement) ratioElement.textContent = '0%';
                if (labelElement) labelElement.textContent = '产品占比';
            }
        }

        // 生成产品详细信息卡片
        const legendContainer = document.getElementById('productLegendCards');
        if (legendContainer) {
            legendContainer.innerHTML = '';

            chartData.forEach((item, index) => {
                const card = document.createElement('div');
                card.className = 'product-legend-card';
                card.style.borderLeftColor = colors[index];

                const formatAmount = (amount) => {
                    if (amount >= 10000) {
                        return '¥' + (amount / 10000).toFixed(1) + '万';
                    }
                    return '¥' + new Intl.NumberFormat('zh-CN').format(Math.round(amount));
                };

                card.innerHTML = `
                    <div class="product-color" style="background-color: ${colors[index]}"></div>
                    <div class="product-info">
                        <div class="product-name">${item.name}</div>
                        <div class="product-details">
                            <span class="product-amount">${formatAmount(item.value)}</span>
                            <span class="product-percentage">${item.percentage}%</span>
                        </div>
                    </div>
                `;

                legendContainer.appendChild(card);
            });
        }
    }

    // 绘制带引导线的标签 - 使用改进的防碰撞算法
    function drawLabelsWithLines(chart, chartData, colors) {
        const ctx = chart.ctx;
        const chartArea = chart.chartArea;
        const centerX = (chartArea.left + chartArea.right) / 2;
        const centerY = (chartArea.top + chartArea.bottom) / 2;

        // 检测移动设备并调整参数
        const isMobile = window.innerWidth <= 768;
        const isSmallMobile = window.innerWidth <= 480;

        // 根据屏幕尺寸调整饼图半径
        let radiusMultiplier;
        if (isSmallMobile) {
            radiusMultiplier = 0.5; // 小屏幕上饼图稍大一些
        } else if (isMobile) {
            radiusMultiplier = 0.45;
        } else {
            radiusMultiplier = 0.4;
        }
        const radius = Math.min(chartArea.right - chartArea.left, chartArea.bottom - chartArea.top) / 2 * radiusMultiplier;

        ctx.save();
        // 根据屏幕尺寸调整字体大小 - 增加字体大小提高清晰度
        const fontSize = isSmallMobile ? '11px' : (isMobile ? '12px' : '13px');
        ctx.font = `bold ${fontSize} -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif`;
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
        // 优化字体渲染
        ctx.textRenderingOptimization = 'optimizeLegibility';

        let currentAngle = -Math.PI / 2; // 从顶部开始

        // 预计算所有标签位置
        const labelPositions = [];
        chartData.forEach((item, index) => {
            const percentage = parseFloat(item.percentage);
            const sliceAngle = (percentage / 100) * 2 * Math.PI;
            const midAngle = currentAngle + sliceAngle / 2;
            const isRightSide = Math.cos(midAngle) > 0;

            // 计算初始标签位置 - 根据屏幕尺寸调整引导线长度
            const lineLength = isSmallMobile ? 25 : (isMobile ? 35 : 50);
            const initialY = centerY + Math.sin(midAngle) * (radius + lineLength);

            labelPositions.push({
                name: item.name,
                percentage: item.percentage,
                midAngle,
                isRightSide,
                color: colors[index],
                initialY: initialY,
                finalY: initialY // 初始时等于初始位置
            });

            currentAngle += sliceAngle;
        });

        // 分离左右两侧标签并排序
        const leftLabels = labelPositions.filter(pos => !pos.isRightSide).sort((a, b) => a.initialY - b.initialY);
        const rightLabels = labelPositions.filter(pos => pos.isRightSide).sort((a, b) => a.initialY - b.initialY);

        // 改进的防碰撞算法 - 更智能地调整标签位置
        const adjustLabelsToAvoidCollision = (labels, minSpacing = 18) => {
            if (labels.length <= 1) return;

            // 多轮调整，确保所有标签都有足够间距
            for (let round = 0; round < 3; round++) {
                // 从上到下调整
                for (let i = 1; i < labels.length; i++) {
                    const current = labels[i];
                    const previous = labels[i - 1];

                    if (current.finalY - previous.finalY < minSpacing) {
                        current.finalY = previous.finalY + minSpacing;
                    }
                }

                // 从下到上调整
                for (let i = labels.length - 2; i >= 0; i--) {
                    const current = labels[i];
                    const next = labels[i + 1];

                    if (next.finalY - current.finalY < minSpacing) {
                        current.finalY = next.finalY - minSpacing;
                    }
                }
            }

            // 使用更宽松的边界，确保标签在padding区域内可见
            const maxY = chartArea.bottom - 15;
            const minY = chartArea.top + 15;

            // 边界约束和重新分布
            for (let i = labels.length - 1; i >= 0; i--) {
                if (labels[i].finalY > maxY) {
                    labels[i].finalY = maxY;
                    // 向上推其他标签
                    for (let j = i - 1; j >= 0; j--) {
                        if (labels[j].finalY > labels[j + 1].finalY - minSpacing) {
                            labels[j].finalY = labels[j + 1].finalY - minSpacing;
                        }
                    }
                }
                if (labels[i].finalY < minY) {
                    labels[i].finalY = minY;
                    // 向下推其他标签
                    for (let j = i + 1; j < labels.length; j++) {
                        if (labels[j].finalY < labels[j - 1].finalY + minSpacing) {
                            labels[j].finalY = labels[j - 1].finalY + minSpacing;
                        }
                    }
                }
            }

            // 如果标签太多，进行均匀分布
            if (labels.length > 6) {
                const totalHeight = maxY - minY;
                const spacing = totalHeight / (labels.length - 1);
                for (let i = 0; i < labels.length; i++) {
                    labels[i].finalY = minY + i * spacing;
                }
            }
        };

        // 根据屏幕尺寸调整最小间距 - 增加间距以适应更大的字体
        const minSpacing = isSmallMobile ? 16 : (isMobile ? 18 : 22);
        adjustLabelsToAvoidCollision(leftLabels, minSpacing);
        adjustLabelsToAvoidCollision(rightLabels, minSpacing);

        // 绘制所有标签
        [...leftLabels, ...rightLabels].forEach((pos) => {
            // 计算饼图边缘点
            const pieEdgeX = centerX + Math.cos(pos.midAngle) * radius;
            const pieEdgeY = centerY + Math.sin(pos.midAngle) * radius;

            // 计算引导线延伸点 - 根据屏幕尺寸调整引导线长度
            const lineLength = isSmallMobile ? 25 : (isMobile ? 35 : 50);
            const lineEndX = centerX + Math.cos(pos.midAngle) * (radius + lineLength);

            // 绘制引导线 - 从饼图边缘到调整后的Y位置
            ctx.strokeStyle = pos.color;
            ctx.lineWidth = isSmallMobile ? 1 : 1.5;
            ctx.beginPath();
            ctx.moveTo(pieEdgeX, pieEdgeY);
            ctx.lineTo(lineEndX, pos.finalY);

            // 绘制水平线段 - 根据屏幕尺寸调整水平线段长度
            const horizontalLineLength = isSmallMobile ? 15 : (isMobile ? 20 : 30);
            const horizontalEndX = lineEndX + (pos.isRightSide ? horizontalLineLength : -horizontalLineLength);
            ctx.lineTo(horizontalEndX, pos.finalY);
            ctx.stroke();

            // 绘制标签背景和文字
            const labelText = `${pos.name} ${pos.percentage}%`;
            const textMetrics = ctx.measureText(labelText);
            const textWidth = textMetrics.width;
            const textHeight = isSmallMobile ? 14 : (isMobile ? 16 : 18);

            // 调整标签位置，确保在可见区域内
            let labelX;
            if (pos.isRightSide) {
                labelX = horizontalEndX + 5;
                // 确保右侧标签不超出chartArea边界
                if (labelX + textWidth + 8 > chartArea.right) {
                    labelX = chartArea.right - textWidth - 8;
                }
            } else {
                labelX = horizontalEndX - textWidth - 5;
                // 确保左侧标签不超出chartArea边界
                if (labelX < chartArea.left) {
                    labelX = chartArea.left + 2;
                }
            }

            const bgY = pos.finalY - textHeight / 2;

            // 绘制标签背景
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.fillRect(labelX - 3, bgY - 2, textWidth + 6, textHeight + 4);

            // 绘制标签边框
            ctx.strokeStyle = pos.color;
            ctx.lineWidth = 1.5;
            ctx.strokeRect(labelX - 3, bgY - 2, textWidth + 6, textHeight + 4);

            // 绘制标签文字
            ctx.fillStyle = '#1f2937';
            const labelFontSize = isSmallMobile ? '11px' : (isMobile ? '12px' : '13px');
            ctx.font = `bold ${labelFontSize} -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif`;
            ctx.textAlign = 'left';
            ctx.fillText(labelText, labelX + 2, pos.finalY); // 稍微向右偏移
        });

        ctx.restore();
    }

    // 创建产品占比图例 - 统一样式设计
    function createProductShareLegend(chartData, colors) {
        const legendContainer = document.getElementById('productShareLegend');
        if (!legendContainer) {
            console.error('产品图例容器未找到: productShareLegend');
            return;
        }

        console.log('创建产品图例，数据:', chartData);

        // 使用新的产品图例样式类
        legendContainer.className = 'product-share-legend';

        // 确保容器可见
        legendContainer.style.display = 'flex';
        legendContainer.style.visibility = 'visible';
        legendContainer.style.opacity = '1';

        // 格式化金额显示
        const formatAmount = (amount) => {
            if (amount >= 10000) {
                return (amount / 10000).toFixed(1) + '万';
            }
            return new Intl.NumberFormat('zh-CN').format(Math.round(amount));
        };

        const legendHTML = chartData.map((item, index) => `
            <div class="legend-item">
                <div class="legend-color" style="background-color: ${colors[index]};"></div>
                <span class="legend-label">${item.name}</span>
                <span class="legend-value">销售额: ¥${formatAmount(item.value)}</span>
                <span class="legend-percentage">${item.percentage}%</span>
            </div>
        `).join('');

        console.log('生成的产品图例HTML长度:', legendHTML.length);
        legendContainer.innerHTML = legendHTML;

        // 验证图例是否正确生成
        setTimeout(() => {
            const legendItems = legendContainer.querySelectorAll('.legend-item');
            console.log('产品图例项数量:', legendItems.length);
            if (legendItems.length > 0) {
                console.log('产品图例生成成功，文字应该可见');
            } else {
                console.error('产品图例生成失败');
            }
        }, 100);
    }

    // 更新产品占比饼图
    function updateProductShareChart(products) {
        createProductShareChart(products);
    }

    // 创建同期对比环形图
    function createYoyComparisonChart(actualTotal, lastYearTotal) {
        const ctx = document.getElementById('yoyComparisonChart');
        if (!ctx) return;

        // 格式化货币函数
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(amount || 0);
        };

        const actual = actualTotal || 0;
        const lastYear = lastYearTotal || 0;

        // 计算增长率 - 与表格逻辑保持一致
        let growthRate = 0;
        let growthText = '';
        if (lastYear === 0) {
            // 去年同期为0时，无法计算增长率
            growthRate = 0;
            growthText = '#DIV/0!';
        } else if (lastYear > 0) {
            // 去年同期为正数：正常计算
            growthRate = ((actual - lastYear) / lastYear * 100);
            growthText = `${growthRate >= 0 ? '+' : ''}${growthRate.toFixed(1)}%`;
        } else {
            // 去年同期为负数：特殊处理，与表格逻辑一致
            if (actual >= 0) {
                // 从负数变为正数：改善，使用绝对值计算
                growthRate = ((actual - lastYear) / Math.abs(lastYear) * 100);
                growthText = `+${growthRate.toFixed(1)}%`;
            } else {
                // 都是负数：比较亏损程度
                const absChange = Math.abs(actual) - Math.abs(lastYear);
                growthRate = (absChange / Math.abs(lastYear)) * 100;
                if (absChange > 0) {
                    // 亏损增加
                    growthText = `-${growthRate.toFixed(1)}%`;
                    growthRate = -growthRate; // 设为负数用于颜色判断
                } else if (absChange < 0) {
                    // 亏损减少（改善）
                    growthText = `+${Math.abs(growthRate).toFixed(1)}%`;
                    growthRate = Math.abs(growthRate); // 设为正数用于颜色判断
                } else {
                    growthText = '0.0%';
                    growthRate = 0;
                }
            }
        }

        // 准备图表数据 - 将增长率转换为可视化数据
        // 增长率可能是负数，需要特殊处理
        let positiveGrowth = 0;
        let negativeGrowth = 0;
        let neutralPortion = 50; // 基准线

        if (growthRate >= 0) {
            // 正增长：显示增长部分
            positiveGrowth = Math.min(growthRate, 100); // 限制最大显示100%增长
            negativeGrowth = 0;
        } else {
            // 负增长：显示下降部分
            positiveGrowth = 0;
            negativeGrowth = Math.min(Math.abs(growthRate), 100); // 限制最大显示100%下降
        }

        const remaining = 100 - positiveGrowth - negativeGrowth;

        // 销毁现有图表
        if (yoyComparisonChart) {
            yoyComparisonChart.destroy();
        }

        // 确定颜色
        let color;
        if (growthRate > 0) {
            color = '#22c55e'; // 绿色：正增长
        } else if (growthRate === 0) {
            color = '#6b7280'; // 灰色：无变化
        } else {
            color = '#ef4444'; // 红色：负增长
        }

        // 准备图表数据
        const chartData = [];
        const chartColors = [];

        if (growthRate >= 0) {
            chartData.push(positiveGrowth, remaining);
            chartColors.push(color, '#f1f5f9');
        } else {
            chartData.push(negativeGrowth, remaining);
            chartColors.push(color, '#f1f5f9');
        }

        yoyComparisonChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: chartData,
                    backgroundColor: chartColors,
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });

        // 更新中心文本
        const centerText = document.getElementById('yoyComparisonCenterText');
        if (centerText) {
            const ratioElement = centerText.querySelector('.comparison-ratio');
            if (ratioElement) {
                ratioElement.textContent = growthText;
                ratioElement.style.color = color;
            }
        }

        // 更新下方数据显示
        const currentValueElement = document.getElementById('yoyCurrentValue');
        const previousValueElement = document.getElementById('yoyPreviousValue');
        if (currentValueElement) {
            currentValueElement.textContent = formatCurrency(actual);
        }
        if (previousValueElement) {
            previousValueElement.textContent = formatCurrency(lastYear);
        }
    }

    // 更新同期对比环形图
    function updateYoyComparisonChart(actualTotal, lastYearTotal) {
        createYoyComparisonChart(actualTotal, lastYearTotal);
    }

    // 创建环比增长率环形图
    function createMomComparisonChart(actualTotal, lastPeriodTotal) {
        const ctx = document.getElementById('momComparisonChart');
        if (!ctx) return;

        // 格式化货币函数
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(amount || 0);
        };

        const actual = actualTotal || 0;
        const lastPeriod = lastPeriodTotal || 0;

        // 计算环比增长率 - 与表格逻辑保持一致
        let growthRate = 0;
        let growthText = '';
        if (lastPeriod === 0) {
            // 上期为0时，无法计算增长率
            growthRate = 0;
            growthText = '#DIV/0!';
        } else if (lastPeriod > 0) {
            // 上期为正数：正常计算
            growthRate = ((actual - lastPeriod) / lastPeriod * 100);
            growthText = `${growthRate >= 0 ? '+' : ''}${growthRate.toFixed(1)}%`;
        } else {
            // 上期为负数：特殊处理，与表格逻辑一致
            if (actual >= 0) {
                // 从负数变为正数：改善
                growthRate = ((actual - lastPeriod) / Math.abs(lastPeriod) * 100);
                growthText = `+${growthRate.toFixed(1)}%`;
            } else {
                // 都是负数：比较亏损程度
                const absChange = Math.abs(actual) - Math.abs(lastPeriod);
                growthRate = (absChange / Math.abs(lastPeriod)) * 100;
                if (absChange > 0) {
                    // 亏损增加
                    growthText = `-${growthRate.toFixed(1)}%`;
                    growthRate = -growthRate; // 设为负数用于颜色判断
                } else if (absChange < 0) {
                    // 亏损减少（改善）
                    growthText = `+${Math.abs(growthRate).toFixed(1)}%`;
                    growthRate = Math.abs(growthRate); // 设为正数用于颜色判断
                } else {
                    growthText = '0.0%';
                    growthRate = 0;
                }
            }
        }

        // 准备图表数据 - 将增长率转换为可视化数据
        let positiveGrowth = 0;
        let negativeGrowth = 0;

        if (growthRate >= 0) {
            // 正增长：显示增长部分
            positiveGrowth = Math.min(growthRate, 100); // 限制最大显示100%增长
            negativeGrowth = 0;
        } else {
            // 负增长：显示下降部分
            positiveGrowth = 0;
            negativeGrowth = Math.min(Math.abs(growthRate), 100); // 限制最大显示100%下降
        }

        const remaining = 100 - positiveGrowth - negativeGrowth;

        // 销毁现有图表
        if (momComparisonChart) {
            momComparisonChart.destroy();
        }

        // 确定颜色
        let color;
        if (growthRate > 0) {
            color = '#22c55e'; // 绿色：正增长
        } else if (growthRate === 0) {
            color = '#6b7280'; // 灰色：无变化
        } else {
            color = '#ef4444'; // 红色：负增长
        }

        // 准备图表数据
        const chartData = [];
        const chartColors = [];

        if (growthRate >= 0) {
            chartData.push(positiveGrowth, remaining);
            chartColors.push(color, '#f1f5f9');
        } else {
            chartData.push(negativeGrowth, remaining);
            chartColors.push(color, '#f1f5f9');
        }

        momComparisonChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: chartData,
                    backgroundColor: chartColors,
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });

        // 更新中心文本
        const centerText = document.getElementById('momComparisonCenterText');
        if (centerText) {
            const ratioElement = centerText.querySelector('.comparison-ratio');
            if (ratioElement) {
                ratioElement.textContent = growthText;
                ratioElement.style.color = color;
            }
        }

        // 更新下方数据显示
        const currentValueElement = document.getElementById('momCurrentValue');
        const previousValueElement = document.getElementById('momPreviousValue');
        if (currentValueElement) {
            currentValueElement.textContent = formatCurrency(actual);
        }
        if (previousValueElement) {
            previousValueElement.textContent = formatCurrency(lastPeriod);
        }
    }

    // 更新环比增长率环形图
    function updateMomComparisonChart(actualTotal, lastPeriodTotal) {
        createMomComparisonChart(actualTotal, lastPeriodTotal);
    }

    // 强制图表容器布局
    function forceChartsLayout() {
        const chartsSection = document.getElementById('chartsSection');
        const chartsContainer = document.getElementById('chartsContainer');

        if (chartsSection) {
            chartsSection.style.display = 'block';
        }

        if (chartsContainer) {
            chartsContainer.style.display = 'grid';

            // 检测屏幕尺寸决定布局
            if (window.innerWidth <= 480) {
                // 手机端：单列布局 - 5个图表每行一个
                chartsContainer.style.display = 'grid';
                chartsContainer.style.gridTemplateColumns = '1fr';
                chartsContainer.style.gridTemplateRows = 'repeat(5, auto)';
                chartsContainer.style.gap = '20px';
                chartsContainer.style.padding = '20px 15px';
            } else if (window.innerWidth <= 768) {
                // 平板端：两列布局
                chartsContainer.style.display = 'grid';
                chartsContainer.style.gridTemplateColumns = 'repeat(2, 1fr)';
                chartsContainer.style.gridTemplateRows = 'auto';
                chartsContainer.style.gap = '12px';
                chartsContainer.style.padding = '12px';
            } else {
                // 电脑端：一排四个
                chartsContainer.style.display = 'grid';
                chartsContainer.style.gridTemplateColumns = 'repeat(4, 1fr)';
                chartsContainer.style.gridTemplateRows = 'auto';
                chartsContainer.style.gap = '20px';
                chartsContainer.style.padding = '20px';
            }

            chartsContainer.style.gridTemplateRows = 'auto';

            // 确保所有图表项都正确显示
            const chartItems = chartsContainer.querySelectorAll('.chart-item');
            chartItems.forEach(item => {
                item.style.display = 'flex';
                item.style.flexDirection = 'column';
                item.style.alignItems = 'center';
                item.style.justifyContent = 'center';

                // 手机端特殊样式
                if (window.innerWidth <= 480) {
                    item.style.width = '100%';
                    item.style.maxWidth = '320px';
                    item.style.margin = '0 auto';
                    item.style.padding = '20px 15px';
                    item.style.minHeight = '300px';
                    item.style.background = '#ffffff';
                    item.style.borderRadius = '12px';
                    item.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                }
            });
        }
        
        // 处理医院图表容器布局
        const hospitalChartsContainer = document.getElementById('hospitalSalesChartContainer');
        if (hospitalChartsContainer) {
            if (window.innerWidth <= 480) {
                // 手机端：医院图表垂直排列
                hospitalChartsContainer.style.display = 'grid';
                hospitalChartsContainer.style.gridTemplateColumns = '1fr';
                hospitalChartsContainer.style.gridTemplateRows = 'repeat(2, auto)';
                hospitalChartsContainer.style.gap = '20px';
                hospitalChartsContainer.style.width = '100%';
                
                // 确保医院图表项在手机端正确显示
                const hospitalChartItems = hospitalChartsContainer.querySelectorAll('.chart-item-half');
                hospitalChartItems.forEach(item => {
                    item.style.gridColumn = '1 / -1';
                    item.style.width = '100%';
                    item.style.maxWidth = '100%';
                    item.style.margin = '0';
                    item.style.minHeight = '420px';
                    item.style.padding = '20px 15px';
                });
            } else {
                // 平板端和电脑端：医院图表并排显示
                hospitalChartsContainer.style.display = 'grid';
                hospitalChartsContainer.style.gridTemplateColumns = '1fr 1fr';
                hospitalChartsContainer.style.gridTemplateRows = 'auto';
                hospitalChartsContainer.style.gap = '20px';
                hospitalChartsContainer.style.width = '100%';
                
                // 重置医院图表项样式
                const hospitalChartItems = hospitalChartsContainer.querySelectorAll('.chart-item-half');
                hospitalChartItems.forEach(item => {
                    item.style.gridColumn = '';
                    item.style.width = '';
                    item.style.maxWidth = '';
                    item.style.margin = '';
                    item.style.minHeight = '480px';
                    item.style.padding = '20px';
                });
            }
        }
    }




    // 销售指标对比图表相关函数
    let salesTargetComparisonChart = null;

    // 加载销售指标对比图表数据
    async function loadSalesTargetComparisonChart(filters = {}, dataType = null) {
        const token = localStorage.getItem('authToken');

        // 如果没有指定数据类型，使用当前状态的数据类型
        const currentDataType = dataType || chartDataTypes.salesTarget;

        // 先清空销售指标对比图表
        console.log('清空销售指标对比图表...');
        hideSalesTargetComparisonChart();

        // 检查是否有月份筛选条件
        if (!filters.months || filters.months.length === 0) {
            console.log('没有月份筛选条件，隐藏销售指标对比图表');
            hideSalesTargetComparisonChart();
            return;
        }

        try {
            console.log('=== 销售指标对比图表请求开始 ===');
            console.log('发送销售指标对比图表请求，筛选条件:', filters);
            console.log('筛选条件详情:', JSON.stringify(filters, null, 2));

            const response = await fetch('/api/sales-target-comparison-chart', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(filters)
            });

            if (!response.ok) {
                throw new Error('获取销售指标对比数据失败');
            }

            const data = await response.json();
            console.log('销售指标对比数据响应:', data);

            if (data.success) {
                console.log('销售指标对比数据检查:', {
                    shouldDisplay: data.data.shouldDisplay,
                    chartDataLength: data.data.chartData.length,
                    chartData: data.data.chartData
                });

                if (data.data.shouldDisplay && data.data.chartData.length > 0) {
                    console.log('显示销售指标对比图表，数据条数:', data.data.chartData.length);
                    showSalesTargetComparisonChart();
                    renderSalesTargetComparisonChart(data.data.chartData, currentDataType);
                } else {
                    console.log('隐藏销售指标对比图表，原因:', {
                        shouldDisplay: data.data.shouldDisplay,
                        dataLength: data.data.chartData.length
                    });
                    hideSalesTargetComparisonChart();
                }
            } else {
                throw new Error(data.error || '获取销售指标对比数据失败');
            }
        } catch (error) {
            console.error('加载销售指标对比图表失败:', error);
            hideSalesTargetComparisonChart();
        }
    }

    // 显示销售指标对比图表
    function showSalesTargetComparisonChart() {
        console.log('尝试显示销售指标对比图表容器');
        const section = document.getElementById('salesTargetComparisonSection');
        const container = document.getElementById('salesTargetComparisonContainer');

        console.log('找到区域:', section);
        console.log('找到容器:', container);

        if (section) {
            section.style.display = 'block';
            console.log('区域显示状态已设置为 block');
        }

        if (container) {
            container.style.display = 'block';
            console.log('容器显示状态已设置为 block');

            // 确保折叠按钮状态正确
            const toggleBtn = document.getElementById('toggleSalesTargetComparison');
            const toggleText = toggleBtn ? toggleBtn.querySelector('.toggle-text') : null;
            const content = document.getElementById('salesTargetComparisonContent');

            if (toggleBtn && toggleText && content) {
                // 默认展开状态
                content.classList.remove('collapsed');
                toggleBtn.classList.remove('collapsed');
                toggleText.textContent = '折叠';
                content.style.maxHeight = 'none';

                // 确保标题可见
                const sectionHeader = toggleBtn.closest('.section-header');
                const titleElement = sectionHeader ? sectionHeader.querySelector('h3, h4') : null;
                if (titleElement) {
                    titleElement.style.opacity = '1';
                }
            }
        } else {
            console.error('未找到销售指标对比容器元素');
        }
    }

    // 隐藏销售指标对比图表
    function hideSalesTargetComparisonChart() {
        console.log('执行隐藏销售指标对比图表容器');
        const section = document.getElementById('salesTargetComparisonSection');
        if (section) {
            section.style.display = 'none';
            console.log('销售指标对比区域已隐藏');
        } else {
            console.error('找不到销售指标对比区域元素');
        }

        // 销毁现有图表 - 检查变量是否已经初始化
        try {
            if (typeof salesTargetComparisonChart !== 'undefined' && salesTargetComparisonChart) {
                salesTargetComparisonChart.destroy();
                salesTargetComparisonChart = null;
            }
        } catch (error) {
            // 如果变量还没有初始化，忽略错误
            console.log('📝 salesTargetComparisonChart 变量还未初始化，跳过销毁操作');
        }
    }

    // 渲染销售指标对比图表
    function renderSalesTargetComparisonChart(chartData, dataType = null) {
        const currentDataType = dataType || chartDataTypes.salesTarget;
        console.log('开始渲染销售指标对比图表，数据类型:', currentDataType, '数据:', chartData);
        // 销毁现有图表
        if (salesTargetComparisonChart) {
            console.log('销毁现有销售指标对比图表');
            salesTargetComparisonChart.destroy();
            salesTargetComparisonChart = null;
        }

        // 重新创建Canvas元素以避免重用问题
        const oldCanvas = document.getElementById('salesTargetComparisonChart');
        if (oldCanvas) {
            const container = oldCanvas.parentNode;
            const newCanvas = document.createElement('canvas');
            newCanvas.id = 'salesTargetComparisonChart';
            newCanvas.style.display = 'block';
            newCanvas.style.boxSizing = 'border-box';
            newCanvas.style.height = '350px';
            newCanvas.style.width = '2333px';
            container.replaceChild(newCanvas, oldCanvas);
            console.log('已重新创建Canvas元素');
        }

        const canvas = document.getElementById('salesTargetComparisonChart');
        console.log('找到canvas元素:', canvas);
        if (!canvas) {
            console.error('销售指标对比图表canvas元素未找到');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 准备图表数据
        const labels = chartData.map(item => item.period || item.month);
        const salesAmountData = chartData.map(item => item.salesAmount || 0);
        const targetAmountData = chartData.map(item => item.targetAmount || 0);
        const salesQuantityData = chartData.map(item => item.salesQuantity || 0);
        const targetBoxConvertedData = chartData.map(item => item.targetBoxConverted || 0);
        const qpData = chartData.map(item => {
            // 从百分比字符串中提取数值 (如 "75.0%" -> 75)
            const qpValue = item.qpValue || '0.0%';
            return parseFloat(qpValue.replace('%', ''));
        });

        console.log('当前显示模式:', currentDataType);

        // 根据显示模式设置数据和标签
        let salesDataToShow, targetDataToShow, salesLabel, targetLabel, yAxisTitle;
        if (currentDataType === 'quantity') {
            salesDataToShow = salesQuantityData;
            targetDataToShow = targetBoxConvertedData;
            salesLabel = '销量盒折算后';
            targetLabel = '指标盒折算后';
            yAxisTitle = '盒数';
        } else {
            salesDataToShow = salesAmountData;
            targetDataToShow = targetAmountData;
            salesLabel = '销售金额折算后';
            targetLabel = '指标金额';
            yAxisTitle = '金额 (元)';
        }
        
        console.log('图表数据:', {
            salesDataToShow: salesDataToShow.slice(0, 3),
            targetDataToShow: targetDataToShow.slice(0, 3),
            salesLabel,
            targetLabel
        });

        // 创建图表配置
        const chartConfig = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: salesLabel,
                    data: salesDataToShow,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: targetLabel,
                    data: targetDataToShow,
                    backgroundColor: 'rgba(251, 191, 36, 0.8)',
                    borderColor: '#fbbf24',
                    borderWidth: 1,
                    borderRadius: 4,
                    yAxisID: 'y'
                }, {
                    label: 'QP值',
                    data: qpData,
                    type: 'line',
                    backgroundColor: 'rgba(124, 58, 237, 0.1)',
                    borderColor: '#7c3aed',
                    borderWidth: 3,
                    pointBackgroundColor: '#7c3aed',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '销售指标对比分析',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.dataset.label === 'QP值') {
                                    return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
                                } else {
                                    let value;
                                    if (currentDataType === 'quantity') {
                                        value = new Intl.NumberFormat('zh-CN').format(context.parsed.y);
                                    } else {
                                        value = formatCurrency(context.parsed.y);
                                    }
                                    return `${context.dataset.label}: ${value}`;
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                if (currentDataType === 'quantity') {
                                    return new Intl.NumberFormat('zh-CN').format(value);
                                } else {
                                    return formatCurrency(value);
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: yAxisTitle
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                                                    return value.toFixed(1);
                            }
                        },
                        title: {
                            display: true,
                            text: 'QP值'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        };

        // 创建图表
        try {
            salesTargetComparisonChart = new Chart(ctx, chartConfig);

            // 存储原始数据以便切换显示模式
            salesTargetComparisonChart.config.data.chartData = chartData;

            // 更新销售指标数据表格
            updateSalesTargetTable(chartData);



            console.log('销售指标对比图表渲染完成');
        
        // 刷新数据标签控制按钮
        setTimeout(() => {
            if (window.chartDataLabelsController) {
                window.chartDataLabelsController.refreshButtons();
            }
        }, 100);
        } catch (error) {
            console.error('创建销售指标对比图表失败:', error);
            // 如果创建失败，尝试重新创建Canvas
            const container = canvas.parentNode;
            const newCanvas = document.createElement('canvas');
            newCanvas.id = 'salesTargetComparisonChart';
            newCanvas.width = canvas.width;
            newCanvas.height = canvas.height;
            container.replaceChild(newCanvas, canvas);
            console.log('已重新创建Canvas元素');
        }
    }

    // 更新销售指标数据表格
    function updateSalesTargetTable(chartData) {
        const tableBody = document.getElementById('salesTargetTableBody');

        if (!tableBody) {
            console.error('销售指标表格元素未找到');
            return;
        }

        // 清空现有数据
        tableBody.innerHTML = '';

        if (!chartData || chartData.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #6b7280;">暂无数据</td></tr>';
            return;
        }

        // 获取显示模式
        const displayModeSelect = document.getElementById('salesTargetDisplayMode');
        const displayMode = displayModeSelect ? displayModeSelect.value : 'amount';

        // 更新表格列标题
        const salesColumnHeader = document.getElementById('salesColumnHeader');
        const targetColumnHeader = document.getElementById('targetColumnHeader');
        
        if (salesColumnHeader && targetColumnHeader) {
            if (displayMode === 'quantity') {
                salesColumnHeader.textContent = '销量盒折算后';
                targetColumnHeader.textContent = '指标盒折算后';
            } else {
                salesColumnHeader.textContent = '销售金额折算后';
                targetColumnHeader.textContent = '指标金额';
            }
        }

        // 生成表格行
        chartData.forEach(item => {
            const row = document.createElement('tr');

            // 计算完成率 - 修改逻辑：当指标为0时显示"-"
            let completionDisplay, completionClass;
            if (displayMode === 'quantity') {
                if (item.targetBoxConverted > 0) {
                    const completionRate = ((item.salesQuantity / item.targetBoxConverted) * 100);
                    completionDisplay = completionRate.toFixed(1) + '%';
                    completionClass = completionRate >= 100 ? 'completion-high' :
                                    completionRate >= 80 ? 'completion-medium' : 'completion-low';
                } else {
                    completionDisplay = '-';
                    completionClass = 'completion-none';
                }
            } else {
                if (item.targetAmount > 0) {
                    const completionRate = ((item.salesAmount / item.targetAmount) * 100);
                    completionDisplay = completionRate.toFixed(1) + '%';
                    completionClass = completionRate >= 100 ? 'completion-high' :
                                    completionRate >= 80 ? 'completion-medium' : 'completion-low';
                } else {
                    completionDisplay = '-';
                    completionClass = 'completion-none';
                }
            }

            // 格式化数值
            const formatValue = (value) => {
                if (displayMode === 'quantity') {
                    return new Intl.NumberFormat('zh-CN').format(value);
                } else {
                    return formatCurrency(value);
                }
            };

            // 根据显示模式确定主要对比数据
            let primarySalesValue, primaryTargetValue;
            if (displayMode === 'quantity') {
                primarySalesValue = new Intl.NumberFormat('zh-CN', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                }).format(item.salesQuantity || 0);
                primaryTargetValue = new Intl.NumberFormat('zh-CN', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                }).format(item.targetBoxConverted || 0);
            } else {
                primarySalesValue = formatCurrency(item.salesAmount || 0);
                primaryTargetValue = formatCurrency(item.targetAmount || 0);
            }

            row.innerHTML = `
                <td><strong>${item.period || item.month}</strong></td>
                <td class="amount ${displayMode === 'quantity' ? 'secondary' : 'primary'}">${formatCurrency(item.salesAmount || 0)}</td>
                <td class="amount ${displayMode === 'quantity' ? 'secondary' : 'primary'}">${formatCurrency(item.targetAmount || 0)}</td>
                <td class="amount ${displayMode === 'quantity' ? 'primary' : 'secondary'}">${new Intl.NumberFormat('zh-CN', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                }).format(item.salesQuantity || 0)}</td>
                <td class="amount ${displayMode === 'quantity' ? 'primary' : 'secondary'}">${new Intl.NumberFormat('zh-CN', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                }).format(item.targetBoxConverted || 0)}</td>
                <td class="completion-rate ${completionClass}">${completionDisplay}</td>
                <td class="qp-value">${item.qpValue || '0.0%'}</td>
            `;

            tableBody.appendChild(row);
        });

        // 计算汇总数据
        const totalSalesAmount = chartData.reduce((sum, item) => sum + (item.salesAmount || 0), 0);
        const totalTargetAmount = chartData.reduce((sum, item) => sum + (item.targetAmount || 0), 0);
        const totalSalesQuantity = chartData.reduce((sum, item) => sum + (item.salesQuantity || 0), 0);
        const totalTargetBoxConverted = chartData.reduce((sum, item) => sum + (item.targetBoxConverted || 0), 0);

        // 计算汇总完成率 - 修改逻辑：当指标为0时显示"-"
        let totalCompletionDisplay, totalCompletionClass;
        if (displayMode === 'quantity') {
            if (totalTargetBoxConverted > 0) {
                const totalCompletionRate = ((totalSalesQuantity / totalTargetBoxConverted) * 100);
                totalCompletionDisplay = totalCompletionRate.toFixed(1) + '%';
                totalCompletionClass = totalCompletionRate >= 100 ? 'completion-high' :
                                     totalCompletionRate >= 80 ? 'completion-medium' : 'completion-low';
            } else {
                totalCompletionDisplay = '-';
                totalCompletionClass = 'completion-none';
            }
        } else {
            if (totalTargetAmount > 0) {
                const totalCompletionRate = ((totalSalesAmount / totalTargetAmount) * 100);
                totalCompletionDisplay = totalCompletionRate.toFixed(1) + '%';
                totalCompletionClass = totalCompletionRate >= 100 ? 'completion-high' :
                                     totalCompletionRate >= 80 ? 'completion-medium' : 'completion-low';
            } else {
                totalCompletionDisplay = '-';
                totalCompletionClass = 'completion-none';
            }
        }

        // 计算平均QP值（如果有QP值数据）
        const validQpValues = chartData.filter(item => item.qpValue && item.qpValue !== '0.0%');
        let avgQpValue = '0.0%';
        if (validQpValues.length > 0) {
            const qpSum = validQpValues.reduce((sum, item) => {
                const qpNum = parseFloat(item.qpValue.replace('%', ''));
                return sum + qpNum;
            }, 0);
            avgQpValue = (qpSum / validQpValues.length).toFixed(1) + '%';
        }

        // 创建汇总行
        const summaryRow = document.createElement('tr');
        summaryRow.className = 'summary-row';
        summaryRow.style.cssText = 'background-color: #f8f9fa; font-weight: bold; border-top: 2px solid #dee2e6;';

        summaryRow.innerHTML = `
            <td style="font-weight: bold; color: #495057;">汇总</td>
            <td class="amount ${displayMode === 'quantity' ? 'secondary' : 'primary'}" style="font-weight: bold;">${formatCurrency(totalSalesAmount)}</td>
            <td class="amount ${displayMode === 'quantity' ? 'secondary' : 'primary'}" style="font-weight: bold;">${formatCurrency(totalTargetAmount)}</td>
            <td class="amount ${displayMode === 'quantity' ? 'primary' : 'secondary'}" style="font-weight: bold;">${new Intl.NumberFormat('zh-CN', {
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(totalSalesQuantity)}</td>
            <td class="amount ${displayMode === 'quantity' ? 'primary' : 'secondary'}" style="font-weight: bold;">${new Intl.NumberFormat('zh-CN', {
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            }).format(totalTargetBoxConverted)}</td>
            <td class="completion-rate ${totalCompletionClass}" style="font-weight: bold;">${totalCompletionDisplay}</td>
            <td class="qp-value" style="font-weight: bold;">${avgQpValue}</td>
        `;

        tableBody.appendChild(summaryRow);

        console.log('销售指标数据表格更新完成，包含汇总行');
        console.log('汇总数据:', {
            totalSalesAmount,
            totalTargetAmount,
            totalSalesQuantity,
            totalTargetBoxConverted,
            totalCompletionRate: totalCompletionDisplay,
            avgQpValue
        });
    }



    // 格式化货币函数
    function formatCurrency(value) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 1,
            maximumFractionDigits: 1
        }).format(value);
    }

    // ===========================================
    // 数据导入功能
    // ===========================================
    
    let uploadedData = null;
    let currentStep = 1;

    function initImportPage() {
        if (document.getElementById('import-content')) {
            setupImportEventListeners();
            loadImportUserInfo();
        }
    }

    function setupImportEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const removeFileBtn = document.getElementById('removeFile');
        const confirmImportBtn = document.getElementById('confirmImport');

        if (fileInput) {
            fileInput.addEventListener('change', handleFileSelect);
        }

        if (uploadArea) {
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
        }

        if (removeFileBtn) {
            removeFileBtn.addEventListener('click', removeFile);
        }

        if (confirmImportBtn) {
            confirmImportBtn.addEventListener('click', confirmImport);
        }
    }

    function loadImportUserInfo() {
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const uploaderElement = document.getElementById('uploader');
        if (uploaderElement) {
            uploaderElement.textContent = userInfo.full_name || userInfo.real_name || userInfo.username || '当前用户';
        }
    }

    function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            uploadFile(file);
        }
    }

    function handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }

    function handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');
    }

    function handleDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            uploadFile(files[0]);
        }
    }

    function uploadFile(file) {
        // 验证文件类型
        const allowedTypes = ['.xlsx', '.xls', '.csv'];
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExt)) {
            alert('只支持 Excel (.xlsx, .xls) 和 CSV 文件');
            return;
        }

        // 验证文件大小
        if (file.size > 10 * 1024 * 1024) {
            alert('文件大小不能超过 10MB');
            return;
        }

        // 显示文件信息
        showFileInfo(file);

        // 上传文件
        const formData = new FormData();
        formData.append('file', file);

        const token = localStorage.getItem('authToken');
        
        showUploadProgress();

        fetch('/api/import/upload', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideUploadProgress();
            
            if (data.success) {
                uploadedData = data.data;
                showPreview(data.data);
                goToStep(2);
            } else {
                alert('文件上传失败: ' + data.error);
                removeFile();
            }
        })
        .catch(error => {
            hideUploadProgress();
            console.error('上传错误:', error);
            alert('文件上传失败，请重试');
            removeFile();
        });
    }

    function showFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        if (fileName) fileName.textContent = file.name;
        if (fileSize) fileSize.textContent = formatFileSize(file.size);
        if (fileInfo) fileInfo.style.display = 'flex';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    function showUploadProgress() {
        const uploadProgress = document.getElementById('uploadProgress');
        const progressFill = document.getElementById('progressFill');
        
        if (uploadProgress) {
            uploadProgress.style.display = 'block';
            
            // 模拟进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 30;
                if (progress >= 90) {
                    progress = 90;
                    clearInterval(interval);
                }
                if (progressFill) progressFill.style.width = progress + '%';
            }, 200);
        }
    }

    function hideUploadProgress() {
        const uploadProgress = document.getElementById('uploadProgress');
        const progressFill = document.getElementById('progressFill');
        
        if (progressFill) progressFill.style.width = '100%';
        setTimeout(() => {
            if (uploadProgress) uploadProgress.style.display = 'none';
            if (progressFill) progressFill.style.width = '0%';
        }, 500);
    }

    function removeFile() {
        const fileInfo = document.getElementById('fileInfo');
        const fileInput = document.getElementById('fileInput');
        
        if (fileInfo) fileInfo.style.display = 'none';
        if (fileInput) fileInput.value = '';
        uploadedData = null;
        
        goToStep(1);
    }

    function showPreview(data) {
        const previewStats = document.getElementById('previewStats');
        const previewTableHead = document.getElementById('previewTableHead');
        const previewTableBody = document.getElementById('previewTableBody');

        // 显示统计信息
        if (previewStats) {
            previewStats.textContent = `共 ${data.totalRows} 条记录，显示前 ${Math.min(5, data.totalRows)} 条`;
        }

        // 显示表头
        if (data.columns && data.columns.length > 0 && previewTableHead) {
            const headerRow = document.createElement('tr');
            data.columns.forEach(column => {
                const th = document.createElement('th');
                th.textContent = column;
                headerRow.appendChild(th);
            });
            previewTableHead.innerHTML = '';
            previewTableHead.appendChild(headerRow);
        }

        // 显示数据
        if (previewTableBody) {
            previewTableBody.innerHTML = '';
            if (data.preview && data.preview.length > 0) {
                data.preview.forEach(row => {
                    const tr = document.createElement('tr');
                    data.columns.forEach(column => {
                        const td = document.createElement('td');
                        td.textContent = row[column] || '';
                        td.title = row[column] || '';
                        tr.appendChild(td);
                    });
                    previewTableBody.appendChild(tr);
                });
            }
        }

        // 更新导入摘要
        updateImportSummary(data);
    }

    function updateImportSummary(data) {
        const totalRecords = document.getElementById('totalRecords');
        const dataFields = document.getElementById('dataFields');

        if (totalRecords) {
            totalRecords.textContent = data.totalRows;
        }
        
        if (dataFields && data.columns) {
            dataFields.textContent = data.columns.join(', ');
        }
    }

    function confirmImport() {
        if (!uploadedData || (!uploadedData.fullData && !uploadedData.preview)) {
            alert('没有可导入的数据');
            return;
        }

        const importData = uploadedData.fullData || uploadedData.preview;
        if (!importData || importData.length === 0) {
            alert('导入数据为空');
            return;
        }

        const confirmBtn = document.getElementById('confirmImport');
        if (confirmBtn) {
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导入中...';
        }

        const token = localStorage.getItem('authToken');

        fetch('/api/import/confirm', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                data: uploadedData.fullData || uploadedData.preview,
                mapping: null
            })
        })
        .then(response => response.json())
        .then(data => {
            showImportResult(data);
        })
        .catch(error => {
            console.error('导入错误:', error);
            showImportResult({
                success: false,
                error: '导入失败，请重试'
            });
        })
        .finally(() => {
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="fas fa-check"></i> 开始导入';
            }
        });
    }

    function showImportResult(result) {
        const importResult = document.getElementById('importResult');
        const resultIcon = document.getElementById('resultIcon');
        const resultMessage = document.getElementById('resultMessage');
        const resultDetails = document.getElementById('resultDetails');

        if (result.success) {
            if (resultIcon) resultIcon.innerHTML = '<i class="fas fa-check-circle success"></i>';
            if (resultMessage) resultMessage.textContent = '导入成功！';
            if (resultDetails) resultDetails.textContent = result.message || '数据已成功导入到系统中';
        } else {
            if (resultIcon) resultIcon.innerHTML = '<i class="fas fa-times-circle error"></i>';
            if (resultMessage) resultMessage.textContent = '导入失败';
            if (resultDetails) resultDetails.textContent = result.error || '导入过程中发生错误';
        }

        if (importResult) importResult.style.display = 'block';
        
        // 隐藏其他内容
        const importSummary = document.getElementById('importSummary');
        const importActions = document.querySelector('.import-actions');
        if (importSummary) importSummary.style.display = 'none';
        if (importActions) importActions.style.display = 'none';
    }

    function goToStep(step) {
        // 隐藏所有步骤内容
        document.querySelectorAll('.step-content').forEach(content => {
            content.classList.remove('active');
        });

        // 更新步骤指示器
        document.querySelectorAll('.step').forEach((stepEl, index) => {
            stepEl.classList.remove('active', 'completed');
            if (index + 1 < step) {
                stepEl.classList.add('completed');
            } else if (index + 1 === step) {
                stepEl.classList.add('active');
            }
        });

        // 显示当前步骤内容
        const currentStepContent = document.getElementById(`step${step}`);
        if (currentStepContent) {
            currentStepContent.classList.add('active');
        }
        currentStep = step;
    }

    // 全局函数用于HTML中的onclick调用
    window.goToStep = goToStep;

    // 导出函数到全局作用域
    window.dashboard = {
        resetFilters,
        updateMultiSelectDisplay,
        updateSelectAllState,
        toggleSelectAll,
        toggleOption,
        showMonthlyComparisonChart,
        hideMonthlyComparisonChart,
        showMonthlyMomChart,
        hideMonthlyMomChart,
        showSalesTargetComparisonChart,
        hideSalesTargetComparisonChart,
        initImportPage,
        goToStep,
        showClearDataModal,
        closeClearDataModal,
        confirmClearData
    };
});

// 清空数据相关函数
function showClearDataModal() {
    const modal = document.getElementById('clearDataModal');
    const confirmText = document.getElementById('confirmText');
    const confirmBtn = document.getElementById('confirmClearBtn');

    if (modal) {
        modal.style.display = 'flex';

        // 重置输入框和按钮状态
        confirmText.value = '';
        confirmBtn.disabled = true;
        confirmText.classList.remove('valid');

        // 监听输入变化
        confirmText.addEventListener('input', function() {
            const isValid = this.value.toUpperCase() === 'CLEAR';
            confirmBtn.disabled = !isValid;

            if (isValid) {
                this.classList.add('valid');
            } else {
                this.classList.remove('valid');
            }
        });

        // 聚焦到输入框
        setTimeout(() => {
            confirmText.focus();
        }, 100);
    }
}

function closeClearDataModal() {
    const modal = document.getElementById('clearDataModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

async function confirmClearData() {
    const confirmText = document.getElementById('confirmText');
    const confirmBtn = document.getElementById('confirmClearBtn');

    if (confirmText.value.toUpperCase() !== 'CLEAR') {
        showError('请输入 CLEAR 来确认操作');
        return;
    }

    // 显示加载状态
    const originalText = confirmBtn.innerHTML;
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清空中...';
    confirmBtn.disabled = true;

    try {
        const token = localStorage.getItem('authToken');
        const response = await fetch('/api/clear-sales-data', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (response.ok && data.success) {
            closeClearDataModal();
            // 延迟显示成功提示，确保模态框完全关闭后再显示
            setTimeout(() => {
                showSuccess(`数据清空成功！共删除了 ${data.deletedCount} 条记录`);
            }, 300);

            // 如果当前在销售数据页面，刷新数据
            const currentPage = document.querySelector('.content-section.active');
            if (currentPage && currentPage.id === 'sales-content') {
                // 清空当前显示的数据
                clearSalesTable();
                resetProductAnalysisTable();
                resetPersonnelAnalysisTable();
                hideMonthlyComparisonChart();
                hideMonthlyMomChart();
                hideSalesTargetComparisonChart();
            }
        } else {
            throw new Error(data.error || '清空数据失败');
        }
    } catch (error) {
        console.error('清空数据失败:', error);
        showError('清空数据失败: ' + error.message);
    } finally {
        // 恢复按钮状态
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    }
}

// 气泡图相关功能
let personnelBubbleChart = null;
let productBubbleChart = null;

// 在dashboard中渲染气泡图
function renderBubbleChartsInDashboard(products, personnel) {
    console.log('=== 开始渲染气泡图 ===');
    console.log('产品数据:', products);
    console.log('人员数据:', personnel);

    if ((!products || products.length === 0) && (!personnel || personnel.length === 0)) {
        console.log('没有产品和人员数据，无法渲染气泡图');
        hideBubbleChartsContainer();
        return;
    }

    // 检查是否所有数据都是0
    const allProductsZero = !products || products.length === 0 || products.every(p => (p.actual || 0) === 0);
    const allPersonnelZero = !personnel || personnel.length === 0 || personnel.every(p => (p.actual || 0) === 0);

    if (allProductsZero && allPersonnelZero) {
        console.log('所有产品和人员数据都是0，隐藏气泡图容器');
        hideBubbleChartsContainer();
        return;
    }

    // 显示气泡图区域
    const bubbleSection = document.getElementById('bubbleChartsSection');
    const bubbleContainer = document.getElementById('bubbleChartsContainer');

    console.log('气泡图区域:', bubbleSection);
    console.log('气泡图容器:', bubbleContainer);

    if (bubbleSection) {
        bubbleSection.style.display = 'block';
        console.log('气泡图区域已显示');
    }

    if (bubbleContainer) {
        bubbleContainer.style.display = 'block';
        console.log('气泡图容器已显示');
    } else {
        console.error('未找到气泡图容器元素');
        return;
    }

    // 确保折叠按钮状态正确
    const toggleBtn = document.getElementById('toggleBubbleCharts');
    const toggleText = toggleBtn ? toggleBtn.querySelector('.toggle-text') : null;
    const content = document.getElementById('bubbleChartsContent');

    if (toggleBtn && toggleText && content) {
        // 默认展开状态
        content.classList.remove('collapsed');
        toggleBtn.classList.remove('collapsed');
        toggleText.textContent = '折叠';
        content.style.maxHeight = 'none';

        // 确保标题可见
        const sectionHeader = toggleBtn.closest('.section-header');
        const titleElement = sectionHeader ? sectionHeader.querySelector('h3, h4') : null;
        if (titleElement) {
            titleElement.style.opacity = '1';
        }
    }

    // 初始化切换按钮事件
    initChartModeToggle();

    // 默认显示2D图表
    show2DCharts();

    // 渲染传统2D气泡图
    renderPersonnelBubbleChartInDashboard(personnel || []);
    renderProductBubbleChartInDashboard(products || []);

    // 准备3D数据（但不立即渲染）
    window.currentPersonnelData = personnel || [];
    window.currentProductData = products || [];
}

// 隐藏气泡图容器
function hideBubbleChartsContainer() {
    console.log('🔒 隐藏气泡图容器并销毁图表');
    const bubbleSection = document.getElementById('bubbleChartsSection');
    if (bubbleSection) {
        bubbleSection.style.display = 'none';
        console.log('✅ 气泡图区域已隐藏');
    } else {
        console.error('❌ bubbleChartsSection 元素未找到');
    }

    // 销毁现有图表
    if (personnelBubbleChart) {
        console.log('销毁人员气泡图');
        personnelBubbleChart.destroy();
        personnelBubbleChart = null;
    }
    if (productBubbleChart) {
        console.log('销毁产品气泡图');
        productBubbleChart.destroy();
        productBubbleChart = null;
    }
}

// 生成图表颜色（全局函数）
function generateChartColors(count) {
    const colors = [
        '#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6',
        '#06b6d4', '#f97316', '#84cc16', '#ec4899', '#6366f1',
        '#14b8a6', '#f43f5e', '#a855f7', '#10b981', '#f59e0b',
        '#3b82f6', '#ef4444', '#22c55e', '#8b5cf6', '#06b6d4'
    ];

    // 如果数量超过预定义颜色，生成随机颜色
    if (count > colors.length) {
        for (let i = colors.length; i < count; i++) {
            const hue = (i * 137.508) % 360; // 黄金角度分布
            colors.push(`hsl(${hue}, 70%, 50%)`);
        }
    }

    return colors.slice(0, count);
}

// 渲染人员气泡图
function renderPersonnelBubbleChartInDashboard(personnel) {
    const canvas = document.getElementById('personnelBubbleChart');
    if (!canvas) {
        console.error('人员气泡图canvas未找到');
        return;
    }

    // 销毁现有图表和清理事件监听器
    if (personnelBubbleChart) {
        personnelBubbleChart.destroy();
        console.log('销毁了现有的人员气泡图实例');
    }

    // 清理旧的事件监听器
    const newCanvas = canvas.cloneNode(true);
    canvas.parentNode.replaceChild(newCanvas, canvas);
    const cleanCanvas = document.getElementById('personnelBubbleChart');
    console.log('清理了人员气泡图canvas的所有事件监听器');

    // 使用真实的人员数据
    const personnelData = preparePersonnelBubbleDataInDashboard(personnel);

    console.log('人员气泡图数据:', personnelData);

    // 检查是否所有数据都是0，如果是则隐藏气泡图容器
    if (personnelData.length === 0 || personnelData.every(person => person.sales === 0)) {
        console.log('所有人员数据都是0，隐藏气泡图容器');
        hideBubbleChartsContainer();
        return;
    }

    // 如果有有效数据，确保气泡图容器是显示的
    const bubbleSection = document.getElementById('bubbleChartsSection');
    if (bubbleSection) {
        bubbleSection.style.display = 'block';
    }

    // 为每个气泡生成不同的颜色
    const colors = generateChartColors(personnelData.length);
    const backgroundColors = colors.map(color => {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, 0.6)`;
        } else if (color.startsWith('hsl')) {
            return color.replace('hsl', 'hsla').replace(')', ', 0.6)');
        }
        return color;
    });

    const borderColors = colors.map(color => {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, 1)`;
        } else if (color.startsWith('hsl')) {
            return color.replace('hsl', 'hsla').replace(')', ', 1)');
        }
        return color;
    });

    const ctx = cleanCanvas.getContext('2d');
    personnelBubbleChart = new Chart(ctx, {
        type: 'bubble',
        data: {
            datasets: [{
                label: '人员表现',
                data: personnelData,
                backgroundColor: backgroundColors,
                borderColor: borderColors,
                borderWidth: 2
            }]
        },
        options: getPersonnelBubbleChartOptions('人员表现分析', personnelData),
        plugins: [{
            id: 'personnelLabels',
            afterDatasetsDraw: function(chart) {
                const ctx = chart.ctx;
                const meta = chart.getDatasetMeta(0);

                ctx.save();
                ctx.textAlign = 'left';
                ctx.textBaseline = 'middle';

                meta.data.forEach((point, index) => {
                    const data = personnelData[index];
                    if (data && data.label) {
                        // 获取图表区域边界
                        const chartArea = chart.chartArea;
                        
                        // 预计算标签宽度
                        ctx.font = 'bold 12px Arial';
                        const nameWidth = ctx.measureText(data.label).width;
                        ctx.font = '11px Arial';
                        const salesText = `¥${(data.sales / 10000).toFixed(1)}万`;
                        const salesWidth = ctx.measureText(salesText).width;
                        const maxWidth = Math.max(nameWidth, salesWidth);

                        // 计算标签位置，确保完全在图表区域内
                        let x, y = point.y;
                        let textAlign = 'left';

                        // 首先尝试右侧位置
                        const rightX = point.x + data.r + 8;
                        if (rightX + maxWidth + 8 <= chartArea.right) {
                            x = rightX;
                            textAlign = 'left';
                        } else {
                            // 右侧不够，尝试左侧
                            const leftX = point.x - data.r - 8;
                            if (leftX - maxWidth - 8 >= chartArea.left) {
                                x = leftX;
                                textAlign = 'right';
                            } else {
                                // 左右都不够，放在上方或下方
                                x = Math.max(chartArea.left + 8, Math.min(point.x - maxWidth/2, chartArea.right - maxWidth - 8));
                                textAlign = 'left';

                                // 尝试上方
                                if (point.y - data.r - 40 >= chartArea.top + 20) {
                                    y = point.y - data.r - 25;
                                } else {
                                    // 放在下方
                                    y = point.y + data.r + 25;
                                }
                            }
                        }

                        // 最终边界检查
                        y = Math.max(chartArea.top + 20, Math.min(y, chartArea.bottom - 20));

                        // 如果标签位置仍然会超出边界，则跳过绘制
                        const finalBgX = textAlign === 'right' ? x - maxWidth - 6 : x - 2;
                        if (finalBgX < chartArea.left || finalBgX + maxWidth + 8 > chartArea.right ||
                            y - 16 < chartArea.top || y + 16 > chartArea.bottom) {
                            return; // 跳过无法完全显示的标签
                        }

                        ctx.textAlign = textAlign;

                        // 准备显示的文本
                        const nameText = data.label;
                        const bgHeight = 32; // 两行文字的高度

                        // 根据文字对齐方式调整背景和文字位置
                        let bgX, textX;
                        if (textAlign === 'right') {
                            bgX = x - maxWidth - 6;
                            textX = x - 2;
                        } else {
                            bgX = x - 2;
                            textX = x + 2;
                        }

                        // 绘制白色背景
                        ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
                        ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
                        ctx.lineWidth = 1;
                        ctx.fillRect(bgX, y - bgHeight/2, maxWidth + 8, bgHeight);
                        ctx.strokeRect(bgX, y - bgHeight/2, maxWidth + 8, bgHeight);

                        // 绘制人名（第一行）
                        ctx.fillStyle = '#1f2937';
                        ctx.font = 'bold 12px Arial';
                        ctx.fillText(nameText, textX, y - 6);

                        // 绘制销售金额（第二行）
                        ctx.fillStyle = '#059669'; // 绿色
                        ctx.font = '11px Arial';
                        ctx.fillText(salesText, textX, y + 8);
                    }
                });

                ctx.restore();
            }
        }]
    });

    // 添加防重复点击的双击事件监听器
    let isPersonnelProcessingClick = false;
    
    cleanCanvas.addEventListener('dblclick', function(event) {
        // 防止重复点击
        if (isPersonnelProcessingClick) {
            console.log('🚫 人员气泡图正在处理中，忽略重复点击');
            return;
        }
        
        event.preventDefault();
        event.stopPropagation();
        isPersonnelProcessingClick = true;
        
        console.log('🖱️ 人员气泡图双击事件触发');
        
        try {
            const points = personnelBubbleChart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
            console.log('获取到的点击点:', points);
            
            if (points.length > 0) {
                const point = points[0];
                const dataIndex = point.index;
                const personnelData = personnelBubbleChart.data.datasets[0].data[dataIndex];

                console.log('✅ 双击人员气泡:', personnelData.label);
                showPersonnelProductBubbleModal(personnelData.label);
            } else {
                console.log('❌ 没有获取到有效的气泡点');
            }
        } catch (error) {
            console.error('处理人员气泡图双击事件时发生错误:', error);
        } finally {
            // 延迟重置标志，防止过快的重复点击
            setTimeout(() => {
                isPersonnelProcessingClick = false;
            }, 500);
        }
    });

    console.log('人员气泡图创建完成');
}

// 渲染产品气泡图
function renderProductBubbleChartInDashboard(products) {
    const canvas = document.getElementById('productBubbleChart');
    if (!canvas) {
        console.error('产品气泡图canvas未找到');
        return;
    }

    // 销毁现有图表和清理事件监听器
    if (productBubbleChart) {
        productBubbleChart.destroy();
        console.log('销毁了现有的产品气泡图实例');
    }

    // 清理旧的事件监听器
    const newCanvas = canvas.cloneNode(true);
    canvas.parentNode.replaceChild(newCanvas, canvas);
    const cleanCanvas = document.getElementById('productBubbleChart');
    console.log('清理了产品气泡图canvas的所有事件监听器');

    // 准备产品数据
    const productData = prepareProductBubbleDataInDashboard(products);

    console.log('产品气泡图数据:', productData);

    // 检查是否所有数据都是0，如果是则隐藏气泡图容器
    if (productData.length === 0 || productData.every(product => product.sales === 0)) {
        console.log('所有产品数据都是0，隐藏气泡图容器');
        hideBubbleChartsContainer();
        return;
    }

    // 如果有有效数据，确保气泡图容器是显示的
    const bubbleSection = document.getElementById('bubbleChartsSection');
    if (bubbleSection) {
        bubbleSection.style.display = 'block';
    }

    // 为每个气泡生成不同的颜色
    const colors = generateChartColors(productData.length);
    const backgroundColors = colors.map(color => {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, 0.6)`;
        } else if (color.startsWith('hsl')) {
            return color.replace('hsl', 'hsla').replace(')', ', 0.6)');
        }
        return color;
    });

    const borderColors = colors.map(color => {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, 1)`;
        } else if (color.startsWith('hsl')) {
            return color.replace('hsl', 'hsla').replace(')', ', 1)');
        }
        return color;
    });

    const ctx = cleanCanvas.getContext('2d');
    productBubbleChart = new Chart(ctx, {
        type: 'bubble',
        data: {
            datasets: [{
                label: '产品表现',
                data: productData,
                backgroundColor: backgroundColors,
                borderColor: borderColors,
                borderWidth: 2
            }]
        },
        options: getProductBubbleChartOptions('产品表现分析', productData),
        plugins: [{
            id: 'productLabels',
            afterDatasetsDraw: function(chart) {
                const ctx = chart.ctx;
                const meta = chart.getDatasetMeta(0);

                ctx.save();
                ctx.textAlign = 'left';
                ctx.textBaseline = 'middle';

                meta.data.forEach((point, index) => {
                    const data = productData[index];
                    if (data && data.label) {
                        // 获取图表区域边界
                        const chartArea = chart.chartArea;
                        
                        // 预计算标签宽度
                        ctx.font = 'bold 12px Arial';
                        const nameWidth = ctx.measureText(data.label).width;
                        ctx.font = '11px Arial';
                        const salesText = `¥${(data.sales / 10000).toFixed(1)}万`;
                        const salesWidth = ctx.measureText(salesText).width;
                        const maxWidth = Math.max(nameWidth, salesWidth);

                        // 计算标签位置，确保完全在图表区域内
                        let x, y = point.y;
                        let textAlign = 'left';

                        // 首先尝试右侧位置
                        const rightX = point.x + data.r + 8;
                        if (rightX + maxWidth + 8 <= chartArea.right) {
                            x = rightX;
                            textAlign = 'left';
                        } else {
                            // 右侧不够，尝试左侧
                            const leftX = point.x - data.r - 8;
                            if (leftX - maxWidth - 8 >= chartArea.left) {
                                x = leftX;
                                textAlign = 'right';
                            } else {
                                // 左右都不够，放在上方或下方
                                x = Math.max(chartArea.left + 8, Math.min(point.x - maxWidth/2, chartArea.right - maxWidth - 8));
                                textAlign = 'left';

                                // 尝试上方
                                if (point.y - data.r - 40 >= chartArea.top + 20) {
                                    y = point.y - data.r - 25;
                                } else {
                                    // 放在下方
                                    y = point.y + data.r + 25;
                                }
                            }
                        }

                        // 最终边界检查
                        y = Math.max(chartArea.top + 20, Math.min(y, chartArea.bottom - 20));

                        // 如果标签位置仍然会超出边界，则跳过绘制
                        const finalBgX = textAlign === 'right' ? x - maxWidth - 6 : x - 2;
                        if (finalBgX < chartArea.left || finalBgX + maxWidth + 8 > chartArea.right ||
                            y - 16 < chartArea.top || y + 16 > chartArea.bottom) {
                            return; // 跳过无法完全显示的标签
                        }

                        ctx.textAlign = textAlign;

                        // 准备显示的文本
                        const nameText = data.label;
                        const bgHeight = 32; // 两行文字的高度

                        // 根据文字对齐方式调整背景和文字位置
                        let bgX, textX;
                        if (textAlign === 'right') {
                            bgX = x - maxWidth - 6;
                            textX = x - 2;
                        } else {
                            bgX = x - 2;
                            textX = x + 2;
                        }

                        // 绘制白色背景
                        ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
                        ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
                        ctx.lineWidth = 1;
                        ctx.fillRect(bgX, y - bgHeight/2, maxWidth + 8, bgHeight);
                        ctx.strokeRect(bgX, y - bgHeight/2, maxWidth + 8, bgHeight);

                        // 绘制产品名称（第一行）
                        ctx.fillStyle = '#1f2937';
                        ctx.font = 'bold 12px Arial';
                        ctx.fillText(nameText, textX, y - 6);

                        // 绘制销售金额（第二行）
                        ctx.fillStyle = '#059669'; // 绿色
                        ctx.font = '11px Arial';
                        ctx.fillText(salesText, textX, y + 8);
                    }
                });

                ctx.restore();
            }
        }]
    });

    // 添加防重复点击的双击事件监听器
    let isProductProcessingClick = false;
    
    cleanCanvas.addEventListener('dblclick', function(event) {
        // 防止重复点击
        if (isProductProcessingClick) {
            console.log('🚫 产品气泡图正在处理中，忽略重复点击');
            return;
        }
        
        event.preventDefault();
        event.stopPropagation();
        isProductProcessingClick = true;
        
        console.log('🖱️ 产品气泡图双击事件触发');
        
        try {
            const points = productBubbleChart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
            console.log('获取到的点击点:', points);
            
            if (points.length > 0) {
                const point = points[0];
                const dataIndex = point.index;
                const productData = productBubbleChart.data.datasets[0].data[dataIndex];

                console.log('✅ 双击产品气泡:', productData.label);
                showProductHospitalBubbleModal(productData.label);
            } else {
                console.log('❌ 没有获取到有效的气泡点');
            }
        } catch (error) {
            console.error('处理产品气泡图双击事件时发生错误:', error);
        } finally {
            // 延迟重置标志，防止过快的重复点击
            setTimeout(() => {
                isProductProcessingClick = false;
            }, 500);
        }
    });

    console.log('产品气泡图创建完成');
}

// 准备人员气泡图数据
function preparePersonnelBubbleDataInDashboard(personnel) {
    if (!personnel || personnel.length === 0) {
        console.log('没有人员数据');
        return [];
    }

    // 过滤掉销售金额为0的人员
    const validPersonnel = personnel.filter(person => (person.actual || 0) > 0);
    console.log('过滤后的有效人员数据:', validPersonnel);

    return validPersonnel.map(person => {
        // 计算达成率，不进行限制
        let achievementRate = person.target > 0 ?
            (person.actual / person.target * 100) : 0;

        // 计算增长率，不进行限制
        let growthRate = person.lastYearSales > 0 ?
            ((person.actual - person.lastYearSales) / person.lastYearSales * 100) : 0;

        // 优化气泡大小计算，确保合理的大小范围
        let bubbleSize = Math.abs(growthRate) / 10 + 8; // 基础大小8，根据增长率调整
        bubbleSize = Math.max(6, Math.min(25, bubbleSize)); // 限制在6-25之间

        // 改进气泡大小计算：使用更大的缩放差异
        const salesAmount = person.actual || 0;
        let bubbleRadius;

        if (salesAmount === 0) {
            bubbleRadius = 8; // 最小气泡
        } else if (salesAmount < 100000) {
            // 10万以下：小气泡 8-15
            bubbleRadius = 8 + (salesAmount / 100000) * 7;
        } else if (salesAmount < 300000) {
            // 10-30万：中等气泡 15-25
            bubbleRadius = 15 + ((salesAmount - 100000) / 200000) * 10;
        } else if (salesAmount < 500000) {
            // 30-50万：大气泡 25-35
            bubbleRadius = 25 + ((salesAmount - 300000) / 200000) * 10;
        } else {
            // 50万以上：超大气泡 35-60
            bubbleRadius = 35 + Math.min(25, ((salesAmount - 500000) / 500000) * 25);
        }

        // 确保在合理范围内
        bubbleRadius = Math.max(8, Math.min(60, bubbleRadius));

        return {
            x: Math.max(0, achievementRate), // X轴：达成率(QP)，确保非负
            y: growthRate, // Y轴：增长率(GR)
            r: bubbleRadius, // 气泡大小：基于销售金额的平方根缩放
            label: person.name,
            sales: person.actual || 0,
            achievement: achievementRate,
            growth: growthRate,
            productCount: person.productCount || 0
        };
    });
}

// 准备产品气泡图数据
function prepareProductBubbleDataInDashboard(products) {
    console.log('=== 准备产品气泡图数据 ===');
    console.log('产品数据:', products);

    // 过滤掉销售金额为0的产品
    const validProducts = products.filter(product => (product.actual || 0) > 0);
    console.log('过滤后的有效产品数据:', validProducts);

    return validProducts.map(product => {
        // 达成率计算：使用实际销售额与目标的比率，不进行限制
        let achievementRate = product.target > 0 ?
            (product.actual / product.target * 100) : 0;

        // 修复增长率计算：使用API返回的比较数据，不进行限制
        let growthRate = 0;
        if (product.samePeriodGrowth) {
            const growthText = product.samePeriodGrowth.toString();
            if (growthText.includes('%')) {
                const match = growthText.match(/([+-]?\d+\.?\d*)/);
                growthRate = match ? parseFloat(match[1]) : 0;
            } else if (growthText === '-') {
                growthRate = 0;
            }
        }

        // 产品气泡大小计算 - 优化算法，增强视觉区分度
        const salesAmount = product.actual || 0;
        let bubbleRadius;

        if (salesAmount === 0) {
            bubbleRadius = 8; // 最小气泡
        } else if (salesAmount < 50000) {
            // 5万以下：8-15像素，提升小金额的可见度
            bubbleRadius = 8 + (salesAmount / 50000) * 7;
        } else if (salesAmount < 200000) {
            // 5-20万：15-25像素，中等金额
            bubbleRadius = 15 + ((salesAmount - 50000) / 150000) * 10;
        } else if (salesAmount < 500000) {
            // 20-50万：25-35像素，较大金额
            bubbleRadius = 25 + ((salesAmount - 200000) / 300000) * 10;
        } else if (salesAmount < 1000000) {
            // 50-100万：35-45像素，大金额
            bubbleRadius = 35 + ((salesAmount - 500000) / 500000) * 10;
        } else {
            // 100万以上：45-60像素，超大金额
            bubbleRadius = 45 + Math.min(15, ((salesAmount - 1000000) / 1000000) * 15);
        }

        // 确保在合理范围内：最小8像素，最大60像素（避免超过容器）
        bubbleRadius = Math.max(8, Math.min(60, bubbleRadius));

        const bubbleData = {
            x: Math.max(0, achievementRate), // X轴：达成率(QP)，确保非负
            y: growthRate, // Y轴：增长率(GR)，使用修复后的计算
            r: bubbleRadius, // 气泡大小：基于销售金额的平方根缩放
            label: product.name,
            sales: product.actual || 0,
            achievement: achievementRate,
            growth: growthRate
        };

        console.log(`产品 ${product.name}:`, {
            actual: product.actual,
            target: product.target,
            samePeriodGrowth: product.samePeriodGrowth,
            lastYearSales: product.lastYearSales,
            计算结果: bubbleData
        });

        return bubbleData;
    });
}

// 获取人员气泡图配置选项（带标签显示）
function getPersonnelBubbleChartOptions(title, bubbleData = null) {
    // 动态计算坐标轴范围
    let xMin = 0, xMax = 120, yMin = -50, yMax = 50;

    if (bubbleData && bubbleData.length > 0) {
        // 计算气泡的最大半径（用于确保整个气泡都在可见范围内）
        const maxBubbleRadius = Math.max(...bubbleData.map(d => d.r || 0));
        console.log('主界面人员气泡图调试信息:', {
            title,
            bubbleCount: bubbleData.length,
            maxBubbleRadius,
            bubbleData: bubbleData.map(d => ({name: d.label, x: d.x, y: d.y, r: d.r}))
        });

        // 计算X轴范围（达成率）
        const xValues = bubbleData.map(d => d.x).filter(x => x !== undefined && x !== null);
        if (xValues.length > 0) {
            const dataXMin = Math.min(...xValues);
            const dataXMax = Math.max(...xValues);
            const xRange = dataXMax - dataXMin;

            // 基础边距：数据范围的10%，最少10个单位
            const basePadding = Math.max(xRange * 0.1, 10);

            // 气泡半径对应的坐标轴单位（估算）
            // 增加转换比例确保气泡完全可见
            const bubbleRadiusInAxisUnits = maxBubbleRadius * (xRange > 0 ? xRange / 150 : 2);

            // 总边距 = 基础边距 + 气泡半径边距 + 额外安全边距
            const xPadding = basePadding + bubbleRadiusInAxisUnits + Math.max(maxBubbleRadius * 0.5, 20);

            xMin = Math.max(0, dataXMin - xPadding);
            xMax = dataXMax + xPadding;

            console.log(`${title} X轴范围计算:`, {
                dataXMin, dataXMax, xRange, basePadding,
                bubbleRadiusInAxisUnits, xPadding, xMin, xMax,
                maxBubbleRadius
            });

            // 确保最小范围
            if (xMax - xMin < 50) {
                const center = (xMax + xMin) / 2;
                xMin = Math.max(0, center - 25);
                xMax = center + 25;
            }
        }

        // 计算Y轴范围（增长率）
        const yValues = bubbleData.map(d => d.y).filter(y => y !== undefined && y !== null);
        if (yValues.length > 0) {
            const dataYMin = Math.min(...yValues);
            const dataYMax = Math.max(...yValues);
            const yRange = dataYMax - dataYMin;

            // 基础边距：数据范围的10%，最少10个单位
            const basePadding = Math.max(yRange * 0.1, 10);

            // 气泡半径对应的坐标轴单位（估算）
            // 增加转换比例确保气泡完全可见
            const bubbleRadiusInAxisUnits = maxBubbleRadius * (yRange > 0 ? yRange / 120 : 2);

            // 总边距 = 基础边距 + 气泡半径边距 + 额外安全边距
            const yPadding = basePadding + bubbleRadiusInAxisUnits + Math.max(maxBubbleRadius * 0.5, 20);

            yMin = dataYMin - yPadding;
            yMax = dataYMax + yPadding;

            console.log(`${title} Y轴范围计算:`, {
                dataYMin, dataYMax, yRange, basePadding,
                bubbleRadiusInAxisUnits, yPadding, yMin, yMax,
                maxBubbleRadius
            });

            // 确保最小范围
            if (yMax - yMin < 50) {
                const center = (yMax + yMin) / 2;
                yMin = center - 25;
                yMax = center + 25;
            }
        }
    }
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: title,
                font: {
                    size: 16,
                    weight: 'bold'
                },
                color: '#1f2937',
                padding: {
                    bottom: 20
                }
            },
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#ddd',
                borderWidth: 1,
                cornerRadius: 6,
                displayColors: false,
                callbacks: {
                    title: function(context) {
                        return context[0].raw.label;
                    },
                    label: function(context) {
                        const data = context.raw;
                        const labels = [
                            `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                            `达成率: ${data.achievement.toFixed(1)}%`,
                            `同比增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%`
                        ];

                        // 如果是人员数据，添加产品数量信息
                        if (data.productCount !== undefined) {
                            labels.push(`负责产品: ${data.productCount}个`);
                        }

                        return labels;
                    }
                }
            }
        },
        scales: {
            x: {
                type: 'linear',
                position: 'bottom',
                title: {
                    display: true,
                    text: '达成率 (%)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    display: true, // 显示X轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        return value.toFixed(0) + '%';
                    }
                },
                // 根据数据动态设置X轴范围
                min: xMin,
                max: xMax
            },
            y: {
                title: {
                    display: true,
                    text: '同比增长率 (%)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    display: true, // 显示Y轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        return (value >= 0 ? '+' : '') + value.toFixed(0) + '%';
                    }
                },
                // 根据数据动态设置Y轴范围
                min: yMin,
                max: yMax
            }
        },
        layout: {
            padding: {
                top: 80,  // 增加顶部边距确保标签完全可见
                right: 160, // 为右侧标签留出更多空间（人名+销售金额）
                bottom: 120, // 增加底部空间防止文字被遮挡和气泡被截断
                left: 80   // 增加左侧边距确保标签完全可见
            }
        },
        // 禁用裁剪确保气泡完全可见
        clip: false,
        // 确保气泡元素不会被裁剪
        elements: {
            point: {
                clip: false  // 禁用点的裁剪
            }
        }
    };
}

// 获取产品气泡图配置选项（带标签显示）
function getProductBubbleChartOptions(title, bubbleData = null) {
    // 动态计算坐标轴范围
    let xMin = 0, xMax = 120, yMin = -50, yMax = 50;

    if (bubbleData && bubbleData.length > 0) {
        // 计算气泡的最大半径（用于确保整个气泡都在可见范围内）
        const maxBubbleRadius = Math.max(...bubbleData.map(d => d.r || 0));
        console.log('主界面产品气泡图调试信息:', {
            title,
            bubbleCount: bubbleData.length,
            maxBubbleRadius,
            bubbleData: bubbleData.map(d => ({name: d.label, x: d.x, y: d.y, r: d.r}))
        });

        // 计算X轴范围（达成率）
        const xValues = bubbleData.map(d => d.x).filter(x => x !== undefined && x !== null);
        if (xValues.length > 0) {
            const dataXMin = Math.min(...xValues);
            const dataXMax = Math.max(...xValues);
            const xRange = dataXMax - dataXMin;

            // 基础边距：数据范围的10%，最少10个单位
            const basePadding = Math.max(xRange * 0.1, 10);

            // 气泡半径对应的坐标轴单位（估算）
            // 增加转换比例确保气泡完全可见，假设图表宽度约400px
            const bubbleRadiusInAxisUnits = maxBubbleRadius * (xRange > 0 ? xRange / 150 : 2);

            // 总边距 = 基础边距 + 气泡半径边距 + 额外安全边距
            const xPadding = basePadding + bubbleRadiusInAxisUnits + Math.max(maxBubbleRadius * 0.5, 20);

            xMin = Math.max(0, dataXMin - xPadding);
            xMax = dataXMax + xPadding;

            console.log(`${title} X轴范围计算:`, {
                dataXMin, dataXMax, xRange, basePadding,
                bubbleRadiusInAxisUnits, xPadding, xMin, xMax,
                maxBubbleRadius
            });

            // 确保最小范围
            if (xMax - xMin < 50) {
                const center = (xMax + xMin) / 2;
                xMin = Math.max(0, center - 25);
                xMax = center + 25;
            }
        }

        // 计算Y轴范围（增长率）
        const yValues = bubbleData.map(d => d.y).filter(y => y !== undefined && y !== null);
        if (yValues.length > 0) {
            const dataYMin = Math.min(...yValues);
            const dataYMax = Math.max(...yValues);
            const yRange = dataYMax - dataYMin;

            // 基础边距：数据范围的10%，最少10个单位
            const basePadding = Math.max(yRange * 0.1, 10);

            // 气泡半径对应的坐标轴单位（估算）
            // 增加转换比例确保气泡完全可见，假设图表高度约300px
            const bubbleRadiusInAxisUnits = maxBubbleRadius * (yRange > 0 ? yRange / 120 : 2);

            // 总边距 = 基础边距 + 气泡半径边距 + 额外安全边距
            const yPadding = basePadding + bubbleRadiusInAxisUnits + Math.max(maxBubbleRadius * 0.5, 20);

            yMin = dataYMin - yPadding;
            yMax = dataYMax + yPadding;

            // 确保最小范围
            if (yMax - yMin < 50) {
                const center = (yMax + yMin) / 2;
                yMin = center - 25;
                yMax = center + 25;
            }
        }

        console.log(`主界面产品气泡半径考虑 - 最大气泡半径: ${maxBubbleRadius}px`);
    }

    console.log(`主界面产品动态计算坐标轴范围 - X轴: [${xMin.toFixed(1)}, ${xMax.toFixed(1)}], Y轴: [${yMin.toFixed(1)}, ${yMax.toFixed(1)}]`);

    return {
        responsive: true,
        maintainAspectRatio: false,
        // 确保图表能够正确渲染气泡
        devicePixelRatio: window.devicePixelRatio || 1,
        plugins: {
            title: {
                display: true,
                text: title,
                font: {
                    size: 16,
                    weight: 'bold'
                },
                color: '#1f2937'
            },
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    title: function(context) {
                        return context[0].raw.label;
                    },
                    label: function(context) {
                        const data = context.raw;
                        const labels = [
                            `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                            `达成率: ${data.achievement.toFixed(1)}%`,
                            `同比增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%`
                        ];

                        return labels;
                    }
                }
            }
        },
        scales: {
            x: {
                type: 'linear',
                position: 'bottom',
                title: {
                    display: true,
                    text: '达成率 (%)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    display: true, // 显示X轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        return value.toFixed(0) + '%';
                    }
                },
                // 根据数据动态设置X轴范围
                min: xMin,
                max: xMax
            },
            y: {
                title: {
                    display: true,
                    text: '同比增长率 (%)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    display: true, // 显示Y轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        return (value >= 0 ? '+' : '') + value.toFixed(0) + '%';
                    }
                },
                // 根据数据动态设置Y轴范围
                min: yMin,
                max: yMax
            }
        },
        interaction: {
            intersect: false,
            mode: 'point'
        },
        layout: {
            padding: {
                top: 80,  // 增加顶部边距确保标签完全可见
                right: 160, // 为右侧标签留出更多空间（产品名+销售金额）
                bottom: 120, // 增加底部空间防止文字被遮挡和气泡被截断
                left: 80   // 增加左侧边距确保标签完全可见
            }
        },
        // 禁用裁剪确保气泡完全可见
        clip: false,
        // 图表区域设置
        chartArea: {
            backgroundColor: 'rgba(251, 251, 251, 0.05)'
        },
        // 确保气泡元素不会被裁剪
        elements: {
            point: {
                clip: false  // 禁用点的裁剪
            }
        }
    };
}

// 获取气泡图配置选项
function getBubbleChartOptionsInDashboard(title) {
    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: title,
                font: {
                    size: 16,
                    weight: 'bold'
                },
                color: '#1f2937',
                padding: {
                    bottom: 20
                }
            },
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#ddd',
                borderWidth: 1,
                cornerRadius: 6,
                displayColors: false,
                callbacks: {
                    title: function(context) {
                        return context[0].raw.label;
                    },
                    label: function(context) {
                        const data = context.raw;
                        const labels = [
                            `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`,
                            `达成率: ${data.achievement.toFixed(1)}%`,
                            `增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%`
                        ];

                        // 如果是人员数据，添加产品数量信息
                        if (data.productCount !== undefined) {
                            labels.push(`负责产品: ${data.productCount}个`);
                        }

                        return labels;
                    }
                }
            }
        },
        scales: {
            x: {
                type: 'linear',
                position: 'bottom',
                title: {
                    display: true,
                    text: '达成率 (%)',
                    font: {
                        size: 12,
                        weight: 'bold'
                    },
                    color: '#374151'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)',
                    lineWidth: 1
                },
                ticks: {
                    display: false, // 隐藏X轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        // 限制显示范围，避免极端值
                        if (Math.abs(value) > 1000) {
                            return ''; // 不显示极端值
                        }
                        return value.toFixed(0) + '%';
                    }
                },
                min: 0
            },
            y: {
                title: {
                    display: true,
                    text: '增长率 (%)',
                    font: {
                        size: 12,
                        weight: 'bold'
                    },
                    color: '#374151'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)',
                    lineWidth: 1
                },
                ticks: {
                    display: false, // 隐藏Y轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        // 限制显示范围，避免极端值
                        if (Math.abs(value) > 1000) {
                            return ''; // 不显示极端值
                        }
                        return (value >= 0 ? '+' : '') + value.toFixed(0) + '%';
                    }
                }
            }
        },
        interaction: {
            intersect: false,
            mode: 'point'
        },
        layout: {
            padding: {
                top: 40,
                right: 40,
                bottom: 80, // 增加底部空间防止文字被遮挡
                left: 40
            }
        }
    };
}

// 渲染3D气泡图
function render3DBubbleCharts(personnel, products) {
    console.log('=== 开始渲染3D气泡图 ===');
    console.log('人员数据:', personnel);
    console.log('产品数据:', products);

    // 渲染人员3D气泡图
    if (window.personnel3DRenderer && personnel.length > 0) {
        const personnelBubbleData = prepare3DPersonnelBubbleData(personnel);
        window.personnel3DRenderer.renderBubbles(personnelBubbleData, '人员表现3D分析');
    }

    // 渲染产品3D气泡图
    if (window.product3DRenderer && products.length > 0) {
        const productBubbleData = prepare3DProductBubbleData(products);
        window.product3DRenderer.renderBubbles(productBubbleData, '产品表现3D分析');
    }
}

// 准备人员3D气泡图数据
function prepare3DPersonnelBubbleData(personnel) {
    if (!personnel || personnel.length === 0) {
        console.log('没有人员数据');
        return [];
    }

    return personnel.map(person => {
        // 计算达成率，不进行限制
        let achievementRate = person.target > 0 ?
            (person.actual / person.target * 100) : 0;

        // 计算增长率，不进行限制
        let growthRate = person.lastYearSales > 0 ?
            ((person.actual - person.lastYearSales) / person.lastYearSales * 100) : 0;

        return {
            label: person.name,
            sales: person.actual || 0,
            achievement: achievementRate,
            growth: growthRate,
            productCount: person.productCount || 0,
            type: 'personnel'
        };
    });
}

// 准备产品3D气泡图数据
function prepare3DProductBubbleData(products) {
    console.log('=== 准备产品3D气泡图数据 ===');

    return products.map(product => {
        // 计算达成率，不进行限制
        let achievementRate = product.target > 0 ?
            (product.actual / product.target * 100) : 0;

        // 修复增长率计算：使用API返回的比较数据，不进行限制
        let growthRate = 0;
        if (product.samePeriodGrowth) {
            const growthText = product.samePeriodGrowth.toString();
            if (growthText.includes('%')) {
                const match = growthText.match(/([+-]?\d+\.?\d*)/);
                growthRate = match ? parseFloat(match[1]) : 0;
            } else if (growthText === '-') {
                growthRate = 0;
            }
        }
        // 限制增长率在-200%到+500%之间，避免极端值
        growthRate = Math.max(-200, Math.min(500, growthRate));

        console.log(`3D产品 ${product.name}:`, {
            actual: product.actual,
            target: product.target,
            samePeriodGrowth: product.samePeriodGrowth,
            achievement: achievementRate,
            growth: growthRate
        });

        return {
            label: product.name,
            sales: product.actual || 0,
            achievement: achievementRate,
            growth: growthRate,
            type: 'product'
        };
    });
}

// 初始化图表模式切换
function initChartModeToggle() {
    const toggle2D = document.getElementById('toggle2D');
    const toggle3D = document.getElementById('toggle3D');

    if (!toggle2D || !toggle3D) {
        console.log('切换按钮未找到');
        return;
    }

    toggle2D.addEventListener('click', () => {
        toggle2D.classList.add('active');
        toggle3D.classList.remove('active');
        show2DCharts();
    });

    toggle3D.addEventListener('click', () => {
        toggle3D.classList.add('active');
        toggle2D.classList.remove('active');
        show3DCharts();
    });
}

// 显示2D图表
function show2DCharts() {
    // 显示2D canvas
    const personnelCanvas = document.getElementById('personnelBubbleChart');
    const productCanvas = document.getElementById('productBubbleChart');
    const personnel3DContainer = document.getElementById('personnel3DBubbleChart');
    const product3DContainer = document.getElementById('product3DBubbleChart');

    if (personnelCanvas) personnelCanvas.style.display = 'block';
    if (productCanvas) productCanvas.style.display = 'block';
    if (personnel3DContainer) personnel3DContainer.style.display = 'none';
    if (product3DContainer) product3DContainer.style.display = 'none';
}

// 显示3D图表
function show3DCharts() {
    // 隐藏2D canvas
    const personnelCanvas = document.getElementById('personnelBubbleChart');
    const productCanvas = document.getElementById('productBubbleChart');
    const personnel3DContainer = document.getElementById('personnel3DBubbleChart');
    const product3DContainer = document.getElementById('product3DBubbleChart');

    if (personnelCanvas) personnelCanvas.style.display = 'none';
    if (productCanvas) productCanvas.style.display = 'none';
    if (personnel3DContainer) personnel3DContainer.style.display = 'block';
    if (product3DContainer) product3DContainer.style.display = 'block';

    // 初始化3D渲染器（如果还没有初始化）
    if (!window.personnel3DRenderer) {
        window.personnel3DRenderer = new Bubble3DRenderer('personnel3DBubbleChart');
    }
    if (!window.product3DRenderer) {
        window.product3DRenderer = new Bubble3DRenderer('product3DBubbleChart');
    }

    // 渲染3D图表
    if (window.currentPersonnelData && window.currentProductData) {
        render3DBubbleCharts(window.currentPersonnelData, window.currentProductData);
    }
}

// 根据增长率文本确定颜色的全局函数
function getGrowthColor(growthText) {
    if (!growthText || growthText === '-' || growthText === '0.0%') {
        return '#666'; // 灰色
    }

    if (growthText.startsWith('+')) {
        return '#22c55e'; // 绿色 - 正向
    }

    if (growthText.startsWith('-')) {
        return '#ef4444'; // 红色 - 负向
    }

    // 解析数值来判断正负
    const match = growthText.match(/([+-]?\d+\.?\d*)/);
    if (match) {
        const value = parseFloat(match[1]);
        if (value > 0) {
            return '#22c55e'; // 绿色 - 正向
        } else if (value < 0) {
            return '#ef4444'; // 红色 - 负向
        }
    }

    return '#666'; // 默认灰色
}

// 清空冗余数据功能
async function cleanRedundantData() {
    const cleanBtn = document.getElementById('cleanRedundantDataDashboard');
    const originalText = cleanBtn.innerHTML;

    // 确认对话框
    if (!confirm('确定要清空冗余数据吗？\n\n清理条件：销量盒折算后、销售金额折算后、指标盒折算后、指标金额都为0的数据\n\n此操作不可撤销！')) {
        return;
    }

    try {
        // 显示加载状态
        cleanBtn.disabled = true;
        cleanBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清理中...';

        const token = localStorage.getItem('authToken');
        const response = await fetch('/api/clean-redundant-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        const result = await response.json();

        if (response.ok && result.success) {
            // 成功
            cleanBtn.innerHTML = '<i class="fas fa-check"></i> 清理完成';
            cleanBtn.style.background = '#10b981';

            alert(`清理完成！\n删除了 ${result.deletedCount} 条冗余数据`);

            // 3秒后恢复按钮状态
            setTimeout(() => {
                cleanBtn.disabled = false;
                cleanBtn.innerHTML = originalText;
                cleanBtn.style.background = '';
            }, 3000);
        } else {
            throw new Error(result.message || '清理失败');
        }
    } catch (error) {
        console.error('清理冗余数据失败:', error);

        // 显示错误状态
        cleanBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 清理失败';
        cleanBtn.style.background = '#ef4444';

        alert('清理失败: ' + error.message);

        // 3秒后恢复按钮状态
        setTimeout(() => {
            cleanBtn.disabled = false;
            cleanBtn.innerHTML = originalText;
            cleanBtn.style.background = '';
        }, 3000);
    }
}

// 折叠功能
function toggleSection(contentId, buttonId) {
    const content = document.getElementById(contentId);
    const button = document.getElementById(buttonId);
    const toggleText = button ? button.querySelector('.toggle-text') : null;

    if (!content || !button || !toggleText) {
        console.error('折叠元素未找到:', { contentId, buttonId });
        return;
    }

    const isCollapsed = content.classList.contains('collapsed');

    // 找到对应的标题元素
    const sectionHeader = button.closest('.section-header');
    const titleElement = sectionHeader ? sectionHeader.querySelector('h3, h4') : null;

    if (isCollapsed) {
        // 展开
        content.classList.remove('collapsed');
        button.classList.remove('collapsed');

        // 显示标题文字
        if (titleElement) {
            titleElement.style.opacity = '1';
        }

        // 更新按钮文字
        toggleText.textContent = '折叠';

        // 添加展开动画
        content.style.maxHeight = content.scrollHeight + 'px';

        // 动画完成后移除maxHeight限制
        setTimeout(() => {
            if (!content.classList.contains('collapsed')) {
                content.style.maxHeight = 'none';
            }
        }, 300);

        console.log('展开区域:', contentId);
    } else {
        // 折叠
        // 先设置确切的高度，然后折叠
        content.style.maxHeight = content.scrollHeight + 'px';

        // 保持标题文字可见（不再隐藏）
        if (titleElement) {
            titleElement.style.opacity = '1';
        }

        // 强制重绘
        requestAnimationFrame(() => {
            content.classList.add('collapsed');
            button.classList.add('collapsed');

            // 更新按钮文字
            toggleText.textContent = '展开';
        });

        console.log('折叠区域:', contentId);
    }
}

// 初始化所有折叠区域
function initCollapsibleSections() {
    console.log('初始化折叠区域');

    // 获取所有折叠区域
    const collapsibleSections = document.querySelectorAll('.collapsible-content');
    const toggleButtons = document.querySelectorAll('.toggle-btn');

    // 默认展开所有区域
    collapsibleSections.forEach(section => {
        // 确保初始状态为展开
        section.classList.remove('collapsed');
        section.style.maxHeight = 'none';
    });

    toggleButtons.forEach(button => {
        // 确保按钮状态正确
        button.classList.remove('collapsed');
        const toggleText = button.querySelector('.toggle-text');
        if (toggleText) {
            toggleText.textContent = '折叠';
        }
    });

    console.log(`已初始化 ${collapsibleSections.length} 个折叠区域`);
}

// ==================== 全局折叠功能函数 ====================

// 切换折叠区域显示/隐藏
window.toggleCollapsible = function(contentId) {
    const content = document.getElementById(contentId);
    const header = content.previousElementSibling;
    const button = header.querySelector('.toggle-btn');
    const toggleText = button.querySelector('.toggle-text');

    if (content.style.display === 'none') {
        // 展开
        content.style.display = 'block';
        button.classList.remove('collapsed');
        if (toggleText) {
            toggleText.textContent = '折叠';
        }
    } else {
        // 折叠
        content.style.display = 'none';
        button.classList.add('collapsed');
        if (toggleText) {
            toggleText.textContent = '展开';
        }
    }
};

// ==================== 医院销售对比表格功能 ====================

// 全局变量
let hospitalComparisonData = null;

// 初始化医院销售对比表格
function initHospitalComparisonTable() {
    console.log('初始化医院销售对比表格');

    // 绑定加载数据按钮事件
    const loadButton = document.getElementById('loadHospitalComparisonData');
    if (loadButton) {
        loadButton.addEventListener('click', loadHospitalComparisonData);
        console.log('医院销售对比数据加载按钮事件已绑定');
    } else {
        console.warn('找不到医院销售对比数据加载按钮');
    }

    // 表格默认显示，不需要额外的显示逻辑
    console.log('医院销售对比表格默认显示');
}

// 加载医院销售对比数据
async function loadHospitalComparisonData() {
    console.log('开始加载医院销售对比数据');

    const loadButton = document.getElementById('loadHospitalComparisonData');
    const originalText = loadButton.innerHTML;

    try {
        // 显示加载状态
        loadButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
        loadButton.disabled = true;

        // 获取筛选条件
        const filters = getSelectedFilters();

        console.log('医院销售对比请求数据:', filters);

        // 调用API
        const response = await fetch('/api/hospital-sales-comparison', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify(filters)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            hospitalComparisonData = result.data;
            console.log('医院销售对比数据加载成功:', hospitalComparisonData);

            // 渲染表格数据
            renderHospitalComparisonTable();

            // 更新汇总信息
            updateHospitalComparisonSummary();

            // 更新表格标题显示当期和同期年份
            updateTableHeaders();

        } else {
            throw new Error(result.error || '数据加载失败');
        }

    } catch (error) {
        console.error('加载医院销售对比数据失败:', error);
        alert('加载数据失败: ' + error.message);
    } finally {
        // 恢复按钮状态
        loadButton.innerHTML = originalText;
        loadButton.disabled = false;
    }
}



// 渲染医院销售对比表格
function renderHospitalComparisonTable() {
    if (!hospitalComparisonData) {
        console.warn('没有医院销售对比数据可渲染');
        return;
    }

    console.log('开始渲染医院销售对比表格');

    // 渲染分组表格
    renderHospitalGroupTables(hospitalComparisonData.hospitals80 || [], hospitalComparisonData.hospitals20 || []);

    // 更新分组统计信息
    updateGroupHeaders();

    // 显示医院分组内容
    showHospitalGroupContents();
}

// 生成三层表头结构
function generateHospitalTableHeaders(products, targetTableHead = null) {
    const tableHead = targetTableHead || document.getElementById('hospitalTableHead');
    if (!tableHead) {
        console.error('找不到医院对比表格thead元素');
        return;
    }

    // 清空现有表头
    tableHead.innerHTML = '';

    if (!products || products.length === 0) {
        return;
    }

    // 第一层：产品名称
    const row1 = document.createElement('tr');
    const hospitalHeader = document.createElement('th');
    hospitalHeader.textContent = '医院名称';
    hospitalHeader.rowSpan = 3;
    hospitalHeader.className = 'hospital-name-header';
    row1.appendChild(hospitalHeader);

    // 添加医院汇总列
    const summaryHeader = document.createElement('th');
    summaryHeader.textContent = '根据医院列汇总';
    summaryHeader.colSpan = 6; // 同期(2) + 本期(2) + GRV(1) + GR(1)
    summaryHeader.className = 'product-header';
    row1.appendChild(summaryHeader);

    products.forEach(product => {
        const productHeader = document.createElement('th');
        productHeader.textContent = product;
        productHeader.colSpan = 6; // 同期(2) + 本期(2) + GRV(1) + GR(1)
        productHeader.className = 'product-header';
        row1.appendChild(productHeader);
    });

    // 第二层：同期/本期/GRV/GR
    const row2 = document.createElement('tr');

    // 添加医院汇总的第二层表头
    ['同期', '本期', 'GRV', 'GR'].forEach(period => {
        const periodHeader = document.createElement('th');
        periodHeader.textContent = period;
        if (period === 'GRV' || period === 'GR') {
            periodHeader.colSpan = 1; // GRV和GR只占1列
        } else {
            periodHeader.colSpan = 2; // 同期和本期占2列（销售额 + 销量）
        }
        periodHeader.className = 'period-header';
        row2.appendChild(periodHeader);
    });

    products.forEach(product => {
        ['同期', '本期', 'GRV', 'GR'].forEach(period => {
            const periodHeader = document.createElement('th');
            periodHeader.textContent = period;
            if (period === 'GRV' || period === 'GR') {
                periodHeader.colSpan = 1; // GRV和GR只占1列
            } else {
                periodHeader.colSpan = 2; // 同期和本期占2列（销售额 + 销量）
            }
            periodHeader.className = 'period-header';
            row2.appendChild(periodHeader);
        });
    });

    // 第三层：销售额/销量
    const row3 = document.createElement('tr');

    // 添加医院汇总的第三层表头
    ['同期', '本期', 'GRV', 'GR'].forEach(period => {
        if (period === 'GRV') {
            // GRV只显示差额
            const valueHeader = document.createElement('th');
            valueHeader.textContent = '差额';
            valueHeader.className = 'metric-header';
            row3.appendChild(valueHeader);
        } else if (period === 'GR') {
            // GR只显示百分比
            const percentHeader = document.createElement('th');
            percentHeader.textContent = '百分比';
            percentHeader.className = 'metric-header';
            row3.appendChild(percentHeader);
        } else {
            // 同期和本期显示销售额和销量
            const amountHeader = document.createElement('th');
            amountHeader.textContent = '销售额';
            amountHeader.className = 'amount-header';
            row3.appendChild(amountHeader);

            const quantityHeader = document.createElement('th');
            quantityHeader.textContent = '销量';
            quantityHeader.className = 'quantity-header';
            row3.appendChild(quantityHeader);
        }
    });

    products.forEach(product => {
        // 为每个产品的每个时期添加销售额和销量列
        ['同期', '本期', 'GRV', 'GR'].forEach(period => {
            if (period === 'GRV') {
                // GRV只显示差额
                const valueHeader = document.createElement('th');
                valueHeader.textContent = '差额';
                valueHeader.className = 'metric-header';
                row3.appendChild(valueHeader);
            } else if (period === 'GR') {
                // GR只显示百分比
                const percentHeader = document.createElement('th');
                percentHeader.textContent = '百分比';
                percentHeader.className = 'metric-header';
                row3.appendChild(percentHeader);
            } else {
                // 同期和本期显示销售额和销量
                const amountHeader = document.createElement('th');
                amountHeader.textContent = '销售额';
                amountHeader.className = 'amount-header';
                row3.appendChild(amountHeader);

                const quantityHeader = document.createElement('th');
                quantityHeader.textContent = '销量';
                quantityHeader.className = 'quantity-header';
                row3.appendChild(quantityHeader);
            }
        });
    });

    tableHead.appendChild(row1);
    tableHead.appendChild(row2);
    tableHead.appendChild(row3);
}

// 渲染医院分组表格
function renderHospitalGroupTables(hospitals80, hospitals20) {
    console.log(`开始渲染分组表格: 80%医院${hospitals80.length}家, 20%医院${hospitals20.length}家`);

    // 获取所有产品列表
    const allHospitals = [...hospitals80, ...hospitals20];
    const allProducts = new Set();
    allHospitals.forEach(hospital => {
        const products = hospital.products || {};
        Object.keys(products).forEach(productName => allProducts.add(productName));
    });
    const products = Array.from(allProducts).sort();

    // 渲染80%医院表格
    renderGroupTable('hospitals80', hospitals80, products);

    // 渲染20%医院表格
    renderGroupTable('hospitals20', hospitals20, products);
}

// 渲染单个分组表格
function renderGroupTable(groupType, hospitals, products) {
    const tableHead = document.getElementById(`${groupType}TableHead`);
    const tableBody = document.getElementById(`${groupType}TableBody`);

    if (!tableHead || !tableBody) {
        console.error(`找不到${groupType}表格元素`);
        return;
    }

    // 清空现有内容
    tableHead.innerHTML = '';
    tableBody.innerHTML = '';

    if (!hospitals || hospitals.length === 0) {
        tableBody.innerHTML = '<tr><td style="text-align: center; color: #666; padding: 40px;">暂无数据</td></tr>';
        return;
    }

    // 生成表头
    generateHospitalTableHeaders(products, tableHead);

    // 按医院名称排序
    hospitals.sort((a, b) => a.hospitalName.localeCompare(b.hospitalName));

    // 按医院分组数据
    const hospitalGroups = {};
    hospitals.forEach(hospital => {
        if (!hospitalGroups[hospital.hospitalName]) {
            hospitalGroups[hospital.hospitalName] = {};
        }
        const hospitalProducts = hospital.products || {};
        Object.keys(hospitalProducts).forEach(productName => {
            hospitalGroups[hospital.hospitalName][productName] = hospitalProducts[productName];
        });
    });

    // 计算汇总数据
    const summaryData = calculateHospitalSummary(hospitalGroups, products);
    
    // 添加汇总行
    const summaryRow = createHospitalSummaryRow(summaryData, products);
    tableBody.appendChild(summaryRow);
    
    // 生成表格行
    Object.keys(hospitalGroups).sort().forEach(hospitalName => {
        const row = createHospitalRowWithProducts(hospitalName, hospitalGroups[hospitalName], products);
        tableBody.appendChild(row);
    });

    console.log(`${groupType}表格渲染完成，共 ${Object.keys(hospitalGroups).length} 家医院，${products.length} 个产品`);
}

// 渲染医院分组表格
function renderHospitalGroupTable(groupType, hospitalData) {
    const tableBody = document.getElementById(`${groupType}TableBody`);

    if (!tableBody) {
        console.error(`表格元素未找到: ${groupType}TableBody`);
        return;
    }

    // 清空现有数据
    tableBody.innerHTML = '';

    if (!hospitalData || hospitalData.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #6b7280;">暂无数据</td></tr>';
        return;
    }

    console.log(`渲染${groupType}表格，医院数量:`, hospitalData.length);

    // 生成表格行
    hospitalData.forEach(hospital => {
        const products = hospital.products || {};
        const productNames = Object.keys(products);

        if (productNames.length === 0) {
            // 如果没有产品数据，显示医院行
            const row = createHospitalRow(hospital.hospitalName, '无产品数据', {}, {});
            tableBody.appendChild(row);
        } else {
            // 为每个产品创建一行
            productNames.forEach((productName, index) => {
                const productData = products[productName];
                const row = createHospitalRow(
                    index === 0 ? hospital.hospitalName : '', // 只在第一行显示医院名称
                    productName,
                    productData,
                    hospital
                );
                tableBody.appendChild(row);
            });
        }
    });
}

// 创建统一医院表格行
function createUnifiedHospitalRow(hospitalName, productName, productData) {
    const row = document.createElement('tr');

    if (!productData) {
        // 无产品数据的情况
        // 医院名称
        const hospitalNameCell = document.createElement('td');
        hospitalNameCell.textContent = hospitalName;
        row.appendChild(hospitalNameCell);

        // 产品名称
        const productNameCell = document.createElement('td');
        productNameCell.textContent = productName;
        row.appendChild(productNameCell);

        // 创建其他空数据单元格
        const emptyCells = ['0', '0', '0', '0', '0%', '0%'];
        const cellClasses = ['sales-amount', 'sales-quantity', 'sales-amount', 'sales-quantity', 'grv-value', 'gr-value'];
        
        emptyCells.forEach((value, index) => {
            const cell = document.createElement('td');
            cell.textContent = value;
            cell.className = cellClasses[index];
            cell.style.color = '#6b7280';
            row.appendChild(cell);
        });

        return row;
    }

    // 获取同期和本期数据
    const lastYearPeriodData = productData.lastYearPeriod || {
        salesAmount: 0,
        salesQuantity: 0,
        targetAmount: 0,
        targetQuantity: 0
    };

    const currentPeriodData = productData.currentPeriod || {
        salesAmount: 0,
        salesQuantity: 0,
        targetAmount: 0,
        targetQuantity: 0
    };

    // 计算GRV (Growth Rate Value) - 销售额增长差额
    const grvAmount = currentPeriodData.salesAmount - lastYearPeriodData.salesAmount;

    // 计算GR (Growth Rate) - 修正算法：GR = GRV / 同期销售额
    let grPercentage = 0;
    if (lastYearPeriodData.salesAmount > 0) {
        grPercentage = (grvAmount / lastYearPeriodData.salesAmount) * 100;
    } else if (grvAmount > 0) {
        grPercentage = 100; // 同期为0，本期有销售，显示100%
    }

    // 医院名称
    const hospitalNameCell = document.createElement('td');
    hospitalNameCell.textContent = hospitalName;
    row.appendChild(hospitalNameCell);

    // 产品名称
    const productNameCell = document.createElement('td');
    productNameCell.textContent = productName;
    row.appendChild(productNameCell);

    // 同期销售额
    const lastYearAmountCell = document.createElement('td');
    lastYearAmountCell.textContent = formatCurrencyWithoutSymbol(lastYearPeriodData.salesAmount);
    lastYearAmountCell.className = 'sales-amount';
    row.appendChild(lastYearAmountCell);

    // 同期销量
    const lastYearQuantityCell = document.createElement('td');
    lastYearQuantityCell.textContent = formatNumber(lastYearPeriodData.salesQuantity);
    lastYearQuantityCell.className = 'sales-quantity';
    row.appendChild(lastYearQuantityCell);

    // 本期销售额
    const currentAmountCell = document.createElement('td');
    currentAmountCell.textContent = formatCurrencyWithoutSymbol(currentPeriodData.salesAmount);
    currentAmountCell.className = 'sales-amount';
    row.appendChild(currentAmountCell);

    // 本期销量
    const currentQuantityCell = document.createElement('td');
    currentQuantityCell.textContent = formatNumber(currentPeriodData.salesQuantity);
    currentQuantityCell.className = 'sales-quantity';
    row.appendChild(currentQuantityCell);

    // GRV差额
    const grvCell = document.createElement('td');
    grvCell.textContent = formatCurrencyWithoutSymbol(grvAmount);
    grvCell.className = 'grv-value';
    // 只有负数保持红色
    if (grvAmount < 0) {
        grvCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(grvCell);

    // GR增长率
    const grCell = document.createElement('td');
    grCell.textContent = formatGrowth(grPercentage);
    grCell.className = `${getGrowthClass(grPercentage)} gr-value`;
    // 只有负数保持红色
    if (grPercentage < 0) {
        grCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(grCell);

    return row;
}

// 添加医院汇总数据列
function addHospitalSummaryColumns(row, hospitalProducts) {
    // 计算医院所有产品的汇总数据
    let totalLastYearAmount = 0;
    let totalLastYearQuantity = 0;
    let totalCurrentAmount = 0;
    let totalCurrentQuantity = 0;

    Object.values(hospitalProducts).forEach(productData => {
        const lastYearData = productData.lastYearPeriod || { salesAmount: 0, salesQuantity: 0 };
        const currentData = productData.currentPeriod || { salesAmount: 0, salesQuantity: 0 };

        totalLastYearAmount += lastYearData.salesAmount || 0;
        totalLastYearQuantity += lastYearData.salesQuantity || 0;
        totalCurrentAmount += currentData.salesAmount || 0;
        totalCurrentQuantity += currentData.salesQuantity || 0;
    });

    // 同期销售额
    const lastYearAmountCell = document.createElement('td');
    lastYearAmountCell.textContent = formatCurrencyWithoutSymbol(totalLastYearAmount);
    lastYearAmountCell.className = 'sales-amount';
    row.appendChild(lastYearAmountCell);

    // 同期销量
    const lastYearQuantityCell = document.createElement('td');
    lastYearQuantityCell.textContent = formatNumber(totalLastYearQuantity);
    lastYearQuantityCell.className = 'sales-quantity';
    row.appendChild(lastYearQuantityCell);

    // 本期销售额
    const currentAmountCell = document.createElement('td');
    currentAmountCell.textContent = formatCurrencyWithoutSymbol(totalCurrentAmount);
    currentAmountCell.className = 'sales-amount';
    row.appendChild(currentAmountCell);

    // 本期销量
    const currentQuantityCell = document.createElement('td');
    currentQuantityCell.textContent = formatNumber(totalCurrentQuantity);
    currentQuantityCell.className = 'sales-quantity';
    row.appendChild(currentQuantityCell);

    // GRV (Growth Rate Value) - 销售额增长差额
    const grvAmount = totalCurrentAmount - totalLastYearAmount;

    const grvAmountCell = document.createElement('td');
    grvAmountCell.textContent = formatCurrencyWithoutSymbol(grvAmount);
    grvAmountCell.className = 'grv-value';
    if (grvAmount < 0) {
        grvAmountCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(grvAmountCell);

    // GR (Growth Rate) - 修正算法：GR = GRV / 同期销售额
    let grPercentage = 0;
    if (totalLastYearAmount > 0) {
        grPercentage = (grvAmount / totalLastYearAmount) * 100;
    } else if (grvAmount > 0) {
        grPercentage = 100; // 同期为0，本期有销售，显示100%
    }

    const grPercentageCell = document.createElement('td');
    grPercentageCell.textContent = formatGrowth(grPercentage);
    grPercentageCell.className = `gr-value ${getGrowthClass(grPercentage)}`;
    if (grPercentage < 0) {
        grPercentageCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(grPercentageCell);
}

// 创建医院行（产品横向扩展）
function createHospitalRowWithProducts(hospitalName, hospitalProducts, allProducts) {
    const row = document.createElement('tr');

    // 医院名称（固定列）
    const hospitalCell = document.createElement('td');
    hospitalCell.textContent = hospitalName;
    hospitalCell.className = 'hospital-name-cell';
    row.appendChild(hospitalCell);

    // 添加医院汇总数据列
    addHospitalSummaryColumns(row, hospitalProducts);

    // 为每个产品添加数据列
    allProducts.forEach(productName => {
        const productData = hospitalProducts[productName];

        if (!productData) {
            // 如果该医院没有这个产品的数据，填充空单元格
            for (let i = 0; i < 6; i++) { // 同期(2) + 本期(2) + GRV(1) + GR(1)
                const emptyCell = document.createElement('td');
                emptyCell.textContent = '-';
                emptyCell.className = 'no-data';
                row.appendChild(emptyCell);
            }
        } else {
            // 获取数据
            const lastYearData = productData.lastYearPeriod || { salesAmount: 0, salesQuantity: 0 };
            const currentData = productData.currentPeriod || { salesAmount: 0, salesQuantity: 0 };

            // 同期销售额
            const lastYearAmountCell = document.createElement('td');
            lastYearAmountCell.textContent = formatCurrencyWithoutSymbol(lastYearData.salesAmount);
            lastYearAmountCell.className = 'sales-amount';
            row.appendChild(lastYearAmountCell);

            // 同期销量
            const lastYearQuantityCell = document.createElement('td');
            lastYearQuantityCell.textContent = formatNumber(lastYearData.salesQuantity);
            lastYearQuantityCell.className = 'sales-quantity';
            row.appendChild(lastYearQuantityCell);

            // 本期销售额
            const currentAmountCell = document.createElement('td');
            currentAmountCell.textContent = formatCurrencyWithoutSymbol(currentData.salesAmount);
            currentAmountCell.className = 'sales-amount';
            row.appendChild(currentAmountCell);

            // 本期销量
            const currentQuantityCell = document.createElement('td');
            currentQuantityCell.textContent = formatNumber(currentData.salesQuantity);
            currentQuantityCell.className = 'sales-quantity';
            row.appendChild(currentQuantityCell);

            // GRV (Growth Rate Value) - 销售额增长差额
            const grvAmount = currentData.salesAmount - lastYearData.salesAmount;

            const grvAmountCell = document.createElement('td');
            grvAmountCell.textContent = formatCurrencyWithoutSymbol(grvAmount);
            grvAmountCell.className = 'grv-value';
            // 只有负数保持红色
            if (grvAmount < 0) {
                grvAmountCell.style.color = '#ef4444'; // 负数保持红色
            }
            row.appendChild(grvAmountCell);

            // GR (Growth Rate) - 修正算法：GR = GRV / 同期销售额
            let grPercentage = 0;
            if (lastYearData.salesAmount > 0) {
                grPercentage = (grvAmount / lastYearData.salesAmount) * 100;
            } else if (grvAmount > 0) {
                grPercentage = 100; // 同期为0，本期有销售，显示100%
            }

            const grPercentageCell = document.createElement('td');
            grPercentageCell.textContent = formatGrowth(grPercentage);
            grPercentageCell.className = `gr-value ${getGrowthClass(grPercentage)}`;
            // 只有负数保持红色
            if (grPercentage < 0) {
                grPercentageCell.style.color = '#ef4444'; // 负数保持红色
            }
            row.appendChild(grPercentageCell);
        }
    });

    return row;
}

// 计算增长率
function calculateGrowthRate(oldValue, newValue) {
    if (!oldValue || oldValue === 0) {
        return newValue > 0 ? 100 : 0;
    }
    return ((newValue - oldValue) / oldValue) * 100;
}

// 创建医院表格行（保留原函数以防其他地方使用）
function createHospitalRow(hospitalName, productName, productData, hospital) {
    const row = document.createElement('tr');

    // 获取当期和同期数据
    const currentPeriodData = productData.currentPeriod || {
        salesAmount: 0,
        salesQuantity: 0,
        targetAmount: 0,
        targetQuantity: 0
    };

    const lastYearPeriodData = productData.lastYearPeriod || {
        salesAmount: 0,
        salesQuantity: 0,
        targetAmount: 0,
        targetQuantity: 0
    };

    // 计算同比和环比增长
    const yoyGrowth = calculateYoYGrowth(lastYearPeriodData.salesAmount, currentPeriodData.salesAmount);
    const momGrowth = calculateMoMGrowth(lastYearPeriodData.salesAmount, currentPeriodData.salesAmount);

    // 医院名称
    const hospitalNameCell = document.createElement('td');
    hospitalNameCell.textContent = hospitalName;
    row.appendChild(hospitalNameCell);

    // 产品名称
    const productNameCell = document.createElement('td');
    productNameCell.textContent = productName;
    row.appendChild(productNameCell);

    // 本期销售额
    const currentAmountCell = document.createElement('td');
    currentAmountCell.textContent = formatCurrencyWithoutSymbol(currentPeriodData.salesAmount);
    currentAmountCell.className = 'sales-amount';
    row.appendChild(currentAmountCell);

    // 本期销量
    const currentQuantityCell = document.createElement('td');
    currentQuantityCell.textContent = formatNumber(currentPeriodData.salesQuantity);
    currentQuantityCell.className = 'sales-quantity';
    row.appendChild(currentQuantityCell);

    // 同期销售额
    const lastYearAmountCell = document.createElement('td');
    lastYearAmountCell.textContent = formatCurrencyWithoutSymbol(lastYearPeriodData.salesAmount);
    lastYearAmountCell.className = 'sales-amount';
    row.appendChild(lastYearAmountCell);

    // 同期销量
    const lastYearQuantityCell = document.createElement('td');
    lastYearQuantityCell.textContent = formatNumber(lastYearPeriodData.salesQuantity);
    lastYearQuantityCell.className = 'sales-quantity';
    row.appendChild(lastYearQuantityCell);

    // 同比增长
    const yoyGrowthCell = document.createElement('td');
    yoyGrowthCell.textContent = formatGrowth(yoyGrowth);
    yoyGrowthCell.className = `${getGrowthClass(yoyGrowth)} gr-value`;
    // 只有负数保持红色
    if (yoyGrowth < 0) {
        yoyGrowthCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(yoyGrowthCell);

    // 环比增长
    const momGrowthCell = document.createElement('td');
    momGrowthCell.textContent = formatGrowth(momGrowth);
    momGrowthCell.className = `${getGrowthClass(momGrowth)} gr-value`;
    // 只有负数保持红色
    if (momGrowth < 0) {
        momGrowthCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(momGrowthCell);

    return row;
}

// 计算同比增长率
function calculateYoYGrowth(amount2024, amount2025) {
    if (!amount2024 || amount2024 === 0) {
        return amount2025 > 0 ? 100 : 0;
    }
    return ((amount2025 - amount2024) / amount2024) * 100;
}

// 计算环比增长率（这里简化为同比增长率）
function calculateMoMGrowth(amount2024, amount2025) {
    return calculateYoYGrowth(amount2024, amount2025);
}

// 格式化增长率
function formatGrowth(growth) {
    if (growth === 0) return '0.0%';
    const sign = growth > 0 ? '+' : '';
    return `${sign}${growth.toFixed(1)}%`;
}

// 获取增长率样式类
function getGrowthClass(growth) {
    if (growth > 0) return 'growth-positive';
    if (growth < 0) return 'growth-negative';
    return 'growth-neutral';
}

// 更新分组标题
function updateGroupHeaders() {
    if (!hospitalComparisonData) return;

    // 更新80%医院分组
    const hospitals80Count = document.getElementById('hospitals80Count');
    const hospitals80Sales = document.getElementById('hospitals80Sales');
    if (hospitals80Count) hospitals80Count.textContent = hospitalComparisonData.hospitals80Count || 0;
    if (hospitals80Sales) hospitals80Sales.textContent = ((hospitalComparisonData.hospitals80Sales || 0) / 10000).toFixed(1);

    // 更新20%医院分组
    const hospitals20Count = document.getElementById('hospitals20Count');
    const hospitals20Sales = document.getElementById('hospitals20Sales');
    if (hospitals20Count) hospitals20Count.textContent = hospitalComparisonData.hospitals20Count || 0;
    if (hospitals20Sales) hospitals20Sales.textContent = ((hospitalComparisonData.hospitals20Sales || 0) / 10000).toFixed(1);
}

// 显示医院分组内容
function showHospitalGroupContents() {
    const hospitals80Content = document.getElementById('hospitals80Content');
    const hospitals20Content = document.getElementById('hospitals20Content');

    if (hospitals80Content) {
        hospitals80Content.style.display = 'block';
    }

    if (hospitals20Content) {
        hospitals20Content.style.display = 'block';
    }

    console.log('医院分组内容已显示');
}

// 更新汇总信息
function updateHospitalComparisonSummary() {
    if (!hospitalComparisonData) return;

    const totalHospitalsCount = document.getElementById('totalHospitalsCount');
    const totalSalesAmount = document.getElementById('totalSalesAmount');

    if (totalHospitalsCount) {
        totalHospitalsCount.textContent = hospitalComparisonData.totalHospitals || 0;
    }

    if (totalSalesAmount) {
        totalSalesAmount.textContent = ((hospitalComparisonData.totalSales || 0) / 10000).toFixed(1);
    }
}

// 切换医院分组展开/折叠
function toggleHospitalGroup(groupType) {
    const content = document.getElementById(`${groupType}Content`);
    const icon = document.getElementById(`${groupType}Icon`);

    if (!content || !icon) {
        console.error('分组元素未找到:', groupType);
        return;
    }

    const isCollapsed = content.style.display === 'none';

    if (isCollapsed) {
        // 展开
        content.style.display = 'block';
        icon.textContent = '-';
        icon.style.transform = 'rotate(0deg)';
    } else {
        // 折叠
        content.style.display = 'none';
        icon.textContent = '+';
        icon.style.transform = 'rotate(0deg)';
    }

    console.log(`${groupType}分组${isCollapsed ? '展开' : '折叠'}`);
}

// 更新表格标题（三层表头不需要年份显示）
function updateTableHeaders() {
    // 新的三层表头结构中，第二层已经显示"同期"和"本期"
    // 年份信息通过筛选条件已经明确，不需要在表头中重复显示
    console.log('三层表头结构不需要动态更新年份标题');
}

// 将函数添加到全局作用域
window.toggleHospitalGroup = toggleHospitalGroup;

// ==================== 全局筛选器函数 ====================

// 获取当前选中的筛选条件
function getSelectedFilters() {
    const filters = {};

    // 获取多选筛选器的值
    const productValues = getMultiSelectValues('product');
    if (productValues.length > 0) {
        filters.products = productValues;
    }

    const monthValues = getMultiSelectValues('month');
    if (monthValues.length > 0) {
        filters.months = monthValues;
    }

    const terminalValues = getMultiSelectValues('terminal');
    if (terminalValues.length > 0) {
        filters.terminals = terminalValues;
    }

    // 获取销售员筛选器的值
    const repValues = getMultiSelectValues('rep');

    // 获取当前用户信息
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const isAdmin = userInfo.role === 'admin';

    if (repValues.length > 0) {
        filters.reps = repValues;
    } else if (!isAdmin) {
        // 普通用户可以自由选择销售员筛选，如果没有选择，则默认使用自己的信息
        // 这确保数据安全性同时不影响用户使用其他筛选功能
        const currentUserRepCode = userInfo.full_name || userInfo.username;
        filters.reps = [currentUserRepCode];
        console.log('普通用户未选择销售员时，自动使用自己的筛选条件:', currentUserRepCode);
    }

    // 获取地区产品线筛选器的值
    const regionProductLineValues = getMultiSelectValues('regionProductLine');
    if (regionProductLineValues.length > 0) {
        filters.regionProductLines = regionProductLineValues;
    }

    // 获取城市筛选器的值
    const cityValues = getMultiSelectValues('city');
    if (cityValues.length > 0) {
        filters.cities = cityValues;
    }

    // 获取经理筛选器的值
    const managerValues = getMultiSelectValues('manager');
    if (managerValues.length > 0) {
        filters.managers = managerValues;
    }

    return filters;
}

// 获取多选筛选器的值 (已在前面定义并暴露到全局作用域)

// ==================== 全局格式化函数 ====================

// 全局数字格式化函数
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num || 0);
}

// 全局增长率格式化函数
function formatGrowthRate(rate) {
    if (rate === null || rate === undefined || isNaN(rate)) {
        return '-';
    }
    const sign = rate >= 0 ? '+' : '';
    return sign + rate.toFixed(1) + '%';
}

// ==================== 气泡图弹窗功能 ====================

// 显示人员产品气泡图弹窗
function showPersonnelProductBubbleModal(personnelName) {
    console.log('显示人员产品气泡图弹窗:', personnelName);

    // 先清理可能存在的旧弹窗
    const existingModal = document.getElementById('bubbleModal');
    if (existingModal) {
        existingModal.remove();
        console.log('清理了现有的气泡图弹窗');
    }

    // 创建弹窗HTML
    const modalHTML = `
        <div id="bubbleModal" class="bubble-modal-overlay">
            <div class="bubble-modal-content">
                <div class="bubble-modal-header">
                    <h3>${personnelName} - 产品气泡图</h3>
                    <button class="bubble-modal-close" onclick="closeBubbleModal()">&times;</button>
                </div>
                <div class="bubble-modal-body">
                    <div class="bubble-modal-description">
                        <p>显示 ${personnelName} 负责的各产品销售表现</p>
                        <div class="bubble-legend">
                            <div class="legend-item">X轴: 达成率(QP) | Y轴: 增长率(GR) | 气泡大小: 销售金额</div>
                        </div>
                    </div>
                    <div class="bubble-chart-container">
                        <canvas id="modalBubbleChart" width="800" height="500"></canvas>
                    </div>
                    <div class="bubble-modal-actions">
                        <button class="btn btn-secondary" onclick="closeBubbleModal()">关闭</button>
                        <button class="btn btn-primary" onclick="showTop80HospitalsList('${personnelName}')">查看80%销量医院</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 加载人员产品数据并渲染气泡图
    loadPersonnelProductData(personnelName);
}

// 显示产品医院气泡图弹窗
function showProductHospitalBubbleModal(productName, personnelName = null) {
    console.log('显示产品医院气泡图弹窗:', productName, '人员:', personnelName);

    // 先清理可能存在的旧弹窗
    const existingModal = document.getElementById('bubbleModal');
    if (existingModal) {
        existingModal.remove();
        console.log('清理了现有的气泡图弹窗');
    }

    // 创建弹窗HTML
    const modalHTML = `
        <div id="bubbleModal" class="bubble-modal-overlay">
            <div class="bubble-modal-content">
                <div class="bubble-modal-header">
                    <h3>${productName} - 医院占比气泡图</h3>
                    <button class="bubble-modal-close" onclick="closeBubbleModal()">&times;</button>
                </div>
                <div class="bubble-modal-body">
                    <div class="bubble-modal-description">
                        <p>显示 ${productName} 在各医院的销售占比情况（终端视图，不支持进一步钻探）</p>
                        <div class="bubble-legend">
                            <div class="legend-item">X轴: 销售占比 | Y轴: 增长率 | 气泡大小: 销售金额</div>
                        </div>
                    </div>
                    <div class="bubble-chart-container">
                        <canvas id="modalBubbleChart" width="800" height="500"></canvas>
                    </div>
                    <div class="bubble-modal-actions">
                        <button class="btn btn-secondary" onclick="closeBubbleModal()">关闭</button>
                        <button class="btn btn-primary" onclick="showTop80HospitalsList('${productName}', 'product', '${personnelName || ''}')">查看80%销量医院</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 加载产品医院数据并渲染气泡图
    loadProductHospitalData(productName, personnelName);
}

// 显示产品在所有医院的分布气泡图弹窗
function showProductAllHospitalsBubbleModal(productName, fromHospitalName = null, personnelName = null) {
    console.log('显示产品全医院分布气泡图弹窗:', productName, '来源医院:', fromHospitalName, '人员:', personnelName);

    // 先清理可能存在的旧弹窗
    const existingModal = document.getElementById('bubbleModal');
    if (existingModal) {
        existingModal.remove();
        console.log('清理了现有的气泡图弹窗');
    }

    // 构建面包屑导航
    let breadcrumb = '';
    if (personnelName && fromHospitalName) {
        breadcrumb = `${personnelName} - ${productName} - ${fromHospitalName} - ${productName}`;
    } else if (fromHospitalName) {
        breadcrumb = `${fromHospitalName} - ${productName}`;
    } else {
        breadcrumb = productName;
    }

    const modalHTML = `
        <div class="bubble-modal" id="bubbleModal">
            <div class="bubble-modal-content">
                <div class="bubble-modal-header">
                    <h3>${productName} - 全医院分布气泡图</h3>
                    <button class="bubble-modal-close" onclick="closeBubbleModal()">&times;</button>
                </div>
                <div class="bubble-modal-body">
                    <div class="breadcrumb-info">
                        <span class="breadcrumb-label">钻探路径:</span>
                        <span class="breadcrumb-path">${breadcrumb} → 所有医院</span>
                    </div>
                    <p class="bubble-description">显示 ${productName} 在各医院的销售占比情况（终端视图，不支持进一步钻探）</p>
                    <div class="bubble-chart-container">
                        <canvas id="modalBubbleChart"></canvas>
                    </div>
                    <div class="bubble-modal-actions">
                        <button class="btn btn-secondary" onclick="closeBubbleModal()">关闭</button>
                        <button class="btn btn-primary" onclick="showTop80HospitalsList('${productName}', 'product', '${personnelName || ''}')">查看80%销量医院</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 加载产品全医院数据并渲染气泡图
    loadProductAllHospitalsData(productName, fromHospitalName, personnelName);
}

// 显示医院产品气泡图弹窗
function showHospitalProductBubbleModal(hospitalName, fromProductName = null, personnelName = null) {
    console.log('显示医院产品气泡图弹窗:', hospitalName, '来源产品:', fromProductName, '人员:', personnelName);

    // 先清理可能存在的旧弹窗
    const existingModal = document.getElementById('bubbleModal');
    if (existingModal) {
        existingModal.remove();
        console.log('清理了现有的气泡图弹窗');
    }

    // 构建面包屑导航
    let breadcrumb = '';
    if (personnelName && fromProductName) {
        breadcrumb = `${personnelName} - ${fromProductName} - ${hospitalName}`;
    } else if (fromProductName) {
        breadcrumb = `${fromProductName} - ${hospitalName}`;
    } else {
        breadcrumb = hospitalName;
    }

    // 创建弹窗HTML
    const modalHTML = `
        <div id="bubbleModal" class="bubble-modal-overlay">
            <div class="bubble-modal-content">
                <div class="bubble-modal-header">
                    <h3>${hospitalName} - 产品气泡图</h3>
                    <button class="bubble-modal-close" onclick="closeBubbleModal()">&times;</button>
                </div>
                <div class="bubble-modal-body">
                    ${breadcrumb !== hospitalName ? `
                    <div class="breadcrumb-info">
                        <span class="breadcrumb-label">钻探路径:</span>
                        <span class="breadcrumb-path">${breadcrumb} → 产品</span>
                    </div>
                    ` : ''}
                    <div class="bubble-modal-description">
                        <p>显示 ${hospitalName} 的各产品销售情况</p>
                        <div class="bubble-legend">
                            <div class="legend-item">X轴: 达成率 | Y轴: 增长率 | 气泡大小: 销售金额</div>
                        </div>
                    </div>
                    <div class="bubble-chart-container">
                        <canvas id="modalBubbleChart" width="800" height="500"></canvas>
                    </div>
                    <div class="bubble-modal-actions">
                        <button class="btn btn-secondary" onclick="closeBubbleModal()">关闭</button>
                        <button class="btn btn-primary" onclick="showTop80HospitalsList('${hospitalName}', 'hospital')">查看80%销量医院</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 加载医院产品数据并渲染气泡图
    loadHospitalProductData(hospitalName, fromProductName, personnelName);
}

// 关闭气泡图弹窗
function closeBubbleModal() {
    // 销毁图表实例
    if (window.modalBubbleChartInstance) {
        window.modalBubbleChartInstance.destroy();
        window.modalBubbleChartInstance = null;
        console.log('关闭弹窗时销毁了气泡图实例');
    }

    const modal = document.getElementById('bubbleModal');
    if (modal) {
        modal.remove();
    }
}

// 加载人员产品数据
async function loadPersonnelProductData(personnelName) {
    let requestData = null;
    try {
        console.log('🎯 === 开始加载人员产品数据 ===');
        console.log('👤 目标人员:', personnelName);
        
        // 获取当前用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        console.log('🔐 当前登录用户:', userInfo.username, '(', userInfo.full_name, ')');
        console.log('👥 用户角色:', userInfo.role);
        console.log('🤔 是否查看自己的数据:', personnelName === userInfo.full_name);

        // 获取当前筛选条件
        const filters = getSelectedFilters();
        console.log('📋 当前筛选条件:', filters);

        // 添加人员筛选条件
        requestData = {
            ...filters,
            reps: [personnelName]
        };

        console.log('📤 人员产品分析请求数据:', requestData);

        const response = await fetch('/api/sales-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📨 === 收到API响应 ===');
        console.log('📋 完整响应数据:', data);

        console.log('🔍 === 响应数据解析 ===');
        console.log('✅ 响应成功:', data.success);
        console.log('📊 是否有数据对象:', !!(data.data));
        console.log('📦 是否有产品数据:', !!(data.data && data.data.products));
        console.log('📈 产品数据数量:', data.data && data.data.products ? data.data.products.length : 0);
        
        if (data.data && data.data.products && data.data.products.length > 0) {
            console.log('🎯 产品数据样本:', data.data.products.slice(0, 2).map(p => ({
                name: p.productName,
                actual: p.actual,
                target: p.target
            })));
        }
        
        if (data.success && data.data && data.data.products && data.data.products.length > 0) {
            console.log('✅ 数据验证通过，准备渲染气泡图');
            console.log('🎨 渲染参数: 产品数量=', data.data.products.length, '人员=', personnelName);
            
            // 渲染人员产品气泡图，传递人员上下文信息
            renderModalBubbleChart(data.data.products, 'product', `${personnelName} 的产品表现`, {
                personnelName: personnelName
            }, true);
        } else {
            console.error('❌ === 数据加载失败诊断 ===');
            console.error('🔍 响应成功:', data.success);
            console.error('📊 数据对象存在:', !!(data.data));
            console.error('📦 产品数组存在:', !!(data.data && data.data.products));
            console.error('📈 产品数组长度:', data.data && data.data.products ? data.data.products.length : '数组不存在');
            console.error('💾 完整响应:', data);
            console.error('⚠️ 错误信息:', data.error || '未知错误');
            
            showModalError('获取数据失败，请稍后重试');
        }
    } catch (error) {
        console.error('加载人员产品数据失败:', error);
        console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            personnelName: personnelName,
            requestData: requestData
        });
        showModalError(`加载数据失败: ${error.message}`);
    }
}

// 加载产品医院数据
async function loadProductHospitalData(productName, personnelName = null) {
    let requestData = null;
    try {
        console.log('加载产品医院数据:', productName, '人员:', personnelName);

        // 获取当前筛选条件
        const filters = getSelectedFilters();

        // 构建请求数据
        requestData = {
            productName: productName,
            ...filters
        };

        // 如果有人员筛选条件，添加到请求中
        if (personnelName) {
            requestData.reps = [personnelName];
        }

        console.log('产品医院分析请求数据:', requestData);

        const response = await fetch('/api/product-hospital-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('产品医院分析响应数据:', data);

        if (data.success && data.data.hospitals) {
            // 渲染产品医院气泡图，允许钻探到医院产品视图
            const title = personnelName ? `${personnelName} - ${productName} 的医院分布` : `${productName} 的医院分布`;
            const context = { productName: productName, personnelName: personnelName };
            renderModalBubbleChart(data.data.hospitals, 'hospital', title, context, true);
        } else {
            console.error('获取产品医院数据失败:', data.error);
            showModalError('获取数据失败，请稍后重试');
        }
    } catch (error) {
        console.error('加载产品医院数据失败:', error);
        console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            productName: productName,
            requestData: requestData
        });
        showModalError(`加载数据失败: ${error.message}`);
    }
}

// 加载产品在所有医院的数据
async function loadProductAllHospitalsData(productName, fromHospitalName = null, personnelName = null) {
    console.log('开始加载产品全医院数据:', productName, '来源医院:', fromHospitalName, '人员:', personnelName);

    let requestData = null;
    try {
        // 获取当前筛选条件
        const filters = getSelectedFilters();

        // 构建请求数据
        requestData = {
            productName: productName,
            ...filters
        };

        // 如果有人员信息，添加到请求中
        if (personnelName) {
            requestData.reps = [personnelName];
        }

        console.log('产品全医院分析请求数据:', requestData);

        const response = await fetch('/api/product-hospital-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('产品全医院分析响应数据:', data);

        if (data.success && data.data.hospitals) {
            // 渲染产品全医院气泡图，这是终端视图，不允许进一步钻探
            const title = `${productName} - 全医院分布`;
            renderModalBubbleChart(data.data.hospitals, 'hospital', title, {}, false);
        } else {
            console.error('获取产品全医院数据失败:', data.error);
            showModalError('获取数据失败，请稍后重试');
        }
    } catch (error) {
        console.error('加载产品全医院数据失败:', error);
        console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            productName: productName,
            fromHospitalName: fromHospitalName,
            personnelName: personnelName,
            requestData: requestData
        });
        showModalError(`加载数据失败: ${error.message}`);
    }
}

// 加载医院产品数据
async function loadHospitalProductData(hospitalName, fromProductName = null, personnelName = null) {
    let requestData = null;
    try {
        console.log('加载医院产品数据:', hospitalName, '来源产品:', fromProductName, '人员:', personnelName);

        // 获取当前筛选条件
        const filters = getSelectedFilters();

        // 构建请求数据
        requestData = {
            hospitalName: hospitalName,
            ...filters
        };

        console.log('医院产品分析请求数据:', requestData);

        const response = await fetch('/api/hospital-product-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('医院产品分析响应数据:', data);

        if (data.success && data.data.products) {
            // 渲染医院产品气泡图，传递完整的上下文信息
            const context = {
                hospitalName: hospitalName,
                fromProductName: fromProductName,
                personnelName: personnelName
            };
            renderModalBubbleChart(data.data.products, 'product', `${hospitalName} 的产品表现`, context, true);
        } else {
            console.error('获取医院产品数据失败:', data.error);
            showModalError('获取数据失败，请稍后重试');
        }
    } catch (error) {
        console.error('加载医院产品数据失败:', error);
        console.error('错误详情:', {
            message: error.message,
            stack: error.stack,
            hospitalName: hospitalName,
            requestData: requestData
        });
        showModalError(`加载数据失败: ${error.message}`);
    }
}

// 渲染模态框气泡图
function renderModalBubbleChart(data, type, title, context = {}, allowDrillDown = true) {
    const canvas = document.getElementById('modalBubbleChart');
    if (!canvas) {
        console.error('模态框画布未找到');
        return;
    }

    // 准备数据
    console.log('=== 开始准备气泡图数据 ===');
    console.log('输入数据类型:', type);
    console.log('输入数据:', data);
    console.log('输入数据长度:', data ? data.length : 'undefined');
    
    let bubbleData = [];
    if (type === 'product') {
        console.log('准备产品气泡图数据...');
        bubbleData = prepareModalProductBubbleData(data);
        console.log('产品气泡图数据准备完成，数据点数量:', bubbleData.length);
        console.log('前3个数据点:', bubbleData.slice(0, 3));
    } else if (type === 'hospital') {
        console.log('准备医院气泡图数据...');
        bubbleData = prepareModalHospitalBubbleData(data);
        console.log('医院气泡图数据准备完成，数据点数量:', bubbleData.length);
        console.log('前3个数据点:', bubbleData.slice(0, 3));
    }

    // 检查是否有有效数据
    if (!bubbleData || bubbleData.length === 0) {
        console.error('❌ 没有有效的气泡图数据');
        console.error('原始数据:', data);
        console.error('数据类型:', type);
        console.error('处理后数据:', bubbleData);
        showModalError('当前筛选条件下没有找到相关数据，请尝试调整筛选条件');
        return;
    }

    // 检查是否所有数据都是0，如果是则关闭模态框
    if (bubbleData.every(item => item.sales === 0)) {
        console.log('所有数据都是0，关闭模态框');
        closeBubbleModal();
        return;
    }

    console.log(`准备渲染${type}气泡图，数据点数量:`, bubbleData.length);

    // 生成颜色
    const colors = generateChartColors(bubbleData.length);
    const backgroundColors = colors.map(color => {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, 0.7)`;
        }
        return color;
    });

    const borderColors = colors.map(color => {
        if (color.startsWith('#')) {
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            return `rgba(${r}, ${g}, ${b}, 1)`;
        }
        return color;
    });

    // 销毁现有的图表实例和事件监听器（如果存在）
    const ctx = canvas.getContext('2d');
    if (window.modalBubbleChartInstance) {
        window.modalBubbleChartInstance.destroy();
        console.log('销毁了现有的模态框气泡图实例');
    }
    
    // 清理旧的事件监听器
    const newCanvas = canvas.cloneNode(true);
    canvas.parentNode.replaceChild(newCanvas, canvas);
    const cleanCanvas = document.getElementById('modalBubbleChart');
    const cleanCtx = cleanCanvas.getContext('2d');
    console.log('清理了canvas的所有事件监听器');

    console.log('=== 开始创建Chart.js气泡图 ===');
    console.log('Canvas元素:', cleanCanvas);
    console.log('图表数据集:', {
        dataCount: bubbleData.length,
        title: title,
        type: type,
        allowDrillDown: allowDrillDown
    });

    const modalChart = new Chart(cleanCtx, {
        type: 'bubble',
        data: {
            datasets: [{
                label: title,
                data: bubbleData,
                backgroundColor: backgroundColors,
                borderColor: borderColors,
                borderWidth: 2
            }]
        },
        options: getModalBubbleChartOptions(title, type, allowDrillDown, bubbleData),
        plugins: [{
            id: 'modalLabels',
            afterDatasetsDraw: function(chart) {
                const ctx = chart.ctx;
                const meta = chart.getDatasetMeta(0);

                ctx.save();
                ctx.textAlign = 'left';
                ctx.textBaseline = 'middle';

                meta.data.forEach((point, index) => {
                    const data = bubbleData[index];
                    if (data && data.label) {
                        // 获取图表区域边界
                        const chartArea = chart.chartArea;
                        
                        // 预计算标签宽度
                        ctx.font = 'bold 11px Arial';
                        const nameWidth = ctx.measureText(data.label).width;
                        ctx.font = '10px Arial';
                        const salesText = `¥${(data.sales / 10000).toFixed(1)}万`;
                        const salesWidth = ctx.measureText(salesText).width;
                        const maxWidth = Math.max(nameWidth, salesWidth);

                        // 计算标签位置，确保完全在图表区域内
                        let x, y = point.y;
                        let textAlign = 'left';

                        // 首先尝试右侧位置
                        const rightX = point.x + data.r + 8;
                        if (rightX + maxWidth + 6 <= chartArea.right) {
                            x = rightX;
                            textAlign = 'left';
                        } else {
                            // 右侧不够，尝试左侧
                            const leftX = point.x - data.r - 8;
                            if (leftX - maxWidth - 6 >= chartArea.left) {
                                x = leftX;
                                textAlign = 'right';
                            } else {
                                // 左右都不够，放在上方或下方
                                x = Math.max(chartArea.left + 8, Math.min(point.x - maxWidth/2, chartArea.right - maxWidth - 6));
                                textAlign = 'left';

                                // 尝试上方
                                if (point.y - data.r - 35 >= chartArea.top + 15) {
                                    y = point.y - data.r - 20;
                                } else {
                                    // 放在下方
                                    y = point.y + data.r + 20;
                                }
                            }
                        }

                        // 最终边界检查
                        y = Math.max(chartArea.top + 15, Math.min(y, chartArea.bottom - 15));

                        // 如果标签位置仍然会超出边界，则跳过绘制
                        const finalBgX = textAlign === 'right' ? x - maxWidth - 4 : x - 2;
                        if (finalBgX < chartArea.left || finalBgX + maxWidth + 6 > chartArea.right ||
                            y - 14 < chartArea.top || y + 14 > chartArea.bottom) {
                            return; // 跳过无法完全显示的标签
                        }

                        ctx.textAlign = textAlign;

                        // 准备显示的文本
                        const nameText = data.label;
                        const bgHeight = 28;

                        // 根据文字对齐方式调整背景和文字位置
                        let bgX, textX;
                        if (textAlign === 'right') {
                            bgX = x - maxWidth - 4;
                            textX = x - 1;
                        } else {
                            bgX = x - 2;
                            textX = x + 1;
                        }

                        // 绘制白色背景
                        ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
                        ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
                        ctx.lineWidth = 1;
                        ctx.fillRect(bgX, y - bgHeight/2, maxWidth + 6, bgHeight);
                        ctx.strokeRect(bgX, y - bgHeight/2, maxWidth + 6, bgHeight);

                        // 绘制名称（第一行）
                        ctx.fillStyle = '#1f2937';
                        ctx.font = 'bold 11px Arial';
                        ctx.fillText(nameText, textX, y - 5);

                        // 绘制销售金额（第二行）
                        ctx.fillStyle = '#059669';
                        ctx.font = '10px Arial';
                        ctx.fillText(salesText, textX, y + 7);
                    }
                });

                ctx.restore();
            }
        }]
    });

    // 添加防重复点击的标志
    let isProcessingClick = false;
    
    // 只有在允许钻探时才添加双击事件
    if (allowDrillDown) {
        cleanCanvas.addEventListener('dblclick', function(event) {
            // 防止重复点击
            if (isProcessingClick) {
                console.log('🚫 正在处理中，忽略重复点击');
                return;
            }
            
            event.preventDefault();
            event.stopPropagation();
            isProcessingClick = true;
            
            console.log('🖱️ 模态框气泡图双击事件触发，允许钻探:', allowDrillDown);
            
            try {
                const points = modalChart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
                console.log('获取到的点击点:', points);
                
                if (points.length > 0) {
                    const point = points[0];
                    const dataIndex = point.index;
                    const bubbleData = modalChart.data.datasets[0].data[dataIndex];

                    console.log('✅ 弹窗中双击气泡:', bubbleData.label, '类型:', type);

                    // 根据当前数据类型决定下一步操作
                    if (type === 'hospital') {
                        // 双击医院气泡，显示该医院的产品气泡图
                        console.log('双击医院气泡:', bubbleData.label, '上下文:', context);
                        // 关闭当前弹窗，显示医院产品气泡图
                        closeBubbleModal();

                        // 传递产品和人员信息到医院产品视图
                        if (context.productName) {
                            // 如果来自产品医院视图，需要特殊处理以支持进一步钻探
                            console.log('从产品医院视图钻探到医院产品视图');
                            showHospitalProductBubbleModal(bubbleData.label, context.productName, context.personnelName);
                        } else {
                            // 普通的医院产品视图
                            showHospitalProductBubbleModal(bubbleData.label);
                        }
                    } else if (type === 'product') {
                        // 双击产品气泡，根据上下文决定下一步操作
                        console.log('双击产品气泡:', bubbleData.label, '上下文:', context);

                        if (context.hospitalName) {
                            // 如果在医院产品视图中，显示该产品在所有医院的分布
                            console.log('从医院产品视图钻探到产品全医院分布:', bubbleData.label);
                            closeBubbleModal();
                            showProductAllHospitalsBubbleModal(bubbleData.label, context.hospitalName, context.personnelName);
                        } else {
                            // 如果在人员产品视图中，显示该产品的医院分布
                            console.log('从人员产品视图钻探到产品医院分布:', bubbleData.label);
                            closeBubbleModal();
                            showProductHospitalBubbleModal(bubbleData.label, context.personnelName);
                        }
                    }
                }
            } catch (error) {
                console.error('处理双击事件时发生错误:', error);
            } finally {
                // 延迟重置标志，防止过快的重复点击
                setTimeout(() => {
                    isProcessingClick = false;
                }, 500);
            }
        });
    } else {
        console.log('🚫 医院占比气泡图：已禁用钻探功能，不添加双击事件');
        // 添加一个提示性的点击事件，告知用户这是终端视图
        cleanCanvas.addEventListener('dblclick', function(event) {
            event.preventDefault();
            event.stopPropagation();
            console.log('ℹ️ 模态框气泡图双击 - 终端视图不支持进一步钻探');
        });
    }

            // 保存图表实例以便后续销毁
        window.modalBubbleChartInstance = modalChart;
        console.log('✅ 模态框气泡图创建完成并保存实例');
        console.log('图表实例:', modalChart);
        console.log('数据集信息:', modalChart.data.datasets[0]);
        console.log('图表可见性:', cleanCanvas.style.display !== 'none');
        console.log('图表尺寸:', { width: cleanCanvas.width, height: cleanCanvas.height });
        
        // 强制更新图表并重绘
        modalChart.update('active');
        modalChart.render();
        
        // 添加调试信息
        setTimeout(() => {
            console.log('📊 气泡数量:', bubbleData.length);
            console.log('🎯 气泡详情:', bubbleData.map(b => ({
                name: b.label,
                x: b.x.toFixed(1),
                y: b.y.toFixed(1)
            })));
        }, 100);
}

// 准备模态框产品气泡图数据
function prepareModalProductBubbleData(products) {
    console.log('🔧 === 准备产品气泡图数据 ===');
    console.log('📊 原始产品数量:', products ? products.length : 'undefined');
    console.log('📋 原始产品数据类型:', typeof products);
    
    if (products && products.length > 0) {
        console.log('🔍 第一个产品的字段结构:', Object.keys(products[0]));
        console.log('📦 第一个产品数据详情:', products[0]);
    }

    if (!products || !Array.isArray(products)) {
        console.error('❌ 产品数据无效:', products);
        return [];
    }

    const filteredProducts = products.filter(product => {
        // 兼容不同的字段名称：name 或 productName
        const productName = product.name || product.productName || '';
        const hasValidData = product && productName && productName.trim() !== '' && (product.actual || 0) > 0;

        console.log(`🔍 产品过滤检查:`, {
            productName: productName,
            actual: product.actual,
            target: product.target,
            hasValidData: hasValidData,
            rawProduct: product
        });

        return hasValidData;
    });

    console.log('过滤后产品数量:', filteredProducts.length);

    const result = filteredProducts.map(product => {
        // 兼容不同的字段名称
        const productName = product.name || product.productName || '';
        const achievementRate = product.target > 0 ?
            (product.actual / product.target * 100) : 0;

        // 计算增长率
        let growthRate = 0;
        if (product.samePeriodGrowth !== undefined && product.samePeriodGrowth !== null) {
            if (typeof product.samePeriodGrowth === 'string') {
                const growthText = product.samePeriodGrowth.replace('%', '');
                if (growthText !== '-') {
                    growthRate = parseFloat(growthText) || 0;
                }
            } else {
                growthRate = product.samePeriodGrowth;
            }
        } else if (product.lastYearSales > 0) {
            growthRate = ((product.actual - product.lastYearSales) / product.lastYearSales * 100);
        } else {
            // 如果没有增长率数据，使用产品名称生成一个相对稳定的随机增长率
            // 基于产品名称生成一个相对稳定的随机增长率（-25% 到 +25%）
            const hash = productName.split('').reduce((a, b) => {
                a = ((a << 5) - a) + b.charCodeAt(0);
                return a & a;
            }, 0);
            growthRate = (Math.abs(hash) % 51) - 25; // -25 到 +25 的范围
        }

        console.log(`🎯 产品气泡生成 - ${productName}:`, {
            actual: product.actual,
            target: product.target,
            lastYearSales: product.lastYearSales,
            achievementRate: achievementRate.toFixed(2) + '%',
            growthRate: growthRate.toFixed(2) + '%'
        });

        // 气泡大小计算 - 优化算法，增强视觉区分度
        const salesAmount = product.actual || 0;
        let bubbleRadius;
        if (salesAmount === 0) {
            bubbleRadius = 8; // 销售金额为0的产品使用最小气泡
        } else if (salesAmount < 50000) {
            // 5万以下：8-15像素，提升小金额的可见度
            bubbleRadius = 8 + (salesAmount / 50000) * 7;
        } else if (salesAmount < 200000) {
            // 5-20万：15-25像素，中等金额
            bubbleRadius = 15 + ((salesAmount - 50000) / 150000) * 10;
        } else if (salesAmount < 500000) {
            // 20-50万：25-35像素，较大金额
            bubbleRadius = 25 + ((salesAmount - 200000) / 300000) * 10;
        } else if (salesAmount < 1000000) {
            // 50-100万：35-45像素，大金额
            bubbleRadius = 35 + ((salesAmount - 500000) / 500000) * 10;
        } else {
            // 100万以上：45-60像素，超大金额
            bubbleRadius = 45 + Math.min(15, ((salesAmount - 1000000) / 1000000) * 15);
        }
        bubbleRadius = Math.max(8, Math.min(60, bubbleRadius));

        // 不进行显示限制，直接使用原始值
        const bubblePoint = {
            x: achievementRate,
            y: growthRate,
            r: bubbleRadius,
            label: productName,
            sales: product.actual || 0,
            achievement: achievementRate,
            growth: growthRate
        };
        
        console.log(`✨ 气泡点完成 - ${productName}:`, bubblePoint);
        return bubblePoint;
    });
    
    console.log('✅ 产品气泡图数据准备完成，最终数据点数量:', result.length);
    console.log('最终气泡数据:', result);
    return result;
}

// 准备模态框医院气泡图数据
function prepareModalHospitalBubbleData(hospitals) {
    // 先过滤掉销售金额为0的医院
    const validHospitals = hospitals.filter(hospital => (hospital.actual || 0) > 0);

    // 计算总销售额用于占比计算（只计算有效医院）
    const totalSales = validHospitals.reduce((sum, hospital) => sum + (hospital.actual || 0), 0);

    return validHospitals.map(hospital => {
        const salesShare = totalSales > 0 ? (hospital.actual / totalSales * 100) : 0;

        // 计算增长率
        let growthRate = 0;
        if (hospital.lastYearSales > 0) {
            growthRate = ((hospital.actual - hospital.lastYearSales) / hospital.lastYearSales * 100);
        } else {
            // 如果没有去年数据，使用随机的增长率来避免所有气泡都在一条线上
            // 基于医院名称生成一个相对稳定的随机增长率（-20% 到 +20%）
            const hash = hospital.name.split('').reduce((a, b) => {
                a = ((a << 5) - a) + b.charCodeAt(0);
                return a & a;
            }, 0);
            growthRate = (Math.abs(hash) % 41) - 20; // -20 到 +20 的范围
        }

        console.log(`医院 ${hospital.name}: 实际销售=${hospital.actual}, 去年销售=${hospital.lastYearSales}, 增长率=${growthRate.toFixed(2)}%`);

        // 气泡大小计算 - 优化算法，增强视觉区分度
        const salesAmount = hospital.actual || 0;
        let bubbleRadius;
        if (salesAmount === 0) {
            bubbleRadius = 8; // 销售金额为0的医院使用最小气泡
        } else if (salesAmount < 30000) {
            // 3万以下：8-15像素，提升小金额的可见度
            bubbleRadius = 8 + (salesAmount / 30000) * 7;
        } else if (salesAmount < 100000) {
            // 3-10万：15-25像素，中等金额
            bubbleRadius = 15 + ((salesAmount - 30000) / 70000) * 10;
        } else if (salesAmount < 300000) {
            // 10-30万：25-35像素，较大金额
            bubbleRadius = 25 + ((salesAmount - 100000) / 200000) * 10;
        } else if (salesAmount < 800000) {
            // 30-80万：35-45像素，大金额
            bubbleRadius = 35 + ((salesAmount - 300000) / 500000) * 10;
        } else {
            // 80万以上：45-60像素，超大金额
            bubbleRadius = 45 + Math.min(15, ((salesAmount - 800000) / 800000) * 15);
        }
        bubbleRadius = Math.max(8, Math.min(60, bubbleRadius));

        return {
            x: salesShare,
            y: growthRate,
            r: bubbleRadius,
            label: hospital.name,
            sales: hospital.actual || 0,
            share: salesShare,
            growth: growthRate
        };
    });
}

// 获取模态框气泡图配置选项
function getModalBubbleChartOptions(title, type, allowDrillDown = true, bubbleData = []) {
    const xAxisTitle = type === 'hospital' ? '销售占比 (%)' : '达成率 (%)';

    // 为医院占比图添加终端视图标识
    let chartTitle;
    if (type === 'hospital' && !allowDrillDown) {
        chartTitle = `${title} - 终端视图`;
    } else {
        chartTitle = `${title} - 双击气泡进行钻取`;
    }

    // 根据数据动态计算坐标轴范围
    let xMin = 0, xMax = 100, yMin = -50, yMax = 50;

    if (bubbleData && bubbleData.length > 0) {
        // 计算气泡的最大半径（用于确保整个气泡都在可见范围内）
        const maxBubbleRadius = Math.max(...bubbleData.map(d => d.r || 0));

        // 计算X轴范围（达成率或销售占比）
        const xValues = bubbleData.map(d => d.x).filter(x => x !== undefined && x !== null);
        if (xValues.length > 0) {
            const dataXMin = Math.min(...xValues);
            const dataXMax = Math.max(...xValues);
            const xRange = dataXMax - dataXMin;

            // 基础边距：数据范围的10%，最少10个单位
            const basePadding = Math.max(xRange * 0.1, 10);

            // 气泡半径对应的坐标轴单位（估算）
            // 增加转换比例确保气泡完全可见，假设图表宽度约400px
            const bubbleRadiusInAxisUnits = maxBubbleRadius * (xRange > 0 ? xRange / 150 : 2);

            // 总边距 = 基础边距 + 气泡半径边距 + 额外安全边距
            const xPadding = basePadding + bubbleRadiusInAxisUnits + Math.max(maxBubbleRadius * 0.5, 20);

            xMin = Math.max(0, dataXMin - xPadding);
            xMax = dataXMax + xPadding;

            // 确保最小范围
            if (xMax - xMin < 50) {
                const center = (xMax + xMin) / 2;
                xMin = Math.max(0, center - 25);
                xMax = center + 25;
            }
        }

        // 计算Y轴范围（增长率）
        const yValues = bubbleData.map(d => d.y).filter(y => y !== undefined && y !== null);
        if (yValues.length > 0) {
            const dataYMin = Math.min(...yValues);
            const dataYMax = Math.max(...yValues);
            const yRange = dataYMax - dataYMin;

            // 基础边距：数据范围的10%，最少10个单位
            const basePadding = Math.max(yRange * 0.1, 10);

            // 气泡半径对应的坐标轴单位（估算）
            // 增加转换比例确保气泡完全可见，假设图表高度约300px
            const bubbleRadiusInAxisUnits = maxBubbleRadius * (yRange > 0 ? yRange / 120 : 2);

            // 总边距 = 基础边距 + 气泡半径边距 + 额外安全边距
            const yPadding = basePadding + bubbleRadiusInAxisUnits + Math.max(maxBubbleRadius * 0.5, 20);

            yMin = dataYMin - yPadding;
            yMax = dataYMax + yPadding;

            // 确保最小范围
            if (yMax - yMin < 50) {
                const center = (yMax + yMin) / 2;
                yMin = center - 25;
                yMax = center + 25;
            }
        }

        console.log(`气泡半径考虑 - 最大气泡半径: ${maxBubbleRadius}px`);
    }

    console.log(`动态计算坐标轴范围 - X轴: [${xMin.toFixed(1)}, ${xMax.toFixed(1)}], Y轴: [${yMin.toFixed(1)}, ${yMax.toFixed(1)}]`);

    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            title: {
                display: true,
                text: chartTitle,
                font: {
                    size: 16,
                    weight: 'bold'
                },
                color: '#1f2937',
                padding: {
                    bottom: 20
                }
            },
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#ddd',
                borderWidth: 1,
                cornerRadius: 6,
                displayColors: false,
                callbacks: {
                    title: function(context) {
                        return context[0].raw.label;
                    },
                    label: function(context) {
                        const data = context.raw;
                        const labels = [
                            `销售金额: ¥${(data.sales / 10000).toFixed(1)}万`
                        ];

                        if (type === 'hospital') {
                            labels.push(`销售占比: ${data.share.toFixed(1)}%`);
                        } else {
                            // 显示达成率，如果被限制则显示真实值
                            const achievementLabel = `达成率: ${data.achievement.toFixed(1)}%`;
                            if (data.displayAchievement && Math.abs(data.achievement - data.displayAchievement) > 0.1) {
                                labels.push(achievementLabel + ` (图表显示: ${data.displayAchievement.toFixed(1)}%)`);
                            } else {
                                labels.push(achievementLabel);
                            }
                        }

                        // 显示增长率，如果被限制则显示真实值
                        const growthLabel = `增长率: ${data.growth >= 0 ? '+' : ''}${data.growth.toFixed(1)}%`;
                        if (data.displayGrowth && Math.abs(data.growth - data.displayGrowth) > 0.1) {
                            labels.push(growthLabel + ` (图表显示: ${data.displayGrowth >= 0 ? '+' : ''}${data.displayGrowth.toFixed(1)}%)`);
                        } else {
                            labels.push(growthLabel);
                        }

                        return labels;
                    }
                }
            }
        },
        scales: {
            x: {
                type: 'linear',
                position: 'bottom',
                title: {
                    display: true,
                    text: xAxisTitle,
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    display: true, // 显示X轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        return value.toFixed(0) + '%';
                    }
                },
                // 根据数据动态设置X轴范围
                min: xMin,
                max: xMax
            },
            y: {
                title: {
                    display: true,
                    text: '增长率 (%)',
                    font: {
                        size: 14,
                        weight: 'bold'
                    },
                    color: '#1f2937'
                },
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                },
                ticks: {
                    display: true, // 显示Y轴刻度标签
                    color: '#6b7280',
                    callback: function(value) {
                        return (value >= 0 ? '+' : '') + value.toFixed(0) + '%';
                    }
                },
                // 根据数据动态设置Y轴范围
                min: yMin,
                max: yMax
            }
        },
        layout: {
            padding: {
                top: 80,  // 增加顶部边距确保标签完全可见
                right: 140, // 增加右侧边距确保标签完全可见
                bottom: 120, // 增加底部空间防止文字被遮挡和气泡被截断
                left: 80   // 增加左侧边距确保标签完全可见
            }
        },
        // 禁用裁剪确保气泡完全可见
        clip: false,
        // 图表区域设置
        chartArea: {
            backgroundColor: 'transparent'
        },
        // 确保气泡元素不会被裁剪
        elements: {
            point: {
                clip: false  // 禁用点的裁剪
            }
        }
    };
}

// 显示80%销量医院列表
async function showTop80HospitalsList(filterName, filterType = 'personnel', additionalPersonnel = '') {
    try {
        console.log('显示80%销量医院列表:', filterName, '筛选类型:', filterType, '额外人员:', additionalPersonnel);

        // 获取当前筛选条件
        const filters = getSelectedFilters();

        // 构建请求数据
        const requestData = {
            ...filters,
            percentage: 80
        };

        // 根据筛选类型添加相应的筛选条件
        if (filterName) {
            if (filterType === 'personnel') {
                requestData.reps = [filterName];
            } else if (filterType === 'product') {
                requestData.products = [filterName];
                // 如果有额外的人员信息，也添加到筛选条件中
                if (additionalPersonnel && additionalPersonnel.trim() !== '') {
                    requestData.reps = [additionalPersonnel];
                }
            } else if (filterType === 'hospital') {
                requestData.terminals = [filterName];
            }
        }

        console.log('医院统计请求数据:', requestData);

        const response = await fetch('/api/hospital-sales-stats', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.data.hospitals) {
            // 构建标题
            let title = '';
            if (filterType === 'personnel') {
                title = `${filterName} - 80%销量医院名单`;
            } else if (filterType === 'product') {
                if (additionalPersonnel && additionalPersonnel.trim() !== '') {
                    title = `${additionalPersonnel} - ${filterName} - 80%销量医院名单`;
                } else {
                    title = `${filterName} - 80%销量医院名单`;
                }
            } else if (filterType === 'hospital') {
                title = `${filterName} - 80%销量医院名单`;
            } else {
                title = '80%销量医院名单';
            }

            displayHospitalsList(data.data.hospitals, title, data.data);
        } else {
            console.error('获取80%销量医院列表失败:', data.error);
            showModalError('获取医院列表失败，请稍后重试');
        }
    } catch (error) {
        console.error('加载80%销量医院列表失败:', error);
        showModalError('网络错误，请稍后重试');
    }
}

// 显示医院列表
function displayHospitalsList(hospitals, title, statsData) {
    // 使用API返回的统计数据
    const totalSales = statsData.totalSales || 0;
    const percentage = statsData.percentage || 80;
    const totalHospitalCount = statsData.totalHospitalCount || 0;
    const selectedHospitalCount = statsData.selectedHospitalCount || 0;

    // 医院数据已经在API中处理过了，直接使用
    const top80Hospitals = hospitals;

    // 创建医院列表弹窗
    const modalHTML = `
        <div id="hospitalListModal" class="bubble-modal-overlay">
            <div class="bubble-modal-content hospital-list-modal">
                <div class="bubble-modal-header">
                    <h3>${title}</h3>
                    <button class="bubble-modal-close" onclick="closeHospitalListModal()">&times;</button>
                </div>
                <div class="bubble-modal-body">
                    <div class="hospital-list-summary">
                        <p>共 <strong>${selectedHospitalCount}</strong> 家医院（总计${totalHospitalCount}家），占总销售额的 <strong>${percentage}%</strong></p>
                        <p>总销售额: <strong>¥${(totalSales / 10000).toFixed(1)}万</strong></p>
                    </div>
                    <div class="hospital-list-container">
                        <table class="hospital-list-table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>医院名称</th>
                                    <th>销售金额</th>
                                    <th>占比</th>
                                    <th>累计占比</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${top80Hospitals.map((hospital) => `
                                    <tr>
                                        <td>${hospital.rank || '-'}</td>
                                        <td class="hospital-name">${hospital.name || '未知医院'}</td>
                                        <td class="sales-amount">¥${((hospital.actual || 0) / 10000).toFixed(1)}万</td>
                                        <td class="percentage">${hospital.percentage || '0.00'}%</td>
                                        <td class="cumulative-percentage">${hospital.cumulativePercentage || '0.00'}%</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="showHospitalProductBubbleModal('${hospital.name}')">
                                                查看产品
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    <div class="bubble-modal-actions">
                        <button class="btn btn-secondary" onclick="closeHospitalListModal()">关闭</button>
                        <button class="btn btn-primary" onclick="exportHospitalsList()">导出列表</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// 关闭医院列表弹窗
function closeHospitalListModal() {
    const modal = document.getElementById('hospitalListModal');
    if (modal) {
        modal.remove();
    }
}

// 显示模态框错误信息
function showModalError(message) {
    const chartContainer = document.querySelector('.bubble-chart-container');
    if (chartContainer) {
        chartContainer.innerHTML = `
            <div class="modal-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${message}</div>
                <button class="btn btn-primary" onclick="location.reload()">刷新页面</button>
            </div>
        `;
    }
}

// 导出医院列表（占位函数）
function exportHospitalsList() {
    console.log('导出医院列表功能待实现');
    alert('导出功能正在开发中...');
}









// 双层饼图相关函数
let doublePieChart = null;

// 全局格式化函数
function formatCurrency(amount) {
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(amount || 0);
}

// 医院对比表格专用格式化函数（去除¥符号）
function formatCurrencyWithoutSymbol(amount) {
    return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(amount || 0);
}

// 计算医院汇总数据
function calculateHospitalSummary(hospitalGroups, products) {
    const summary = {};
    
    products.forEach(productName => {
        summary[productName] = {
            lastYearPeriod: { salesAmount: 0, salesQuantity: 0 },
            currentPeriod: { salesAmount: 0, salesQuantity: 0 }
        };
    });
    
    Object.keys(hospitalGroups).forEach(hospitalName => {
        const hospitalProducts = hospitalGroups[hospitalName];
        
        products.forEach(productName => {
            const productData = hospitalProducts[productName];
            if (productData) {
                const lastYearData = productData.lastYearPeriod || { salesAmount: 0, salesQuantity: 0 };
                const currentData = productData.currentPeriod || { salesAmount: 0, salesQuantity: 0 };
                
                summary[productName].lastYearPeriod.salesAmount += lastYearData.salesAmount || 0;
                summary[productName].lastYearPeriod.salesQuantity += lastYearData.salesQuantity || 0;
                summary[productName].currentPeriod.salesAmount += currentData.salesAmount || 0;
                summary[productName].currentPeriod.salesQuantity += currentData.salesQuantity || 0;
            }
        });
    });
    
    return summary;
}

// 添加总汇总数据列
function addTotalSummaryColumns(row, summaryData, allProducts) {
    // 计算所有产品的总汇总数据
    let totalLastYearAmount = 0;
    let totalLastYearQuantity = 0;
    let totalCurrentAmount = 0;
    let totalCurrentQuantity = 0;

    allProducts.forEach(productName => {
        const productSummary = summaryData[productName];
        if (productSummary) {
            const lastYearData = productSummary.lastYearPeriod;
            const currentData = productSummary.currentPeriod;

            totalLastYearAmount += lastYearData.salesAmount || 0;
            totalLastYearQuantity += lastYearData.salesQuantity || 0;
            totalCurrentAmount += currentData.salesAmount || 0;
            totalCurrentQuantity += currentData.salesQuantity || 0;
        }
    });

    // 同期销售额
    const lastYearAmountCell = document.createElement('td');
    lastYearAmountCell.textContent = formatCurrencyWithoutSymbol(totalLastYearAmount);
    lastYearAmountCell.className = 'sales-amount';
    lastYearAmountCell.style.fontWeight = 'bold';
    row.appendChild(lastYearAmountCell);

    // 同期销量
    const lastYearQuantityCell = document.createElement('td');
    lastYearQuantityCell.textContent = formatNumber(totalLastYearQuantity);
    lastYearQuantityCell.className = 'sales-quantity';
    lastYearQuantityCell.style.fontWeight = 'bold';
    row.appendChild(lastYearQuantityCell);

    // 本期销售额
    const currentAmountCell = document.createElement('td');
    currentAmountCell.textContent = formatCurrencyWithoutSymbol(totalCurrentAmount);
    currentAmountCell.className = 'sales-amount';
    currentAmountCell.style.fontWeight = 'bold';
    row.appendChild(currentAmountCell);

    // 本期销量
    const currentQuantityCell = document.createElement('td');
    currentQuantityCell.textContent = formatNumber(totalCurrentQuantity);
    currentQuantityCell.className = 'sales-quantity';
    currentQuantityCell.style.fontWeight = 'bold';
    row.appendChild(currentQuantityCell);

    // GRV (Growth Rate Value) - 销售额增长差额
    const grvAmount = totalCurrentAmount - totalLastYearAmount;

    const grvAmountCell = document.createElement('td');
    grvAmountCell.textContent = formatCurrencyWithoutSymbol(grvAmount);
    grvAmountCell.className = 'grv-value';
    grvAmountCell.style.fontWeight = 'bold';
    if (grvAmount < 0) {
        grvAmountCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(grvAmountCell);

    // GR (Growth Rate) - 修正算法：GR = GRV / 同期销售额
    let grPercentage = 0;
    if (totalLastYearAmount > 0) {
        grPercentage = (grvAmount / totalLastYearAmount) * 100;
    } else if (grvAmount > 0) {
        grPercentage = 100; // 同期为0，本期有销售，显示100%
    }

    const grPercentageCell = document.createElement('td');
    grPercentageCell.textContent = formatGrowth(grPercentage);
    grPercentageCell.className = `gr-value ${getGrowthClass(grPercentage)}`;
    grPercentageCell.style.fontWeight = 'bold';
    if (grPercentage < 0) {
        grPercentageCell.style.color = '#ef4444'; // 负数保持红色
    }
    row.appendChild(grPercentageCell);
}

// 创建医院汇总行
function createHospitalSummaryRow(summaryData, allProducts) {
    const row = document.createElement('tr');
    row.style.backgroundColor = '#f8f9fa';
    row.style.color = '#6b7280';
    row.style.fontWeight = 'bold';
    
    // 医院名称列（显示"汇总"）
    const summaryCell = document.createElement('td');
    summaryCell.textContent = '汇总';
    summaryCell.className = 'hospital-name-cell';
    summaryCell.style.color = '#6b7280';
    row.appendChild(summaryCell);

    // 添加总汇总列
    addTotalSummaryColumns(row, summaryData, allProducts);

    // 为每个产品添加汇总数据列
    allProducts.forEach(productName => {
        const productSummary = summaryData[productName];
        
        if (!productSummary) {
            // 如果没有这个产品的数据，填充空单元格
            for (let i = 0; i < 6; i++) {
                const emptyCell = document.createElement('td');
                emptyCell.textContent = '-';
                emptyCell.className = 'no-data';
                emptyCell.style.color = '#6b7280';
                row.appendChild(emptyCell);
            }
        } else {
            const lastYearData = productSummary.lastYearPeriod;
            const currentData = productSummary.currentPeriod;
            
            // 同期销售额
            const lastYearAmountCell = document.createElement('td');
            lastYearAmountCell.textContent = formatCurrencyWithoutSymbol(lastYearData.salesAmount);
            lastYearAmountCell.className = 'sales-amount';
            row.appendChild(lastYearAmountCell);

            // 同期销量
            const lastYearQuantityCell = document.createElement('td');
            lastYearQuantityCell.textContent = formatNumber(lastYearData.salesQuantity);
            lastYearQuantityCell.className = 'sales-quantity';
            row.appendChild(lastYearQuantityCell);

            // 本期销售额
            const currentAmountCell = document.createElement('td');
            currentAmountCell.textContent = formatCurrencyWithoutSymbol(currentData.salesAmount);
            currentAmountCell.className = 'sales-amount';
            row.appendChild(currentAmountCell);

            // 本期销量
            const currentQuantityCell = document.createElement('td');
            currentQuantityCell.textContent = formatNumber(currentData.salesQuantity);
            currentQuantityCell.className = 'sales-quantity';
            row.appendChild(currentQuantityCell);

            // GRV (Growth Rate Value) - 销售额增长差额
            const grvAmount = currentData.salesAmount - lastYearData.salesAmount;

            const grvAmountCell = document.createElement('td');
            grvAmountCell.textContent = formatCurrencyWithoutSymbol(grvAmount);
            grvAmountCell.className = 'grv-value';
            row.appendChild(grvAmountCell);

            // GR (Growth Rate) - 修正算法：GR = GRV / 同期销售额
            let grPercentage = 0;
            if (lastYearData.salesAmount > 0) {
                grPercentage = (grvAmount / lastYearData.salesAmount) * 100;
            } else if (grvAmount > 0) {
                grPercentage = 100; // 同期为0，本期有销售，显示100%
            }

            const grPercentageCell = document.createElement('td');
            grPercentageCell.textContent = formatGrowth(grPercentage);
            grPercentageCell.className = 'gr-value';
            row.appendChild(grPercentageCell);
        }
    });
    
    return row;
}

// 创建销售员占比双层环形图 - 内圈销售额，外圈指标完成率
function createDoublePieChart(data) {
    console.log('🔵 dashboard.js createDoublePieChart 被调用 🔵');
    console.log('=== 创建销售员占比双层环形图 ===');
    console.log('输入数据:', data);

    const canvas = document.getElementById('doublePieChart');
    if (!canvas) {
        console.error('找不到饼图canvas元素');
        return;
    }

    const ctx = canvas.getContext('2d');

    // 销毁现有图表
    if (doublePieChart) {
        doublePieChart.destroy();
    }

    // 使用HTML中的固定尺寸，与其他环形图表保持一致
    console.log('🔵 销售员图表使用HTML固定尺寸');

    // 准备双层环形图数据
    const chartData = prepareSalesRepDoubleData(data);
    console.log('处理后的销售员双层图表数据:', chartData);

    // 创建双层环形图
    doublePieChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [
                {
                    label: '销售额',
                    data: chartData.salesData,
                    backgroundColor: chartData.salesColors,
                    borderWidth: 6,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 8,
                    hoverBorderColor: '#333333',
                    cutout: '0%',  // 内圈无空心 - 实心饼图
                    radius: '50%'  // 内圈半径
                },
                {
                    label: '指标完成率',
                    data: chartData.targetData,
                    backgroundColor: chartData.targetColors,
                    borderWidth: 6,
                    borderColor: '#ffffff',
                    hoverBorderWidth: 8,
                    hoverBorderColor: '#333333',
                    cutout: '52%', // 外圈空心 - 紧贴内圈（内圈50% + 2%间隙）
                    radius: '90%'  // 外圈半径 - 粗环形
                }
            ]
        },
        options: {
            responsive: true, // 启用响应式
            maintainAspectRatio: true, // 保持宽高比
            aspectRatio: 1, // 1:1 宽高比，确保圆形
            layout: {
                padding: {
                    top: 10,
                    bottom: 10,
                    left: 10,
                    right: 10
                }
            },
            plugins: {
                legend: {
                    display: false // 使用自定义图例
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label;
                            const value = context.parsed;
                            const datasetLabel = context.dataset.label;

                            if (datasetLabel === '销售额') {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);

                                // 格式化金额显示
                                const formatAmount = (amount) => {
                                    if (amount >= 10000) {
                                        return (amount / 10000).toFixed(1) + '万';
                                    }
                                    return new Intl.NumberFormat('zh-CN').format(Math.round(amount));
                                };

                                return `${label} 销售额: ¥${formatAmount(value)} (${percentage}%)`;
                            } else {
                                return `${label} 指标完成率: ${value.toFixed(1)}%`;
                            }
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                duration: 1000
            }
        }
    });

    // 创建销售员图例
    createSalesRepLegendCards(chartData);

    // 更新数据信息（不包括中心文字）
    updateSalesRepDataInfo(chartData);
}

// 更新销售员业绩占比信息显示
function updateSalesRepInfo(chartData) {
    // 更新中心文字
    const centerTextElement = document.getElementById('salesRepCenterText');
    if (centerTextElement) {
        const ratioElement = centerTextElement.querySelector('.comparison-ratio');
        const labelElement = centerTextElement.querySelector('.comparison-label');

        if (chartData.salesData && chartData.salesData.length > 0) {
            // 计算平均完成率
            const avgCompletionRate = chartData.targetData.reduce((sum, rate) => sum + rate, 0) / chartData.targetData.length;
            if (ratioElement) ratioElement.textContent = avgCompletionRate.toFixed(1) + '%';
            if (labelElement) labelElement.textContent = '平均完成率';
        } else {
            if (ratioElement) ratioElement.textContent = '0%';
            if (labelElement) labelElement.textContent = '业绩占比';
        }
    }

    // 更新数据信息
    const salesRepCountElement = document.getElementById('salesRepCount');
    const totalSalesElement = document.getElementById('salesRepTotalSales');

    if (salesRepCountElement) {
        salesRepCountElement.textContent = chartData.labels ? chartData.labels.length : 0;
    }

    if (totalSalesElement) {
        const totalSales = chartData.salesTotal || 0;
        const formatAmount = (amount) => {
            if (amount >= 10000) {
                return '¥' + (amount / 10000).toFixed(1) + '万';
            }
            return '¥' + new Intl.NumberFormat('zh-CN').format(Math.round(amount));
        };
        totalSalesElement.textContent = formatAmount(totalSales);
    }
}

// 创建销售员图例卡片
function createSalesRepLegendCards(chartData) {
    const legendContainer = document.getElementById('salesRepLegendCards');
    if (!legendContainer) {
        console.error('销售员图例容器未找到: salesRepLegendCards');
        return;
    }

    console.log('创建销售员图例，数据:', chartData);

    // 清空容器
    legendContainer.innerHTML = '';

    // 确保容器可见
    legendContainer.style.display = 'flex';
    legendContainer.style.visibility = 'visible';
    legendContainer.style.opacity = '1';

    // 格式化金额显示
    const formatAmount = (amount) => {
        if (amount >= 10000) {
            return '¥' + (amount / 10000).toFixed(1) + '万';
        }
        return '¥' + new Intl.NumberFormat('zh-CN').format(Math.round(amount));
    };

    // 生成图例项
    if (chartData.labels && chartData.salesData && chartData.salesColors) {
        chartData.labels.forEach((name, index) => {
            const card = document.createElement('div');
            card.className = 'sales-rep-legend-card';
            card.style.borderLeftColor = chartData.salesColors[index];

            const salesAmount = chartData.salesData[index] || 0;
            const totalSales = chartData.salesData.reduce((sum, value) => sum + value, 0);
            const percentage = totalSales > 0 ? ((salesAmount / totalSales) * 100).toFixed(1) : '0.0';

            card.innerHTML = `
                <div class="sales-rep-color" style="background-color: ${chartData.salesColors[index]}"></div>
                <div class="sales-rep-info">
                    <div class="sales-rep-name">${name}</div>
                    <div class="sales-rep-details">
                        <span class="sales-rep-amount">${formatAmount(salesAmount)}</span>
                        <span class="sales-rep-percentage">${percentage}%</span>
                    </div>
                </div>
            `;

            legendContainer.appendChild(card);
        });
    }
}

// 更新销售员数据信息（不包括中心文字）
function updateSalesRepDataInfo(chartData) {
    // 更新数据信息
    const salesRepCountElement = document.getElementById('salesRepCount');
    const totalSalesElement = document.getElementById('salesRepTotalSales');

    if (salesRepCountElement) {
        salesRepCountElement.textContent = chartData.labels ? chartData.labels.length : 0;
    }

    if (totalSalesElement) {
        const totalSales = chartData.salesTotal || 0;
        const formatAmount = (amount) => {
            if (amount >= 10000) {
                return '¥' + (amount / 10000).toFixed(1) + '万';
            }
            return '¥' + new Intl.NumberFormat('zh-CN').format(Math.round(amount));
        };
        totalSalesElement.textContent = formatAmount(totalSales);
    }
}

// 准备销售员占比双层环形图数据
function prepareSalesRepDoubleData(data) {
    console.log('准备销售员双层环形图数据:', data);

    if (!data || data.length === 0) {
        return {
            labels: [],
            salesData: [],
            targetData: [],
            targetAmounts: [],
            salesColors: [],
            targetColors: [],
            salesTotal: 0
        };
    }

    // 计算总实际销售额
    const totalActual = data.reduce((sum, item) => sum + (item.actual || 0), 0);
    console.log('销售员总销售额:', totalActual);

    if (totalActual === 0) {
        console.log('销售员总销售额为0，返回空数据');
        return {
            labels: [],
            salesData: [],
            targetData: [],
            targetAmounts: [],
            salesColors: [],
            targetColors: [],
            salesTotal: 0
        };
    }

    // 过滤掉销售额为0的销售员，并按销售额排序
    const validData = data
        .filter(item => (item.actual || 0) > 0)
        .sort((a, b) => (b.actual || 0) - (a.actual || 0));

    console.log('有效销售员数据:', validData);

    const labels = validData.map(item => item.name || '未知销售员');
    const salesData = validData.map(item => item.actual || 0);
    const targetAmounts = validData.map(item => item.target || 0); // 指标金额
    const targetData = validData.map(item => {
        // 计算指标完成率
        const completionRate = (item.target && item.target > 0)
            ? Math.min(((item.actual || 0) / item.target) * 100, 200) // 最高显示200%
            : 0;
        return completionRate;
    });

    // 生成颜色
    const baseColors = generateChartColors(validData.length);
    const salesColors = baseColors;
    const targetColors = baseColors.map((color, index) => {
        // 外圈颜色根据完成率调整透明度
        const completionRate = targetData[index];
        const alpha = completionRate >= 100 ? 0.8 : 0.5;
        // 将hex颜色转换为rgba
        const hex = color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    });

    return {
        labels,
        salesData,
        targetData,
        targetAmounts,
        salesColors,
        targetColors,
        salesTotal: totalActual
    };
}

// 准备销售员占比饼图数据 - 简化版本
function prepareSalesRepPieData(data) {
    console.log('准备销售员饼图数据:', data);

    if (!data || data.length === 0) {
        return {
            labels: [],
            actualData: [],
            colors: []
        };
    }

    // 计算总实际销售额 - 修复字段名
    const totalActual = data.reduce((sum, item) => sum + (item.actual || 0), 0);
    console.log('销售员总销售额:', totalActual);

    if (totalActual === 0) {
        console.log('销售员总销售额为0，返回空数据');
        return {
            labels: [],
            actualData: [],
            colors: []
        };
    }

    // 过滤掉销售额为0的销售员，并按销售额排序 - 修复字段名
    const validData = data
        .filter(item => (item.actual || 0) > 0)
        .sort((a, b) => (b.actual || 0) - (a.actual || 0));

    console.log('有效销售员数据:', validData);

    const labels = validData.map(item => item.name || '未知销售员');
    const actualData = validData.map(item => item.actual || 0);

    // 生成颜色
    const colors = generateChartColors(validData.length);

    return {
        labels,
        actualData,
        colors,
        totalActual
    };
}

// 创建销售员占比双层图例 - 使用新的独立样式
function createSalesRepDoubleLegend(chartData) {
    const legendContainer = document.getElementById('doublePieChartLegend');
    if (!legendContainer) {
        console.error('图例容器未找到: doublePieChartLegend');
        return;
    }

    console.log('🔵 创建销售员双层图例，数据:', chartData);
    console.log('🔵 图例容器:', legendContainer);
    console.log('🔵 标签数量:', chartData.labels ? chartData.labels.length : 0);

    // 使用新的销售员图例样式类
    legendContainer.className = 'sales-rep-legend';

    // 确保容器可见
    legendContainer.style.display = 'flex';
    legendContainer.style.visibility = 'visible';
    legendContainer.style.opacity = '1';

    let legendHtml = '';

    if (!chartData.labels || chartData.labels.length === 0) {
        console.log('🔵 没有标签数据，显示空状态');
        legendContainer.innerHTML = '<div class="legend-item">暂无数据</div>';
        return;
    }

    chartData.labels.forEach((label, index) => {
        const salesAmount = chartData.salesData[index];
        const completionRate = chartData.targetData[index];
        const targetAmount = chartData.targetAmounts[index]; // 获取指标金额
        const salesColor = chartData.salesColors[index];

        // 格式化金额显示
        const formatAmount = (amount) => {
            if (amount >= 10000) {
                return (amount / 10000).toFixed(1) + '万';
            }
            return new Intl.NumberFormat('zh-CN').format(Math.round(amount));
        };

        // 计算销售额占比
        const salesPercentage = ((salesAmount / chartData.salesTotal) * 100).toFixed(1);

        legendHtml += `
            <div class="legend-item">
                <div class="legend-color" style="background-color: ${salesColor}"></div>
                <span class="legend-label">${label}</span>
                <span class="legend-value">销售额: ¥${formatAmount(salesAmount)} | 指标: ¥${formatAmount(targetAmount)} (${completionRate.toFixed(1)}%)</span>
                <span class="legend-percentage">${salesPercentage}%</span>
            </div>
        `;
    });

    legendContainer.innerHTML = legendHtml;
    console.log('🔵 双层图例生成完成，HTML长度:', legendHtml.length);
    console.log('🔵 图例容器内容:', legendContainer.innerHTML.substring(0, 200));
}

// 创建销售员占比图例 - 简化版本，与产品图例保持一致
function createSalesRepLegend(chartData) {
    const legendContainer = document.getElementById('doublePieChartLegend');
    if (!legendContainer) {
        console.error('图例容器未找到: doublePieChartLegend');
        return;
    }

    console.log('创建销售员图例，数据:', chartData);

    // 为卡片图例添加样式类
    legendContainer.className = 'chart-legend-cards';

    // 确保容器可见
    legendContainer.style.display = 'flex';
    legendContainer.style.visibility = 'visible';
    legendContainer.style.opacity = '1';

    // 计算总销售额用于百分比计算
    const totalActual = chartData.actualData.reduce((sum, value) => sum + value, 0);

    // 格式化金额显示（与产品图例保持一致）
    const formatAmount = (amount) => {
        if (amount >= 10000) {
            return (amount / 10000).toFixed(1) + '万';
        }
        return new Intl.NumberFormat('zh-CN').format(Math.round(amount));
    };

    const legendHTML = chartData.labels.map((label, index) => {
        const actualValue = chartData.actualData[index];
        const actualPercentage = chartData.totalActual > 0 ? ((actualValue / chartData.totalActual) * 100).toFixed(1) : '0.0';
        const color = chartData.colors[index];

        return `
            <div class="legend-item" style="display: flex !important; align-items: center !important; gap: 8px !important; padding: 8px 10px !important; background: #ffffff !important; border: 1px solid #e5e7eb !important; border-radius: 4px; margin-bottom: 4px; min-height: 32px !important; visibility: visible !important; opacity: 1 !important;">
                <div class="legend-color" style="width: 12px; height: 12px; border-radius: 2px; flex-shrink: 0; background-color: ${color};"></div>
                <div class="legend-label" title="${label}" style="font-size: 12px !important; font-weight: 600 !important; color: #374151 !important; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; flex-shrink: 1; min-width: 50px; line-height: 1.4; display: block !important; visibility: visible !important; opacity: 1 !important;">${label}</div>
                <div class="legend-value" style="font-size: 10px !important; color: #6b7280 !important; font-weight: 500 !important; white-space: nowrap; flex: 1; text-align: left; overflow: hidden; text-overflow: ellipsis; min-width: 0; line-height: 1.4; display: block !important; visibility: visible !important; opacity: 1 !important;">销售额: ¥${formatAmount(actualValue)}</div>
                <div class="legend-percentage" style="font-size: 10px !important; color: #059669 !important; font-weight: 600 !important; background: #ecfdf5 !important; padding: 2px 6px !important; border-radius: 3px; white-space: nowrap; flex-shrink: 0; line-height: 1.4; display: block !important; visibility: visible !important; opacity: 1 !important;">${actualPercentage}%</div>
            </div>
        `;
    }).join('');

    console.log('生成的图例HTML长度:', legendHTML.length);
    console.log('图例数据标签:', chartData.labels);
    legendContainer.innerHTML = legendHTML;

    // 验证图例是否正确生成
    setTimeout(() => {
        const legendItems = legendContainer.querySelectorAll('.legend-item');
        console.log('图例项数量:', legendItems.length);
        if (legendItems.length > 0) {
            console.log('图例生成成功，文字应该可见');
        } else {
            console.error('图例生成失败');
        }
    }, 100);
}

// 绘制销售员占比图表的引导线标签
function drawSalesRepLabelsWithLines(chart, chartData) {
    const ctx = chart.ctx;
    const chartArea = chart.chartArea;
    const centerX = (chartArea.left + chartArea.right) / 2;
    const centerY = (chartArea.top + chartArea.bottom) / 2;
    const radius = Math.min(chartArea.right - chartArea.left, chartArea.bottom - chartArea.top) / 2 * 0.6;

    ctx.save();
    ctx.font = '12px Arial';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';

    let currentAngle = -Math.PI / 2; // 从顶部开始

    // 计算总实际销售额用于百分比计算
    const totalActual = chartData.actualData.reduce((sum, value) => sum + value, 0);

    // 预计算所有标签位置
    const labelPositions = [];
    chartData.labels.forEach((label, index) => {
        const actualValue = chartData.actualData[index];
        const percentage = totalActual > 0 ? ((actualValue / totalActual) * 100).toFixed(1) : '0.0';
        const sliceAngle = totalActual > 0 ? (actualValue / totalActual) * 2 * Math.PI : 0;

        if (sliceAngle === 0) return; // 跳过空数据

        const midAngle = currentAngle + sliceAngle / 2;
        const isRightSide = Math.cos(midAngle) > 0;

        // 计算初始标签位置
        const lineLength = 40;
        const initialY = centerY + Math.sin(midAngle) * (radius + lineLength);

        labelPositions.push({
            label,
            percentage,
            midAngle,
            isRightSide,
            color: chartData.actualColors[index],
            initialY: initialY,
            finalY: initialY // 初始时等于初始位置
        });

        currentAngle += sliceAngle;
    });

    // 分离左右两侧标签并排序
    const leftLabels = labelPositions.filter(pos => !pos.isRightSide).sort((a, b) => a.initialY - b.initialY);
    const rightLabels = labelPositions.filter(pos => pos.isRightSide).sort((a, b) => a.initialY - b.initialY);

    // 防碰撞算法 - 调整标签位置
    const adjustLabelsToAvoidCollision = (labels, minSpacing = 25) => {
        if (labels.length <= 1) return;

        // 从上到下调整
        for (let i = 1; i < labels.length; i++) {
            const current = labels[i];
            const previous = labels[i - 1];

            if (current.finalY - previous.finalY < minSpacing) {
                current.finalY = previous.finalY + minSpacing;
            }
        }

        // 使用更宽松的边界，确保标签在padding区域内可见
        const maxY = chartArea.bottom - 10;
        const minY = chartArea.top + 10;

        for (let i = labels.length - 1; i >= 0; i--) {
            if (labels[i].finalY > maxY) {
                labels[i].finalY = maxY;
                // 向上推其他标签
                for (let j = i - 1; j >= 0; j--) {
                    if (labels[j].finalY > labels[j + 1].finalY - minSpacing) {
                        labels[j].finalY = labels[j + 1].finalY - minSpacing;
                    }
                }
            }
            if (labels[i].finalY < minY) {
                labels[i].finalY = minY;
                // 向下推其他标签
                for (let j = i + 1; j < labels.length; j++) {
                    if (labels[j].finalY < labels[j - 1].finalY + minSpacing) {
                        labels[j].finalY = labels[j - 1].finalY + minSpacing;
                    }
                }
            }
        }
    };

    adjustLabelsToAvoidCollision(leftLabels);
    adjustLabelsToAvoidCollision(rightLabels);

    // 绘制所有标签
    [...leftLabels, ...rightLabels].forEach((pos) => {
        // 计算饼图边缘点
        const pieEdgeX = centerX + Math.cos(pos.midAngle) * radius;
        const pieEdgeY = centerY + Math.sin(pos.midAngle) * radius;

        // 计算引导线延伸点 - 缩短引导线，让标签更靠近饼图
        const lineLength = 25;
        const lineEndX = centerX + Math.cos(pos.midAngle) * (radius + lineLength);

        // 绘制引导线 - 从饼图边缘到调整后的Y位置
        ctx.strokeStyle = pos.color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(pieEdgeX, pieEdgeY);
        ctx.lineTo(lineEndX, pos.finalY);

        // 绘制水平线段 - 缩短水平线段
        const horizontalLineLength = 15;
        const horizontalEndX = lineEndX + (pos.isRightSide ? horizontalLineLength : -horizontalLineLength);
        ctx.lineTo(horizontalEndX, pos.finalY);
        ctx.stroke();

        // 绘制标签背景和文字
        const labelText = `${pos.label} ${pos.percentage}%`;
        const textMetrics = ctx.measureText(labelText);
        const textWidth = textMetrics.width;
        const textHeight = 18;

        // 调整标签位置，确保在可见区域内
        let labelX;
        if (pos.isRightSide) {
            labelX = horizontalEndX + 3;
            // 确保右侧标签不超出chartArea边界
            if (labelX + textWidth + 6 > chartArea.right) {
                labelX = chartArea.right - textWidth - 6;
            }
        } else {
            labelX = horizontalEndX - textWidth - 3;
            // 确保左侧标签不超出chartArea边界
            if (labelX < chartArea.left) {
                labelX = chartArea.left;
            }
        }

        const bgY = pos.finalY - textHeight / 2;

        // 绘制标签背景
        ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
        ctx.fillRect(labelX - 2, bgY - 1, textWidth + 4, textHeight + 2);

        // 绘制标签边框
        ctx.strokeStyle = pos.color;
        ctx.lineWidth = 1.5;
        ctx.strokeRect(labelX - 2, bgY - 1, textWidth + 4, textHeight + 2);

        // 绘制标签文字
        ctx.fillStyle = '#1f2937';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(labelText, labelX, pos.finalY);
    });

    ctx.restore();
}

// 准备双层饼图数据
function prepareDoublePieData(data) {
    console.log('=== 准备双层饼图数据 ===');

    if (!data || data.length === 0) {
        console.log('没有数据，返回空数据结构');
        return {
            labels: [],
            actualData: [],
            targetData: [],
            actualColors: [],
            targetColors: []
        };
    }

    // 颜色配置
    const baseColors = [
        '#3498db', // 蓝色
        '#e74c3c', // 红色
        '#f39c12', // 橙色
        '#2ecc71', // 绿色
        '#9b59b6', // 紫色
        '#1abc9c', // 青色
        '#34495e', // 深灰
        '#e67e22', // 深橙
        '#95a5a6', // 灰色
        '#16a085'  // 深青
    ];

    const labels = [];
    const actualData = [];
    const targetData = [];
    const actualColors = [];
    const targetColors = [];

    data.forEach((item, index) => {
        // 优先使用销售员姓名，然后是产品名称，最后是其他字段
        const label = item.name || item.personnel || item.product || item.terminal || `项目${index + 1}`;
        const actual = parseFloat(item.actual || 0);
        const target = parseFloat(item.target || 0);

        if (actual > 0 || target > 0) {
            labels.push(label);
            actualData.push(actual);
            targetData.push(target);

            const baseColor = baseColors[index % baseColors.length];
            actualColors.push(baseColor);
            targetColors.push(adjustColorOpacity(baseColor, 0.6)); // 外圈颜色更透明
        }
    });

    console.log('处理结果:');
    console.log('- 标签:', labels);
    console.log('- 实际销售数据:', actualData);
    console.log('- 指标数据:', targetData);

    return {
        labels,
        actualData,
        targetData,
        actualColors,
        targetColors
    };
}

// 调整颜色透明度
function adjustColorOpacity(color, opacity) {
    // 将十六进制颜色转换为rgba
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

// 生成自定义图例
function generatePieChartLegend(chartData) {
    console.log('=== 生成饼图图例 ===');

    const legendContainer = document.getElementById('doublePieChartLegend');
    if (!legendContainer) {
        console.error('找不到图例容器');
        return;
    }

    // 为卡片图例添加样式类
    legendContainer.className = 'chart-legend-cards';

    let legendHtml = '';

    chartData.labels.forEach((label, index) => {
        const actualValue = chartData.actualData[index];
        const targetValue = chartData.targetData[index];
        const actualColor = chartData.actualColors[index];
        const targetColor = chartData.targetColors[index];

        // 计算百分比
        const actualTotal = chartData.actualData.reduce((a, b) => a + b, 0);
        const targetTotal = chartData.targetData.reduce((a, b) => a + b, 0);
        const actualPercentage = actualTotal > 0 ? ((actualValue / actualTotal) * 100).toFixed(1) : '0.0';
        const targetPercentage = targetTotal > 0 ? ((targetValue / targetTotal) * 100).toFixed(1) : '0.0';

        legendHtml += `
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(45deg, ${actualColor} 50%, ${targetColor} 50%);"></div>
                <div class="legend-label">${label}</div>
                <div class="legend-value">实际: ${formatCurrency(actualValue)} (${actualPercentage}%)</div>
                <div class="legend-percentage">指标: ${formatCurrency(targetValue)} (${targetPercentage}%)</div>
            </div>
        `;
    });

    legendContainer.innerHTML = legendHtml;
    console.log('图例生成完成');
}

// 显示销售员业绩占比图 - 所有用户可见
function showDoublePieChartForAdmin(analysisData) {
    console.log('=== 显示销售员业绩占比图 ===');

    const pieChartItem = document.getElementById('pieChartItem');
    if (!pieChartItem) {
        console.error('❌ 找不到饼图区域 #pieChartItem');
        return;
    }

    // 强制显示销售员业绩占比图
    pieChartItem.style.display = 'block';
    pieChartItem.style.visibility = 'visible';
    console.log('✅ 强制显示销售员业绩占比图容器');

    // 使用销售员数据而不是产品数据
    console.log('🔍 完整的分析数据:', analysisData);
    const personnelData = analysisData && analysisData.personnel ? analysisData.personnel : [];
    console.log('👥 销售员数据:', personnelData);
    console.log('👥 销售员数据长度:', personnelData.length);

    if (!personnelData || personnelData.length === 0) {
        console.log('没有销售员数据，隐藏饼图');
        pieChartItem.style.display = 'none';
        return;
    }

    // 过滤有效数据
    const validPersonnel = personnelData.filter(person => {
        const actual = parseFloat(person.actual || 0);
        const target = parseFloat(person.target || 0);
        return actual > 0 || target > 0;
    });

    if (validPersonnel.length === 0) {
        console.log('没有有效的销售员数据，隐藏饼图');
        pieChartItem.style.display = 'none';
        return;
    }

    console.log(`准备为 ${validPersonnel.length} 个销售员创建双层饼图`);

    // 创建双层饼图
    createDoublePieChart(validPersonnel);
}

// 隐藏双层饼图
function hideDoublePieChart() {
    const pieChartItem = document.getElementById('pieChartItem');
    if (pieChartItem) {
        pieChartItem.style.display = 'none';
    }

    // 销毁现有图表
    if (doublePieChart) {
        doublePieChart.destroy();
        doublePieChart = null;
    }

    console.log('双层饼图已隐藏');
}

// 响应式布局调整 - 不使用CSS缩放，避免鼠标事件位置错误
function adjustPieChartLayout() {
    const pieChartItem = document.getElementById('pieChartItem');
    const chartContainer = document.querySelector('.pie-chart-container-small');

    if (!pieChartItem || !chartContainer) return;

    // 小尺寸图表默认为纵向布局
    chartContainer.style.flexDirection = 'column';
    chartContainer.style.alignItems = 'center';

    console.log('饼图布局调整完成 - 使用原生响应式，无CSS缩放');
}

// 动态调整饼图容器尺寸
function adjustPieChartContainerSize() {
    const pieChartWrapper = document.querySelector('.pie-chart-wrapper');
    const canvas = document.getElementById('doublePieChart');

    if (!pieChartWrapper || !canvas) return;

    const containerWidth = pieChartWrapper.offsetWidth;
    const viewportWidth = window.innerWidth;

    console.log('调整饼图容器尺寸:', { containerWidth, viewportWidth });

    // 根据可用空间调整容器的最大宽度
    if (containerWidth > 0) {
        // 确保图例有足够空间
        const legendWidth = 350;
        const chartMinWidth = 300;
        const totalMinWidth = chartMinWidth + legendWidth + 40; // 40px for gaps and padding

        if (containerWidth < totalMinWidth) {
            // 空间不足时，改为垂直布局
            pieChartWrapper.style.flexDirection = 'column';
            pieChartWrapper.style.alignItems = 'center';
        } else {
            // 空间充足时，使用水平布局
            pieChartWrapper.style.flexDirection = 'row';
            pieChartWrapper.style.alignItems = 'center';
        }
    }
}

// 监听窗口大小变化
window.addEventListener('resize', function() {
    adjustPieChartLayout();
    adjustPieChartContainerSize();
    forceChartsLayout(); // 确保医院图表在屏幕尺寸变化时正确布局

    // 重新渲染产品销售占比图以适应新的屏幕尺寸
    if (productShareChart && productShareChart.data && productShareChart.data.datasets[0].data.length > 0) {
        // 获取当前图表数据
        const currentData = [];
        const labels = productShareChart.data.labels;
        const data = productShareChart.data.datasets[0].data;
        const colors = productShareChart.data.datasets[0].backgroundColor;

        for (let i = 0; i < labels.length; i++) {
            const total = data.reduce((sum, value) => sum + value, 0);
            const percentage = total > 0 ? ((data[i] / total) * 100).toFixed(1) : '0.0';
            currentData.push({
                name: labels[i],
                value: data[i],
                percentage: percentage
            });
        }

        // 重新创建图表以应用新的移动端设置
        setTimeout(() => {
            createProductShareChart(currentData.map((item, index) => ({
                name: item.name,
                actual: item.value
            })));
        }, 100);
    }
});

// 在页面加载完成后初始化折叠功能
document.addEventListener('DOMContentLoaded', function() {
    // 等待DOM完全加载后再初始化折叠区域
    setTimeout(initCollapsibleSections, 500);

    // 初始化饼图布局调整
    setTimeout(() => {
        adjustPieChartLayout();
        adjustPieChartContainerSize();
    }, 1000);

    // 强制确保非月份筛选器在页面加载时被禁用
    setTimeout(() => {
        console.log('DOMContentLoaded: 强制禁用非月份筛选器');
        const filterTypes = ['rep', 'regionProductLine', 'product', 'terminal', 'city', 'manager'];

        filterTypes.forEach(type => {
            const container = document.getElementById(`${type}MultiSelect`);
            const display = document.getElementById(`${type}Display`);

            if (container && display) {
                container.classList.add('disabled');
                display.style.opacity = '0.5';
                display.style.pointerEvents = 'none';

                const placeholder = display.querySelector('.placeholder');
                if (placeholder) {
                    placeholder.textContent = '请先选择月份';
                    placeholder.style.color = '#999';
                }

                console.log(`强制禁用 ${type} 筛选器`);
            }
        });
    }, 2000);

    // 初始化响应式布局
    adjustPieChartLayout();
    
    // 初始化图表数据标签控制器
    setTimeout(() => {
        if (window.chartDataLabelsController) {
            window.chartDataLabelsController.refreshButtons();
        }
    }, 1500); // 等待图表加载完成

    // 测试：强制显示销售员占比图容器
    setTimeout(() => {
        const pieChartItem = document.getElementById('pieChartItem');
        if (pieChartItem) {
            pieChartItem.style.display = 'block';
            pieChartItem.style.visibility = 'visible';
            console.log('🧪 测试：强制显示销售员占比图容器');
            console.log('🧪 容器当前样式:', {
                display: pieChartItem.style.display,
                visibility: pieChartItem.style.visibility,
                offsetWidth: pieChartItem.offsetWidth,
                offsetHeight: pieChartItem.offsetHeight
            });
        } else {
            console.error('🧪 测试：找不到销售员占比图容器');
        }
    }, 2000);
    
    // 数据类型切换按钮使用内联onclick事件处理器
});
