// 全局变量
let currentUser = null;
let isAdmin = false;
let charts = {};
let isLoading = false;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    checkAuth();
    initializeFilters();
    loadChartData();
});

// 检查用户认证
function checkAuth() {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    if (!token || !user) {
        window.location.href = 'login.html';
        return;
    }
    
    currentUser = JSON.parse(user);
    isAdmin = currentUser.role === 'admin';
    
    document.getElementById('userDisplay').textContent = `${currentUser.username} (${currentUser.role === 'admin' ? '管理员' : '用户'})`;
    
    // 如果是管理员，显示用户筛选器
    if (isAdmin) {
        document.getElementById('userFilterGroup').style.display = 'block';
        loadUsers();
    }
}

// 加载用户列表（仅管理员）
async function loadUsers() {
    try {
        const response = await fetch('/api/users', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const users = await response.json();
            const userFilter = document.getElementById('userFilter');
            userFilter.innerHTML = '<option value="">全部用户</option>';
            
            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = user.username;
                userFilter.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载用户列表失败:', error);
    }
}

// 初始化筛选器
function initializeFilters() {
    // 时间范围变化事件
    document.getElementById('timeRange').addEventListener('change', function() {
        const customDateRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customDateRange.style.display = 'block';
        } else {
            customDateRange.style.display = 'none';
        }
    });
    
    // 加载产品列表
    loadProducts();
}

// 加载产品列表
async function loadProducts() {
    try {
        const response = await fetch('/api/products', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });
        
        if (response.ok) {
            const products = await response.json();
            const productFilter = document.getElementById('productFilter');
            productFilter.innerHTML = '<option value="">全部产品</option>';
            
            products.forEach(product => {
                const option = document.createElement('option');
                option.value = product.product_code;
                option.textContent = `${product.product_code} - ${product.product_name || ''}`;
                productFilter.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载产品列表失败:', error);
    }
}

// 标签页切换
function showTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有标签按钮的活动状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 显示选中的标签页
    document.getElementById(tabName).classList.add('active');
    event.target.classList.add('active');
    
    // 重新渲染图表
    setTimeout(() => {
        renderChartsForTab(tabName);
    }, 100);
}

// 应用筛选器
function applyFilters() {
    if (isLoading) {
        showMessage('数据正在加载中，请稍候...', 'warning');
        return;
    }
    loadChartData();
}

// 获取筛选参数
function getFilterParams() {
    const params = new URLSearchParams();
    
    // 时间范围
    const timeRange = document.getElementById('timeRange').value;
    params.append('timeRange', timeRange);
    
    if (timeRange === 'custom') {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
    }
    
    // 产品筛选
    const selectedProducts = Array.from(document.getElementById('productFilter').selectedOptions)
        .map(option => option.value)
        .filter(value => value);
    if (selectedProducts.length > 0) {
        params.append('products', selectedProducts.join(','));
    }
    
    // 用户筛选（仅管理员）
    if (isAdmin) {
        const selectedUsers = Array.from(document.getElementById('userFilter').selectedOptions)
            .map(option => option.value)
            .filter(value => value);
        if (selectedUsers.length > 0) {
            params.append('users', selectedUsers.join(','));
        }
    }
    
    return params.toString();
}

// 加载图表数据
async function loadChartData() {
    if (isLoading) return;

    try {
        isLoading = true;
        showLoadingState();

        const filterParams = getFilterParams();
        console.log('正在加载图表数据，筛选参数:', filterParams);

        const response = await fetch(`/api/chart-data?${filterParams}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            console.log('图表数据加载成功:', data);

            // 验证数据
            if (validateChartData(data)) {
                hideLoadingState();
                renderAllCharts(data);
                showMessage('数据加载成功', 'success');
            } else {
                hideLoadingState();
                showNoDataState();
                showMessage('暂无数据，请尝试调整筛选条件', 'info');
            }
        } else {
            const errorData = await response.json();
            console.error('加载图表数据失败:', errorData);
            hideLoadingState();
            showErrorState();
            showMessage(`数据加载失败: ${errorData.error || '服务器错误'}`, 'error');
        }
    } catch (error) {
        console.error('加载图表数据失败:', error);
        hideLoadingState();
        showErrorState();
        showMessage('网络连接失败，请检查网络连接', 'error');
    } finally {
        isLoading = false;
    }
}

// 渲染所有图表
function renderAllCharts(data) {
    console.log('开始渲染图表，数据:', data);

    try {
        // 清除所有状态元素
        document.querySelectorAll('.chart-state').forEach(state => state.remove());

        // 显示所有canvas元素
        document.querySelectorAll('.chart-container canvas').forEach(canvas => {
            canvas.style.display = 'block';
        });

        renderOverviewCharts(data);
        renderAchievementCharts(data);
        renderComparisonCharts(data);
        renderTrendCharts(data);

        console.log('图表渲染完成');
    } catch (error) {
        console.error('渲染图表时发生错误:', error);
        showMessage('图表渲染失败', 'error');
    }
}

// 渲染总体情况图表
function renderOverviewCharts(data) {
    console.log('渲染总体情况图表:', data);

    // 总体完成情况环形图
    const overallData = data.overall || {};
    const completedValue = parseFloat(overallData.completed) || 0;
    const remainingValue = parseFloat(overallData.remaining) || 0;

    if (completedValue > 0 || remainingValue > 0) {
        renderDonutChart('overallChart', {
            labels: ['已完成', '未完成'],
            data: [completedValue, remainingValue],
            colors: ['#4CAF50', '#E0E0E0']
        });
    } else {
        renderEmptyChart('overallChart', '暂无完成情况数据');
    }

    // 同比情况环形图
    const yearOverYearData = data.yearOverYear || {};
    const currentValue = parseFloat(yearOverYearData.current) || 0;
    const previousValue = parseFloat(yearOverYearData.previous) || 0;

    if (currentValue > 0 || previousValue > 0) {
        renderDonutChart('yearOverYearChart', {
            labels: ['本期', '同期'],
            data: [currentValue, previousValue],
            colors: ['#2196F3', '#FFC107']
        });
    } else {
        renderEmptyChart('yearOverYearChart', '暂无同比数据');
    }

    // 占比情况饼图
    if (data.share && data.share.length > 0) {
        const validShare = data.share.filter(item => item.value > 0);
        if (validShare.length > 0) {
            renderPieChart('shareChart', {
                labels: validShare.map(item => item.name || '未知'),
                data: validShare.map(item => parseFloat(item.value) || 0),
                colors: generateColors(validShare.length)
            });
        } else {
            renderEmptyChart('shareChart', '暂无占比数据');
        }
    } else {
        renderEmptyChart('shareChart', '暂无占比数据');
    }

    // 环比情况环形图
    const monthOverMonthData = data.monthOverMonth || {};
    const currentMonthValue = parseFloat(monthOverMonthData.current) || 0;
    const previousMonthValue = parseFloat(monthOverMonthData.previous) || 0;

    if (currentMonthValue > 0 || previousMonthValue > 0) {
        renderDonutChart('monthOverMonthChart', {
            labels: ['本期', '上期'],
            data: [currentMonthValue, previousMonthValue],
            colors: ['#FF5722', '#9C27B0']
        });
    } else {
        renderEmptyChart('monthOverMonthChart', '暂无环比数据');
    }
}

// 退出登录
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = 'login.html';
}

// 渲染达成情况图表
function renderAchievementCharts(data) {
    // 销售目标达成情况柱状图
    if (data.achievement && data.achievement.length > 0) {
        renderBarChart('achievementChart', {
            labels: data.achievement.map(item => item.name),
            datasets: [{
                label: '实际销售',
                data: data.achievement.map(item => item.actual),
                backgroundColor: '#2196F3'
            }, {
                label: '销售目标',
                data: data.achievement.map(item => item.target),
                backgroundColor: '#FFC107'
            }]
        });
    }

    // 达成率分布饼图
    if (data.achievementDistribution && data.achievementDistribution.length > 0) {
        renderPieChart('achievementDistributionChart', {
            labels: data.achievementDistribution.map(item => item.range),
            data: data.achievementDistribution.map(item => item.count),
            colors: ['#4CAF50', '#FFC107', '#FF5722', '#9C27B0']
        });
    }

    // 目标vs实际环形图
    renderDonutChart('targetVsActualChart', {
        labels: ['已达成', '未达成'],
        data: [data.targetVsActual.achieved || 0, data.targetVsActual.remaining || 0],
        colors: ['#4CAF50', '#E0E0E0']
    });
}

// 渲染对比分析图表
function renderComparisonCharts(data) {
    // 业绩对比柱状图
    if (data.performanceComparison && data.performanceComparison.length > 0) {
        renderBarChart('performanceComparisonChart', {
            labels: data.performanceComparison.map(item => item.period),
            datasets: [{
                label: '本期业绩',
                data: data.performanceComparison.map(item => item.current),
                backgroundColor: '#2196F3'
            }, {
                label: '上期业绩',
                data: data.performanceComparison.map(item => item.previous),
                backgroundColor: '#FFC107'
            }]
        });
    }

    // 业绩同比柱状图
    if (data.yearComparison && data.yearComparison.length > 0) {
        renderBarChart('yearComparisonChart', {
            labels: data.yearComparison.map(item => item.month),
            datasets: [{
                label: '本年',
                data: data.yearComparison.map(item => item.currentYear),
                backgroundColor: '#4CAF50'
            }, {
                label: '去年',
                data: data.yearComparison.map(item => item.previousYear),
                backgroundColor: '#FF5722'
            }]
        });
    }

    // 产品对比饼图
    if (data.productComparison && data.productComparison.length > 0) {
        renderPieChart('productComparisonChart', {
            labels: data.productComparison.map(item => item.product),
            data: data.productComparison.map(item => item.sales),
            colors: generateColors(data.productComparison.length)
        });
    }
}

// 渲染趋势分析图表
function renderTrendCharts(data) {
    // 销售趋势线图
    if (data.salesTrend && data.salesTrend.length > 0) {
        renderLineChart('salesTrendChart', {
            labels: data.salesTrend.map(item => item.period),
            datasets: [{
                label: '销售额',
                data: data.salesTrend.map(item => item.sales),
                borderColor: '#2196F3',
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
                fill: true
            }, {
                label: '目标',
                data: data.salesTrend.map(item => item.target),
                borderColor: '#FFC107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                fill: false
            }]
        });
    }

    // 达成率趋势线图
    if (data.achievementTrend && data.achievementTrend.length > 0) {
        renderLineChart('achievementTrendChart', {
            labels: data.achievementTrend.map(item => item.period),
            datasets: [{
                label: '达成率 (%)',
                data: data.achievementTrend.map(item => item.rate),
                borderColor: '#4CAF50',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                fill: true
            }]
        });
    }
}

// 渲染空图表
function renderEmptyChart(canvasId, message) {
    const canvas = document.getElementById(canvasId);
    const container = canvas.parentElement;

    // 隐藏canvas
    canvas.style.display = 'none';

    // 移除现有状态
    const existingState = container.querySelector('.chart-state');
    if (existingState) {
        existingState.remove();
    }

    // 添加空数据状态
    const emptyDiv = document.createElement('div');
    emptyDiv.className = 'chart-state no-data-state';
    emptyDiv.innerHTML = `
        <div class="no-data-icon">📊</div>
        <p>${message}</p>
    `;
    container.appendChild(emptyDiv);
}

// 渲染环形图
function renderDonutChart(canvasId, config) {
    const canvas = document.getElementById(canvasId);
    const ctx = canvas.getContext('2d');

    // 显示canvas
    canvas.style.display = 'block';

    // 移除状态元素
    const container = canvas.parentElement;
    const existingState = container.querySelector('.chart-state');
    if (existingState) {
        existingState.remove();
    }

    // 销毁现有图表
    if (charts[canvasId]) {
        charts[canvasId].destroy();
    }

    // 确保canvas是正方形，保证环形图完美圆形
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    // 取较小的尺寸作为canvas的尺寸，确保是正方形
    const size = Math.min(containerWidth, containerHeight, 400);
    canvas.width = size;
    canvas.height = size;
    canvas.style.width = size + 'px';
    canvas.style.height = size + 'px';

    charts[canvasId] = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: config.labels,
            datasets: [{
                data: config.data,
                backgroundColor: config.colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: false, // 禁用响应式，使用固定尺寸
            maintainAspectRatio: true, // 保持宽高比
            aspectRatio: 1, // 1:1 宽高比，确保圆形
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        boxHeight: 12,
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    },
                    maxWidth: 600,
                    maxHeight: 60
                }
            }
        }
    });
}

// 渲染饼图
function renderPieChart(canvasId, config) {
    const canvas = document.getElementById(canvasId);
    const ctx = canvas.getContext('2d');

    // 显示canvas
    canvas.style.display = 'block';

    // 移除状态元素
    const container = canvas.parentElement;
    const existingState = container.querySelector('.chart-state');
    if (existingState) {
        existingState.remove();
    }

    // 销毁现有图表
    if (charts[canvasId]) {
        charts[canvasId].destroy();
    }

    // 确保canvas是正方形，保证饼图完美圆形
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    // 取较小的尺寸作为canvas的尺寸，确保是正方形
    const size = Math.min(containerWidth, containerHeight, 400);
    canvas.width = size;
    canvas.height = size;
    canvas.style.width = size + 'px';
    canvas.style.height = size + 'px';

    charts[canvasId] = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: config.labels,
            datasets: [{
                data: config.data,
                backgroundColor: config.colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: false, // 禁用响应式，使用固定尺寸
            maintainAspectRatio: true, // 保持宽高比
            aspectRatio: 1, // 1:1 宽高比，确保圆形
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        boxWidth: 12,
                        boxHeight: 12,
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    },
                    maxWidth: 600,
                    maxHeight: 60
                }
            }
        }
    });
}

// 渲染柱状图
function renderBarChart(canvasId, config) {
    const canvas = document.getElementById(canvasId);
    const ctx = canvas.getContext('2d');

    // 显示canvas
    canvas.style.display = 'block';

    // 移除状态元素
    const container = canvas.parentElement;
    const existingState = container.querySelector('.chart-state');
    if (existingState) {
        existingState.remove();
    }

    // 销毁现有图表
    if (charts[canvasId]) {
        charts[canvasId].destroy();
    }

    charts[canvasId] = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: config.labels,
            datasets: config.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// 渲染线图
function renderLineChart(canvasId, config) {
    const canvas = document.getElementById(canvasId);
    const ctx = canvas.getContext('2d');

    // 显示canvas
    canvas.style.display = 'block';

    // 移除状态元素
    const container = canvas.parentElement;
    const existingState = container.querySelector('.chart-state');
    if (existingState) {
        existingState.remove();
    }

    // 销毁现有图表
    if (charts[canvasId]) {
        charts[canvasId].destroy();
    }

    charts[canvasId] = new Chart(ctx, {
        type: 'line',
        data: {
            labels: config.labels,
            datasets: config.datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
}

// 为特定标签页渲染图表
function renderChartsForTab(tabName) {
    // 重新调整图表大小
    Object.values(charts).forEach(chart => {
        if (chart) {
            chart.resize();
        }
    });
}

// 显示加载状态
function showLoadingState() {
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        const canvas = container.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 移除现有的状态元素
        const existingState = container.querySelector('.chart-state');
        if (existingState) {
            existingState.remove();
        }

        // 添加加载状态
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'chart-state loading-state';
        loadingDiv.innerHTML = `
            <div class="loading-spinner"></div>
            <p>加载中...</p>
        `;
        container.appendChild(loadingDiv);
    });
}

// 隐藏加载状态
function hideLoadingState() {
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        const canvas = container.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'block';
        }

        const loadingState = container.querySelector('.loading-state');
        if (loadingState) {
            loadingState.remove();
        }
    });
}

// 显示无数据状态
function showNoDataState() {
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        const canvas = container.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 移除现有的状态元素
        const existingState = container.querySelector('.chart-state');
        if (existingState) {
            existingState.remove();
        }

        // 添加无数据状态
        const noDataDiv = document.createElement('div');
        noDataDiv.className = 'chart-state no-data-state';
        noDataDiv.innerHTML = `
            <div class="no-data-icon">📊</div>
            <p>暂无数据</p>
            <small>请尝试调整筛选条件</small>
        `;
        container.appendChild(noDataDiv);
    });
}

// 显示错误状态
function showErrorState() {
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        const canvas = container.querySelector('canvas');
        if (canvas) {
            canvas.style.display = 'none';
        }

        // 移除现有的状态元素
        const existingState = container.querySelector('.chart-state');
        if (existingState) {
            existingState.remove();
        }

        // 添加错误状态
        const errorDiv = document.createElement('div');
        errorDiv.className = 'chart-state error-state';
        errorDiv.innerHTML = `
            <div class="error-icon">⚠️</div>
            <p>数据加载失败</p>
            <button onclick="loadChartData()" class="retry-btn">重试</button>
        `;
        container.appendChild(errorDiv);
    });
}

// 验证图表数据
function validateChartData(data) {
    if (!data || typeof data !== 'object') {
        console.log('数据验证失败: 数据为空或格式错误');
        return false;
    }

    // 检查是否有任何有效数据
    const hasValidData =
        (data.overall && (data.overall.completed > 0 || data.overall.remaining > 0)) ||
        (data.share && data.share.length > 0) ||
        (data.achievement && data.achievement.length > 0) ||
        (data.salesTrend && data.salesTrend.length > 0) ||
        (data.productComparison && data.productComparison.length > 0);

    if (!hasValidData) {
        console.log('数据验证失败: 没有有效的图表数据');
        return false;
    }

    console.log('数据验证成功');
    return true;
}

// 显示消息提示
function showMessage(message, type = 'info') {
    // 移除现有消息
    const existingMessage = document.querySelector('.message-toast');
    if (existingMessage) {
        existingMessage.remove();
    }

    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast message-${type}`;
    messageDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 显示动画
    setTimeout(() => {
        messageDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// 生成颜色数组
function generateColors(count) {
    const colors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
        '#4BC0C0', '#FF6384', '#36A2EB', '#FFCE56'
    ];
    return colors.slice(0, count);
}
